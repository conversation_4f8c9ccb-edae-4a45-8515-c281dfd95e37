#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析大庆测井数据中五个变量都比较起伏的深度段
找出最适合可视化的深度区间
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec

def calculate_fluctuation_metrics(data, window_size=50):
    """
    计算数据的起伏程度指标
    
    Parameters:
    - data: 数据数组
    - window_size: 滑动窗口大小
    
    Returns:
    - std_ratio: 标准差与均值的比值（变异系数）
    - range_ratio: 极差与均值的比值
    - gradient_std: 梯度的标准差（变化率的起伏程度）
    """
    if len(data) < 2:
        return 0, 0, 0
    
    # 变异系数（标准差/均值）
    std_ratio = np.std(data) / np.mean(data) if np.mean(data) != 0 else 0
    
    # 极差比（最大值-最小值）/均值
    range_ratio = (np.max(data) - np.min(data)) / np.mean(data) if np.mean(data) != 0 else 0
    
    # 梯度的标准差（变化率的起伏程度）
    gradients = np.abs(np.diff(data))
    gradient_std = np.std(gradients)
    
    return std_ratio, range_ratio, gradient_std

def analyze_depth_segments():
    """分析不同深度段的起伏程度"""
    print("📖 开始分析大庆测井数据的起伏程度...")
    
    # 加载原始数据
    data_path = r"C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_with_lithology.csv"
    try:
        df = pd.read_csv(data_path, encoding='utf-8')
        print(f"✅ 成功加载数据: {data_path}")
        print(f"📊 数据形状: {df.shape}")
        print(f"🎯 深度范围: {df['深度'].min():.1f} - {df['深度'].max():.1f} m")
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return
    
    # 五个关键测井参数的列名映射
    curve_columns = {
        'GR': '自然伽马',
        'AC': '声波时差', 
        'CNL': '补偿中子',
        'DEN': '岩性密度',
        'PE': None  # 数据中可能没有PE
    }
    
    # 检查哪些曲线可用
    available_curves = {}
    for key, col_name in curve_columns.items():
        if col_name and col_name in df.columns:
            available_curves[key] = col_name
            print(f"✅ 找到曲线: {key} -> {col_name}")
        else:
            print(f"❌ 未找到曲线: {key}")
    
    if len(available_curves) < 3:
        print("❌ 可用曲线太少，无法进行分析")
        return
    
    # 分析不同深度段的起伏程度
    depth = df['深度'].values
    segment_size = 100  # 每段100个数据点
    overlap = 50       # 重叠50个点
    
    results = []
    
    for start_idx in range(0, len(df) - segment_size, overlap):
        end_idx = start_idx + segment_size
        segment_data = df.iloc[start_idx:end_idx]
        
        depth_start = segment_data['深度'].iloc[0]
        depth_end = segment_data['深度'].iloc[-1]
        
        # 计算每个曲线的起伏指标
        fluctuation_scores = {}
        total_score = 0
        valid_curves = 0
        
        for curve_key, col_name in available_curves.items():
            data = segment_data[col_name].dropna().values
            if len(data) > 10:  # 确保有足够的数据点
                std_ratio, range_ratio, gradient_std = calculate_fluctuation_metrics(data)
                # 综合评分（权重可调整）
                score = std_ratio * 0.4 + range_ratio * 0.3 + gradient_std * 0.3
                fluctuation_scores[curve_key] = {
                    'std_ratio': std_ratio,
                    'range_ratio': range_ratio, 
                    'gradient_std': gradient_std,
                    'score': score
                }
                total_score += score
                valid_curves += 1
        
        if valid_curves > 0:
            avg_score = total_score / valid_curves
            results.append({
                'depth_start': depth_start,
                'depth_end': depth_end,
                'avg_fluctuation_score': avg_score,
                'valid_curves': valid_curves,
                'fluctuation_details': fluctuation_scores
            })
    
    # 按起伏程度排序
    results.sort(key=lambda x: x['avg_fluctuation_score'], reverse=True)
    
    print("\n🏆 起伏程度最高的深度段（前10名）:")
    print("=" * 80)
    for i, result in enumerate(results[:10]):
        print(f"第{i+1}名: {result['depth_start']:.1f} - {result['depth_end']:.1f} m")
        print(f"  综合起伏评分: {result['avg_fluctuation_score']:.4f}")
        print(f"  有效曲线数: {result['valid_curves']}")
        
        # 显示各曲线的详细评分
        for curve, details in result['fluctuation_details'].items():
            print(f"    {curve}: 变异系数={details['std_ratio']:.3f}, "
                  f"极差比={details['range_ratio']:.3f}, "
                  f"梯度标准差={details['gradient_std']:.3f}, "
                  f"综合评分={details['score']:.4f}")
        print()
    
    # 推荐最佳可视化区间
    best_segment = results[0]
    print(f"🎯 推荐的最佳可视化深度区间:")
    print(f"   深度: {best_segment['depth_start']:.1f} - {best_segment['depth_end']:.1f} m")
    print(f"   综合起伏评分: {best_segment['avg_fluctuation_score']:.4f}")
    
    return results

def visualize_top_segments(results, top_n=3):
    """可视化起伏程度最高的几个深度段"""
    print(f"\n📊 可视化前{top_n}个起伏程度最高的深度段...")
    
    # 重新加载数据用于可视化
    data_path = r"C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_with_lithology.csv"
    df = pd.read_csv(data_path, encoding='utf-8')
    
    curve_columns = {
        'GR': '自然伽马',
        'AC': '声波时差', 
        'CNL': '补偿中子',
        'DEN': '岩性密度'
    }
    
    fig, axes = plt.subplots(top_n, len(curve_columns), figsize=(16, 4*top_n))
    if top_n == 1:
        axes = axes.reshape(1, -1)
    
    for i, result in enumerate(results[:top_n]):
        # 获取该深度段的数据
        mask = (df['深度'] >= result['depth_start']) & (df['深度'] <= result['depth_end'])
        segment_data = df[mask]
        
        depth_vals = segment_data['深度'].values
        
        for j, (curve_key, col_name) in enumerate(curve_columns.items()):
            ax = axes[i, j]
            
            if col_name in df.columns:
                curve_data = segment_data[col_name].values
                ax.plot(curve_data, depth_vals, 'b-', linewidth=1.5)
                ax.set_title(f'{curve_key}\n({result["depth_start"]:.1f}-{result["depth_end"]:.1f}m)')
                ax.set_xlabel(curve_key)
                if j == 0:
                    ax.set_ylabel('深度 (m)')
                ax.invert_yaxis()
                ax.grid(True, alpha=0.3)
            else:
                ax.text(0.5, 0.5, f'{curve_key}\n数据不可用', 
                       ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{curve_key} (无数据)')
    
    plt.tight_layout()
    plt.savefig('top_fluctuation_segments.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 可视化结果已保存为 'top_fluctuation_segments.png'")

if __name__ == "__main__":
    # 分析起伏程度
    results = analyze_depth_segments()
    
    if results:
        # 可视化前3个最起伏的深度段
        visualize_top_segments(results, top_n=3)
