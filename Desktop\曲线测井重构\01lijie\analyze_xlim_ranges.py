#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析50m深度段的测井曲线数据范围，优化横坐标设置
"""

import csv
import math

def analyze_curve_ranges():
    """分析测井曲线的数据范围，为横坐标设置提供建议"""
    print("📊 分析50m深度段的测井曲线数据范围...")
    
    # 读取CSV文件
    data_path = r"C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_with_lithology.csv"
    
    try:
        with open(data_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            data = list(reader)
        
        print(f"✅ 成功加载数据: {len(data)} 行")
        
        # 筛选50m深度段数据
        target_start = 1955.8
        target_end = 2005.8
        
        target_data = [row for row in data if target_start <= float(row['深度']) <= target_end]
        print(f"🎯 目标深度段 ({target_start}-{target_end}m) 数据点数: {len(target_data)}")
        
        # 分析四个主要测井曲线
        curves = {
            'GR': '自然伽马',
            'AC': '声波时差', 
            'CNL': '补偿中子',
            'DEN': '岩性密度'
        }
        
        print("\n📈 各曲线数据范围分析:")
        print("=" * 80)
        
        optimized_ranges = {}
        
        for curve_key, col_name in curves.items():
            if col_name in target_data[0]:
                # 提取数值数据
                values = []
                for row in target_data:
                    try:
                        val = float(row[col_name])
                        values.append(val)
                    except:
                        continue
                
                if values:
                    min_val = min(values)
                    max_val = max(values)
                    mean_val = sum(values) / len(values)
                    
                    # 计算标准差
                    variance = sum((x - mean_val) ** 2 for x in values) / len(values)
                    std_val = math.sqrt(variance)
                    
                    # 计算合适的显示范围（均值 ± 2.5倍标准差，但不超过实际范围）
                    range_start = max(min_val, mean_val - 2.5 * std_val)
                    range_end = min(max_val, mean_val + 2.5 * std_val)
                    
                    # 为了美观，稍微扩展范围
                    range_span = range_end - range_start
                    margin = range_span * 0.1  # 10%的边距
                    
                    suggested_start = range_start - margin
                    suggested_end = range_end + margin
                    
                    # 确保不超出实际数据范围
                    suggested_start = max(min_val * 0.95, suggested_start)
                    suggested_end = min(max_val * 1.05, suggested_end)
                    
                    optimized_ranges[curve_key] = (suggested_start, suggested_end)
                    
                    print(f"{curve_key} ({col_name}):")
                    print(f"  实际范围: {min_val:.3f} - {max_val:.3f}")
                    print(f"  均值: {mean_val:.3f}, 标准差: {std_val:.3f}")
                    print(f"  建议横坐标范围: {suggested_start:.3f} - {suggested_end:.3f}")
                    print(f"  范围跨度: {suggested_end - suggested_start:.3f}")
                    print()
        
        # 生成代码建议
        print("🔧 建议的代码修改:")
        print("=" * 50)
        for curve_key, (start, end) in optimized_ranges.items():
            if curve_key == 'CNL':
                print(f"curve_info.append(('{curve_key}', curve_mapping['{curve_key}'], ({start:.3f}, {end:.3f})))")
            else:
                print(f"curve_info.append(('{curve_key}', curve_mapping['{curve_key}'], ({start:.1f}, {end:.1f})))")
        
        # 检查是否有重叠问题
        print("\n🔍 重叠检查:")
        print("=" * 30)
        ranges_list = list(optimized_ranges.values())
        for i, (curve1, range1) in enumerate(optimized_ranges.items()):
            for j, (curve2, range2) in enumerate(optimized_ranges.items()):
                if i < j:
                    # 检查数值范围是否有重叠
                    overlap = not (range1[1] < range2[0] or range2[1] < range1[0])
                    if overlap:
                        print(f"⚠️  {curve1} 和 {curve2} 的数值范围可能重叠")
                    else:
                        print(f"✅ {curve1} 和 {curve2} 的数值范围分离良好")
        
        return optimized_ranges
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

if __name__ == "__main__":
    analyze_curve_ranges()
