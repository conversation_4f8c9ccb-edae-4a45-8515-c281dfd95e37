#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标轴含义解释示意图
专门解释GIAT热力图中横轴和竖轴的含义
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec
import matplotlib.patches as patches

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_axis_explanation():
    """创建坐标轴解释示意图"""
    
    fig = plt.figure(figsize=(15, 10))
    gs = GridSpec(3, 2, figure=fig, height_ratios=[1, 2, 1], hspace=0.3, wspace=0.2)
    
    # 主标题
    fig.suptitle('GIAT热力图坐标轴含义详解', fontsize=16, weight='bold')
    
    # 1. 概念解释图
    ax1 = fig.add_subplot(gs[0, :])
    ax1.text(0.5, 0.8, '热力图 = 注意力权重在时间-深度二维空间的分布', 
             ha='center', va='center', fontsize=14, weight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.3))
    
    ax1.text(0.5, 0.4, '每个像素点(t, d)表示：在时间t时，模型对深度d位置的关注程度', 
             ha='center', va='center', fontsize=12,
             bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.5))
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(0, 1)
    ax1.axis('off')
    
    # 2. 示例热力图
    ax2 = fig.add_subplot(gs[1, 0])
    
    # 创建示例数据
    time_steps = 20
    depth_points = 15
    time_axis = np.linspace(0, 10, time_steps)
    depth_axis = np.linspace(1750, 1760, depth_points)
    
    # 模拟热力图数据
    np.random.seed(42)
    heatmap_data = np.random.rand(depth_points, time_steps)
    
    # 在特定位置添加高激活（模拟地质边界）
    heatmap_data[5:7, :] *= 2  # 地质边界1
    heatmap_data[10:12, :] *= 1.8  # 地质边界2
    
    # 归一化
    heatmap_data = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())
    
    # 绘制热力图
    im = ax2.imshow(heatmap_data, cmap='viridis', aspect='auto', 
                   extent=[time_axis[0], time_axis[-1], depth_axis[-1], depth_axis[0]])
    
    ax2.set_xlabel('时间 (秒)', fontsize=12, weight='bold')
    ax2.set_ylabel('深度 (米)', fontsize=12, weight='bold')
    ax2.set_title('示例：GIAT注意力热力图', fontsize=12, weight='bold')
    
    # 添加标注
    ax2.annotate('高注意力区域\n(地质边界)', xy=(5, 1753), xytext=(7, 1751),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=10, color='red', weight='bold')
    
    ax2.annotate('低注意力区域\n(均质岩层)', xy=(3, 1757), xytext=(1, 1759),
                arrowprops=dict(arrowstyle='->', color='blue', lw=2),
                fontsize=10, color='blue', weight='bold')
    
    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax2, shrink=0.8)
    cbar.set_label('注意力强度', rotation=270, labelpad=15)
    
    # 3. 详细解释
    ax3 = fig.add_subplot(gs[1, 1])
    ax3.axis('off')
    
    explanation_text = """
坐标轴详细解释：

横轴 - 时间维度 (0-10秒)：
• 模型处理数据的时间步长
• 注意力机制的动态演化过程
• 在实际应用中可对应：
  - 钻井进程时间
  - 数据采集时间窗口
  - 特征提取的时间序列

竖轴 - 深度维度 (1750-1760米)：
• 真实的井深位置
• 对应不同的地质层位
• 来自大庆油田实际测井数据
• 每个深度点代表特定岩性

热力图数值含义：
• 颜色深浅 = 注意力权重大小
• 深色区域 = 模型认为重要的位置
• 浅色区域 = 模型认为不重要的位置
• 条纹模式 = 注意力的时空分布特征

实际应用意义：
• 帮助地质学家理解模型决策
• 识别关键的地质特征位置
• 验证模型的地质合理性
• 指导钻井和完井决策
    """
    
    ax3.text(0.05, 0.95, explanation_text, transform=ax3.transAxes,
             fontsize=10, va='top', ha='left',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.1))
    
    # 4. 底部总结
    ax4 = fig.add_subplot(gs[2, :])
    ax4.axis('off')
    
    summary_text = """
关键理解：热力图不是传统的地质剖面图，而是模型注意力在时间-深度空间的分布图
• 横轴时间：体现了深度学习模型的序列处理特性
• 竖轴深度：保持了地质数据的空间物理意义
• 热力图值：反映了模型对不同时空位置的关注程度
    """
    
    ax4.text(0.5, 0.5, summary_text, transform=ax4.transAxes,
             fontsize=12, ha='center', va='center', weight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='orange', alpha=0.2))
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("🎨 创建坐标轴解释示意图...")
    
    try:
        fig = create_axis_explanation()
        
        # 保存图片
        output_path = 'axis_explanation_demo.png'
        fig.savefig(output_path, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        
        print("✅ 示意图创建完成！")
        print(f"📁 保存路径: {output_path}")
        print("🎯 这个图解释了：")
        print("   ✅ 横轴（时间）的含义和作用")
        print("   ✅ 竖轴（深度）的物理意义")
        print("   ✅ 热力图数值的解释")
        print("   ✅ 实际应用的意义")
        
        plt.show()
        
    except Exception as e:
        print(f"❌ 创建失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 坐标轴解释示意图创建成功！")
    else:
        print("\n❌ 坐标轴解释示意图创建失败！")
