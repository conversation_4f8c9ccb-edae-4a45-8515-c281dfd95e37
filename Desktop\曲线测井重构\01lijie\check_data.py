import pandas as pd
import numpy as np

# 加载数据
data = pd.read_csv(r'C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_lithology_corrected.csv')

print('=== 完整数据岩性分布 ===')
print('Final_Lithology 分布:')
print(data['Final_Lithology'].value_counts())

print('\n=== 不同深度区间的岩性分布 ===')
for start in range(0, 2000, 500):
    end = min(start + 500, len(data))
    subset = data.iloc[start:end]
    print(f'区间 {start}-{end} (深度 {subset["Depth"].iloc[0]:.1f}-{subset["Depth"].iloc[-1]:.1f}m):')
    print(subset['Final_Lithology'].value_counts().to_dict())
    print()

print('=== 寻找岩性变化丰富的区间 ===')
# 寻找包含所有三种岩性的区间
for start in range(0, len(data)-200, 100):
    end = min(start + 200, len(data))
    subset = data.iloc[start:end]
    unique_lithologies = subset['Final_Lithology'].unique()
    if len(unique_lithologies) >= 3:
        print(f'区间 {start}-{end} (深度 {subset["Depth"].iloc[0]:.1f}-{subset["Depth"].iloc[-1]:.1f}m):')
        print(f'包含岩性: {list(unique_lithologies)}')
        print(subset['Final_Lithology'].value_counts().to_dict())
        print()
        if start > 1000:  # 只显示前几个
            break
