#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
岩性数据修正脚本
基于地质学原理，对现有数据进行合理的岩性重新划分
创建连续的岩性段，符合地质实际情况

作者: 李杰
日期: 2025-01-14
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def load_original_data():
    """加载原始数据"""
    try:
        # 加载daqin数据
        data_path = r'C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_five_params_DRSN_style.csv'
        data = pd.read_csv(data_path)
        print(f"✅ 成功加载数据，共 {len(data)} 个数据点")
        print(f"📊 深度范围: {data['Depth'].min():.1f} - {data['Depth'].max():.1f} m")
        
        # 显示原始岩性分布
        lithology_counts = data['Lithology'].value_counts()
        print(f"🏗️ 原始岩性分布:")
        for litho, count in lithology_counts.items():
            print(f"   {litho}: {count} 个点")
        
        return data
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def analyze_logging_characteristics(data):
    """分析测井曲线特征，为岩性重新划分提供依据"""
    print("\n🔍 分析测井曲线特征...")
    
    # 关键测井参数统计
    key_params = ['GR', 'AC', 'CNL', 'DEN', 'PE']
    
    print("📈 各测井参数统计:")
    for param in key_params:
        if param in data.columns:
            mean_val = data[param].mean()
            std_val = data[param].std()
            print(f"   {param}: 均值={mean_val:.2f}, 标准差={std_val:.2f}")
    
    return key_params

def create_geological_lithology_segments(data):
    """
    基于地质学原理创建合理的岩性段
    
    地质学原理：
    1. 岩性应该有连续的层段，不应频繁变化
    2. 基于测井响应特征进行岩性划分
    3. 考虑地质沉积环境的连续性
    """
    print("\n🏗️ 基于地质学原理重新划分岩性...")
    
    # 1. 特征选择和标准化
    feature_columns = ['GR', 'AC', 'CNL', 'DEN', 'PE']
    features = data[feature_columns].copy()
    
    # 数据标准化
    scaler = StandardScaler()
    features_scaled = scaler.fit_transform(features)
    
    # 2. 使用滑动窗口平滑，减少噪声影响
    window_size = 15  # 约2米的窗口（0.125m * 15 ≈ 2m）
    features_smoothed = np.zeros_like(features_scaled)

    # 使用numpy实现滑动窗口平滑
    for i in range(features_scaled.shape[1]):
        # 简单的滑动平均
        padded = np.pad(features_scaled[:, i], window_size//2, mode='edge')
        smoothed = np.convolve(padded, np.ones(window_size)/window_size, mode='valid')
        features_smoothed[:, i] = smoothed
    
    # 3. 基于地质学知识的岩性分类
    # 简化为3大类：泥岩、砂岩、碳酸盐岩
    n_clusters = 3
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
    cluster_labels = kmeans.fit_predict(features_smoothed)
    
    # 4. 基于测井响应特征解释聚类结果
    cluster_characteristics = {}
    for cluster_id in range(n_clusters):
        mask = cluster_labels == cluster_id
        cluster_data = data[mask]
        
        # 计算各聚类的测井特征均值
        gr_mean = cluster_data['GR'].mean()
        den_mean = cluster_data['DEN'].mean()
        pe_mean = cluster_data['PE'].mean()
        
        cluster_characteristics[cluster_id] = {
            'GR': gr_mean,
            'DEN': den_mean,
            'PE': pe_mean,
            'count': mask.sum()
        }
    
    # 5. 根据测井响应特征分配岩性名称
    lithology_mapping = assign_lithology_names(cluster_characteristics)
    
    # 6. 应用连续性约束，创建地质合理的岩性段
    corrected_labels = apply_geological_continuity(cluster_labels, min_segment_length=10)
    
    # 7. 映射到岩性名称
    corrected_lithology = [lithology_mapping[label] for label in corrected_labels]
    corrected_lithology_code = corrected_labels
    
    print(f"✅ 岩性重新划分完成")
    print(f"📊 新的岩性分布:")
    unique_labels, counts = np.unique(corrected_labels, return_counts=True)
    for label, count in zip(unique_labels, counts):
        litho_name = lithology_mapping[label]
        print(f"   {litho_name} (代码{label}): {count} 个点 ({count/len(data)*100:.1f}%)")
    
    return corrected_lithology, corrected_lithology_code, lithology_mapping

def assign_lithology_names(cluster_characteristics):
    """
    基于测井响应特征分配岩性名称
    
    地质学依据：
    - 高GR (>80) + 低DEN (<2.5) → 泥岩
    - 低GR (<60) + 中等DEN (2.5-2.7) + 低PE (<3.5) → 砂岩  
    - 低GR + 高DEN (>2.7) + 高PE (>4.0) → 碳酸盐岩
    """
    lithology_mapping = {}
    
    print("\n🔬 基于测井响应特征分配岩性:")
    
    for cluster_id, chars in cluster_characteristics.items():
        gr = chars['GR']
        den = chars['DEN']
        pe = chars['PE']
        count = chars['count']
        
        print(f"   聚类 {cluster_id}: GR={gr:.1f}, DEN={den:.2f}, PE={pe:.2f}, 点数={count}")
        
        # 岩性判别逻辑
        if gr > 90:  # 高伽马
            lithology_mapping[cluster_id] = "泥岩"
        elif gr < 70 and pe > 4.0:  # 低伽马 + 高光电因子
            lithology_mapping[cluster_id] = "碳酸盐岩"
        else:  # 中等伽马
            lithology_mapping[cluster_id] = "砂岩"
    
    return lithology_mapping

def apply_geological_continuity(labels, min_segment_length=10):
    """
    应用地质连续性约束
    
    原理：
    1. 消除过短的岩性段（<min_segment_length）
    2. 将短段合并到相邻的主要岩性段
    3. 保持地质层序的连续性
    """
    print(f"\n🔧 应用地质连续性约束 (最小段长: {min_segment_length} 个点)...")
    
    corrected_labels = labels.copy()
    
    # 识别和处理短段
    i = 0
    while i < len(corrected_labels):
        current_label = corrected_labels[i]
        
        # 找到当前段的结束位置
        j = i
        while j < len(corrected_labels) and corrected_labels[j] == current_label:
            j += 1
        
        segment_length = j - i
        
        # 如果段太短，合并到相邻段
        if segment_length < min_segment_length:
            # 选择合并目标：优先选择前面的段，如果没有则选择后面的段
            if i > 0:
                target_label = corrected_labels[i-1]
            elif j < len(corrected_labels):
                target_label = corrected_labels[j]
            else:
                target_label = current_label  # 保持不变
            
            # 执行合并
            corrected_labels[i:j] = target_label
            print(f"   合并短段: 位置 {i}-{j-1}, 长度 {segment_length} → 岩性 {target_label}")
        
        i = j
    
    return corrected_labels

def save_corrected_data(original_data, corrected_lithology, corrected_lithology_code):
    """保存修正后的数据"""
    print("\n💾 保存修正后的数据...")
    
    # 创建新的数据框
    corrected_data = original_data.copy()
    corrected_data['Lithology_Corrected'] = corrected_lithology
    corrected_data['Lithology_Code_Corrected'] = corrected_lithology_code
    
    # 保存到新文件
    output_path = r'C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_lithology_corrected.csv'
    corrected_data.to_csv(output_path, index=False)
    
    print(f"✅ 修正后的数据已保存到: {output_path}")
    
    return output_path

def visualize_comparison(data, corrected_lithology, corrected_lithology_code):
    """可视化原始vs修正后的岩性对比"""
    print("\n📊 生成对比可视化...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 8))
    
    # 原始岩性
    depth = data['Depth'].values
    original_codes = data['Lithology_Code'].values
    
    # 颜色映射
    colors = ['#8B4513', '#DAA520', '#228B22', '#4169E1', '#FF6347', '#9370DB']
    
    # 绘制原始岩性
    for i in range(len(depth)-1):
        color = colors[original_codes[i] % len(colors)]
        ax1.fill_betweenx([depth[i], depth[i+1]], 0, 1, color=color, alpha=0.7)
    
    ax1.set_ylabel('深度 (m)')
    ax1.set_title('原始岩性划分')
    ax1.invert_yaxis()
    ax1.set_xlim(0, 1)
    
    # 绘制修正后岩性
    for i in range(len(depth)-1):
        color = colors[corrected_lithology_code[i] % len(colors)]
        ax2.fill_betweenx([depth[i], depth[i+1]], 0, 1, color=color, alpha=0.7)
    
    ax2.set_ylabel('深度 (m)')
    ax2.set_title('修正后岩性划分')
    ax2.invert_yaxis()
    ax2.set_xlim(0, 1)
    
    plt.tight_layout()
    
    # 保存图片
    output_fig = r'C:\Users\<USER>\Desktop\曲线测井重构\01lijie\lithology_comparison.png'
    plt.savefig(output_fig, dpi=300, bbox_inches='tight')
    print(f"📈 对比图已保存到: {output_fig}")
    
    plt.show()

def main():
    """主函数"""
    print("🚀 开始岩性数据修正...")
    print("="*60)
    
    # 1. 加载原始数据
    data = load_original_data()
    if data is None:
        return
    
    # 2. 分析测井特征
    key_params = analyze_logging_characteristics(data)
    
    # 3. 重新划分岩性
    corrected_lithology, corrected_lithology_code, lithology_mapping = create_geological_lithology_segments(data)
    
    # 4. 保存修正后的数据
    output_path = save_corrected_data(data, corrected_lithology, corrected_lithology_code)
    
    # 5. 可视化对比
    visualize_comparison(data, corrected_lithology, corrected_lithology_code)
    
    print("\n" + "="*60)
    print("✅ 岩性数据修正完成！")
    print(f"📁 修正后的数据文件: {output_path}")
    print("\n🎯 修正原理:")
    print("   1. 基于测井响应特征进行K-means聚类")
    print("   2. 根据地质学知识分配岩性名称")
    print("   3. 应用连续性约束，消除不合理的短段")
    print("   4. 创建符合地质实际的连续岩性层")

if __name__ == "__main__":
    main()
