#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版本的起伏分析 - 不依赖numpy和matplotlib
分析大庆测井数据中五个变量都比较起伏的深度段
"""

import csv
import math

def calculate_statistics(data):
    """计算基本统计量"""
    if not data:
        return 0, 0, 0, 0, 0
    
    n = len(data)
    mean_val = sum(data) / n
    
    # 计算标准差
    variance = sum((x - mean_val) ** 2 for x in data) / n
    std_val = math.sqrt(variance)
    
    # 计算极差
    min_val = min(data)
    max_val = max(data)
    range_val = max_val - min_val
    
    # 计算变异系数
    cv = std_val / mean_val if mean_val != 0 else 0
    
    # 计算相邻点变化的平均值（起伏程度）
    if n > 1:
        changes = [abs(data[i+1] - data[i]) for i in range(n-1)]
        avg_change = sum(changes) / len(changes)
    else:
        avg_change = 0
    
    return mean_val, std_val, range_val, cv, avg_change

def analyze_fluctuation():
    """分析数据起伏程度"""
    print("📖 开始分析大庆测井数据的起伏程度...")
    
    # 读取CSV文件
    data_path = r"C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_with_lithology.csv"
    
    try:
        with open(data_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            data = list(reader)
        
        print(f"✅ 成功加载数据: {len(data)} 行")
        
        # 检查列名
        if data:
            print(f"📊 数据列名: {list(data[0].keys())}")
    
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return
    
    # 五个关键测井参数
    curve_columns = {
        'GR': '自然伽马',
        'AC': '声波时差', 
        'CNL': '补偿中子',
        'DEN': '岩性密度'
    }
    
    # 检查哪些曲线可用
    available_curves = {}
    for key, col_name in curve_columns.items():
        if col_name in data[0].keys():
            available_curves[key] = col_name
            print(f"✅ 找到曲线: {key} -> {col_name}")
        else:
            print(f"❌ 未找到曲线: {key}")
    
    if len(available_curves) < 3:
        print("❌ 可用曲线太少，无法进行分析")
        return
    
    # 分析不同深度段
    segment_size = 100  # 每段100个数据点
    overlap = 50       # 重叠50个点
    
    results = []
    
    for start_idx in range(0, len(data) - segment_size, overlap):
        end_idx = start_idx + segment_size
        segment = data[start_idx:end_idx]
        
        try:
            depth_start = float(segment[0]['深度'])
            depth_end = float(segment[-1]['深度'])
        except:
            continue
        
        # 计算每个曲线的起伏指标
        fluctuation_scores = {}
        total_score = 0
        valid_curves = 0
        
        for curve_key, col_name in available_curves.items():
            try:
                # 提取数值数据
                curve_data = []
                for row in segment:
                    try:
                        val = float(row[col_name])
                        curve_data.append(val)
                    except:
                        continue
                
                if len(curve_data) > 10:  # 确保有足够的数据点
                    mean_val, std_val, range_val, cv, avg_change = calculate_statistics(curve_data)
                    
                    # 综合评分（变异系数 + 平均变化率）
                    score = cv * 0.6 + (avg_change / mean_val if mean_val != 0 else 0) * 0.4
                    
                    fluctuation_scores[curve_key] = {
                        'mean': mean_val,
                        'std': std_val,
                        'range': range_val,
                        'cv': cv,
                        'avg_change': avg_change,
                        'score': score
                    }
                    total_score += score
                    valid_curves += 1
            
            except Exception as e:
                print(f"处理曲线 {curve_key} 时出错: {e}")
                continue
        
        if valid_curves > 0:
            avg_score = total_score / valid_curves
            results.append({
                'depth_start': depth_start,
                'depth_end': depth_end,
                'avg_fluctuation_score': avg_score,
                'valid_curves': valid_curves,
                'fluctuation_details': fluctuation_scores
            })
    
    # 按起伏程度排序
    results.sort(key=lambda x: x['avg_fluctuation_score'], reverse=True)
    
    print("\n🏆 起伏程度最高的深度段（前10名）:")
    print("=" * 80)
    
    for i, result in enumerate(results[:10]):
        print(f"第{i+1}名: {result['depth_start']:.1f} - {result['depth_end']:.1f} m")
        print(f"  综合起伏评分: {result['avg_fluctuation_score']:.4f}")
        print(f"  有效曲线数: {result['valid_curves']}")
        
        # 显示各曲线的详细评分
        for curve, details in result['fluctuation_details'].items():
            print(f"    {curve}: 均值={details['mean']:.2f}, "
                  f"标准差={details['std']:.3f}, "
                  f"变异系数={details['cv']:.3f}, "
                  f"平均变化={details['avg_change']:.3f}, "
                  f"评分={details['score']:.4f}")
        print()
    
    # 推荐最佳可视化区间
    if results:
        best_segment = results[0]
        print(f"🎯 推荐的最佳可视化深度区间:")
        print(f"   深度: {best_segment['depth_start']:.1f} - {best_segment['depth_end']:.1f} m")
        print(f"   综合起伏评分: {best_segment['avg_fluctuation_score']:.4f}")
        
        # 输出具体的深度范围供修改代码使用
        print(f"\n📝 建议在2_1.py中使用以下深度范围:")
        print(f"   depth_mask = (data['Depth'] >= {best_segment['depth_start']:.1f}) & (data['Depth'] <= {best_segment['depth_end']:.1f})")
        
        return results
    else:
        print("❌ 没有找到合适的深度段")
        return None

if __name__ == "__main__":
    analyze_fluctuation()
