#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版岩性数据修正脚本
基于地质学原理，对现有数据进行合理的岩性重新划分
创建连续的岩性段，符合地质实际情况

作者: 李杰
日期: 2025-01-14
"""

import pandas as pd
import numpy as np

def load_original_data():
    """加载原始数据"""
    try:
        # 加载daqin数据
        data_path = r'C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_five_params_DRSN_style.csv'
        data = pd.read_csv(data_path)
        print(f"✅ 成功加载数据，共 {len(data)} 个数据点")
        print(f"📊 深度范围: {data['Depth'].min():.1f} - {data['Depth'].max():.1f} m")
        
        # 显示原始岩性分布
        lithology_counts = data['Lithology'].value_counts()
        print(f"🏗️ 原始岩性分布:")
        for litho, count in lithology_counts.items():
            print(f"   {litho}: {count} 个点")
        
        return data
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def simple_lithology_classification(data):
    """
    基于测井响应的简单岩性分类
    
    地质学原理：
    - 高GR (>100) → 泥岩
    - 低GR (<70) + 高PE (>4.0) → 碳酸盐岩  
    - 其他 → 砂岩类
    """
    print("\n🔬 基于测井响应进行岩性分类...")
    
    gr = data['GR'].values
    pe = data['PE'].values
    den = data['DEN'].values
    
    # 初始化岩性标签
    lithology_labels = np.zeros(len(data), dtype=int)
    lithology_names = [''] * len(data)
    
    for i in range(len(data)):
        if gr[i] > 100:  # 高伽马 → 泥岩
            lithology_labels[i] = 0
            lithology_names[i] = "泥岩"
        elif gr[i] < 70 and pe[i] > 4.0:  # 低伽马 + 高光电因子 → 碳酸盐岩
            lithology_labels[i] = 2
            lithology_names[i] = "碳酸盐岩"
        else:  # 中等响应 → 砂岩类
            lithology_labels[i] = 1
            lithology_names[i] = "砂岩"
    
    print(f"📊 初步分类结果:")
    unique_labels, counts = np.unique(lithology_labels, return_counts=True)
    label_names = ["泥岩", "砂岩", "碳酸盐岩"]
    for label, count in zip(unique_labels, counts):
        print(f"   {label_names[label]}: {count} 个点 ({count/len(data)*100:.1f}%)")
    
    return lithology_labels, lithology_names

def apply_geological_continuity(labels, min_segment_length=20):
    """
    应用地质连续性约束
    
    原理：
    1. 消除过短的岩性段
    2. 将短段合并到相邻的主要岩性段
    3. 保持地质层序的连续性
    """
    print(f"\n🔧 应用地质连续性约束 (最小段长: {min_segment_length} 个点)...")
    
    corrected_labels = labels.copy()
    
    # 识别和处理短段
    i = 0
    merged_count = 0
    
    while i < len(corrected_labels):
        current_label = corrected_labels[i]
        
        # 找到当前段的结束位置
        j = i
        while j < len(corrected_labels) and corrected_labels[j] == current_label:
            j += 1
        
        segment_length = j - i
        
        # 如果段太短，合并到相邻段
        if segment_length < min_segment_length:
            # 选择合并目标：优先选择前面的段，如果没有则选择后面的段
            if i > 0:
                target_label = corrected_labels[i-1]
            elif j < len(corrected_labels):
                target_label = corrected_labels[j]
            else:
                target_label = current_label  # 保持不变
            
            # 执行合并
            if target_label != current_label:
                corrected_labels[i:j] = target_label
                merged_count += 1
                print(f"   合并短段: 位置 {i}-{j-1}, 长度 {segment_length} → 岩性 {target_label}")
        
        i = j
    
    print(f"✅ 共合并了 {merged_count} 个短段")
    
    return corrected_labels

def create_continuous_segments(labels, names):
    """创建连续的岩性段"""
    print("\n🏗️ 创建连续岩性段...")
    
    # 进一步平滑处理：使用滑动窗口多数投票
    window_size = 10
    smoothed_labels = labels.copy()
    
    for i in range(len(labels)):
        start = max(0, i - window_size//2)
        end = min(len(labels), i + window_size//2 + 1)
        window = labels[start:end]
        
        # 找到窗口内的多数标签
        unique_labels, counts = np.unique(window, return_counts=True)
        majority_label = unique_labels[np.argmax(counts)]
        smoothed_labels[i] = majority_label
    
    # 更新岩性名称
    label_names = ["泥岩", "砂岩", "碳酸盐岩"]
    smoothed_names = [label_names[label] for label in smoothed_labels]
    
    print(f"📊 平滑后的岩性分布:")
    unique_labels, counts = np.unique(smoothed_labels, return_counts=True)
    for label, count in zip(unique_labels, counts):
        print(f"   {label_names[label]}: {count} 个点 ({count/len(labels)*100:.1f}%)")
    
    return smoothed_labels, smoothed_names

def save_corrected_data(original_data, corrected_lithology, corrected_lithology_code):
    """保存修正后的数据"""
    print("\n💾 保存修正后的数据...")
    
    # 创建新的数据框
    corrected_data = original_data.copy()
    corrected_data['Lithology_Corrected'] = corrected_lithology
    corrected_data['Lithology_Code_Corrected'] = corrected_lithology_code
    
    # 保存到新文件
    output_path = r'C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_lithology_corrected.csv'
    corrected_data.to_csv(output_path, index=False)
    
    print(f"✅ 修正后的数据已保存到: {output_path}")
    
    return output_path

def analyze_segments(labels, depths):
    """分析岩性段的连续性"""
    print("\n📈 分析岩性段连续性...")
    
    label_names = ["泥岩", "砂岩", "碳酸盐岩"]
    
    i = 0
    segment_count = 0
    total_segments = {0: 0, 1: 0, 2: 0}
    
    while i < len(labels):
        current_label = labels[i]
        
        # 找到当前段的结束位置
        j = i
        while j < len(labels) and labels[j] == current_label:
            j += 1
        
        segment_length = j - i
        depth_span = depths[j-1] - depths[i] if j > i else 0
        
        total_segments[current_label] += 1
        segment_count += 1
        
        if segment_length >= 20:  # 只显示较大的段
            print(f"   段 {segment_count}: {label_names[current_label]}, "
                  f"长度 {segment_length} 点 ({depth_span:.1f}m), "
                  f"深度 {depths[i]:.1f}-{depths[j-1]:.1f}m")
        
        i = j
    
    print(f"\n📊 岩性段统计:")
    for label, count in total_segments.items():
        if count > 0:
            print(f"   {label_names[label]}: {count} 个段")

def main():
    """主函数"""
    print("🚀 开始岩性数据修正...")
    print("="*60)
    
    # 1. 加载原始数据
    data = load_original_data()
    if data is None:
        return
    
    # 2. 基于测井响应进行岩性分类
    initial_labels, initial_names = simple_lithology_classification(data)
    
    # 3. 应用地质连续性约束
    continuous_labels = apply_geological_continuity(initial_labels, min_segment_length=20)
    
    # 4. 创建连续的岩性段
    final_labels, final_names = create_continuous_segments(continuous_labels, initial_names)
    
    # 5. 分析岩性段
    analyze_segments(final_labels, data['Depth'].values)
    
    # 6. 保存修正后的数据
    output_path = save_corrected_data(data, final_names, final_labels)
    
    print("\n" + "="*60)
    print("✅ 岩性数据修正完成！")
    print(f"📁 修正后的数据文件: {output_path}")
    print("\n🎯 修正原理:")
    print("   1. 基于GR和PE测井响应进行岩性分类")
    print("   2. 应用连续性约束，消除不合理的短段")
    print("   3. 使用滑动窗口平滑，创建连续岩性层")
    print("   4. 符合地质沉积的连续性规律")
    
    # 7. 显示修正前后对比
    print(f"\n📊 修正前后对比:")
    print(f"   原始岩性类型: {len(data['Lithology'].unique())} 种")
    print(f"   修正后岩性类型: {len(np.unique(final_labels))} 种")
    print(f"   数据点总数: {len(data)} 个")

if __name__ == "__main__":
    main()
