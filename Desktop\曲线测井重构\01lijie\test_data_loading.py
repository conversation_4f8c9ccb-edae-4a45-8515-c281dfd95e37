#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据加载和深度段选择
"""

import csv

def test_data_loading():
    """测试数据加载和深度段选择"""
    print("📖 测试数据加载...")
    
    # 读取CSV文件
    data_path = r"C:\Users\<USER>\Desktop\曲线测井重构\实验\data\daqin_with_lithology.csv"
    
    try:
        with open(data_path, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            data = list(reader)
        
        print(f"✅ 成功加载数据: {len(data)} 行")
        print(f"📊 数据列名: {list(data[0].keys())}")
        
        # 检查深度范围
        depths = [float(row['深度']) for row in data]
        print(f"🎯 总深度范围: {min(depths):.1f} - {max(depths):.1f} m")
        
        # 检查推荐的深度段（扩展到50m跨度）
        target_start = 1955.8
        target_end = 2005.8
        
        target_data = [row for row in data if target_start <= float(row['深度']) <= target_end]
        print(f"🎨 目标深度段 ({target_start}-{target_end}m) 数据点数: {len(target_data)}")
        
        if target_data:
            target_depths = [float(row['深度']) for row in target_data]
            print(f"📏 实际深度范围: {min(target_depths):.1f} - {max(target_depths):.1f} m")
            
            # 检查测井曲线数据
            curves = ['自然伽马', '声波时差', '补偿中子', '岩性密度']
            print("\n📊 目标深度段的测井曲线数据样本:")
            for i, row in enumerate(target_data[:5]):  # 显示前5个数据点
                print(f"  深度 {row['深度']}m:")
                for curve in curves:
                    if curve in row:
                        print(f"    {curve}: {row[curve]}")
                print()
                
            # 检查岩性信息
            if '岩性' in target_data[0]:
                lithologies = [row['岩性'] for row in target_data]
                unique_lithologies = list(set(lithologies))
                print(f"🪨 岩性类型: {unique_lithologies}")
                
        else:
            print("❌ 目标深度段没有数据")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_data_loading()
