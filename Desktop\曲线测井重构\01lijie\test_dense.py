#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超密集数据点的测井曲线可视化
"""

import math
import random
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def generate_dense_well_log_data(depth_start=1955.8, depth_end=2005.8, num_points=10000):
    """生成超密集的测井曲线数据"""
    
    # 创建超密集深度数组
    depth_step = (depth_end - depth_start) / (num_points - 1)
    depth = [depth_start + i * depth_step for i in range(num_points)]
    
    # 生成波动的测井曲线数据
    gr_data = []
    ac_data = []
    cnl_data = []
    den_data = []
    lithology = []
    
    for i, d in enumerate(depth):
        # 添加多层次波动：长期趋势 + 中期波动 + 短期噪声
        base_trend = math.sin((d - depth_start) / 10) * 0.3
        medium_wave = math.sin((d - depth_start) / 2) * 0.2
        short_noise = random.uniform(-0.1, 0.1)
        
        # GR (自然伽马)：30-150 API
        gr_base = 80 + base_trend * 40 + medium_wave * 20 + short_noise * 10
        gr_data.append(max(30, min(150, gr_base)))
        
        # AC (声波时差)：180-320 μs/m
        ac_base = 250 + base_trend * 30 + medium_wave * 15 + short_noise * 8
        ac_data.append(max(180, min(320, ac_base)))
        
        # CNL (中子孔隙度)：0.05-0.35
        cnl_base = 0.2 + base_trend * 0.08 + medium_wave * 0.05 + short_noise * 0.02
        cnl_data.append(max(0.05, min(0.35, cnl_base)))
        
        # DEN (密度)：2.0-2.8 g/cm³
        den_base = 2.4 + base_trend * 0.2 + medium_wave * 0.1 + short_noise * 0.05
        den_data.append(max(2.0, min(2.8, den_base)))
        
        # 岩性（简化为3种）
        if gr_base < 70:
            lithology.append(1)  # 砂岩
        elif gr_base < 110:
            lithology.append(2)  # 粉砂岩
        else:
            lithology.append(3)  # 泥岩
    
    return depth, gr_data, ac_data, cnl_data, den_data, lithology

def create_dense_visualization():
    """创建超密集数据点的可视化"""
    
    print("🎯 生成超密集测井数据...")
    depth, gr, ac, cnl, den, lithology = generate_dense_well_log_data(num_points=10000)
    print(f"✅ 生成了 {len(depth)} 个数据点")
    
    # 创建图形
    fig, axes = plt.subplots(1, 5, figsize=(15, 10))
    fig.suptitle('超密集测井曲线可视化 (10,000个数据点)', fontsize=14, fontweight='bold')
    
    # 岩性颜色映射
    lithology_colors = ['#FFD700', '#90EE90', '#87CEEB']  # 金色、浅绿、浅蓝
    lithology_names = ['砂岩', '粉砂岩', '泥岩']
    
    # 1. 岩性柱
    ax1 = axes[0]
    for i in range(len(depth)-1):
        color = lithology_colors[lithology[i]-1]
        ax1.fill_betweenx([depth[i], depth[i+1]], 0, 1, color=color, alpha=0.8)
    
    ax1.set_xlim(0, 1)
    ax1.set_ylim(max(depth), min(depth))
    ax1.set_xlabel('岩性')
    ax1.set_ylabel('深度 (m)')
    ax1.set_xticks([])
    
    # 添加岩性图例
    for i, (color, name) in enumerate(zip(lithology_colors, lithology_names)):
        ax1.text(0.5, min(depth) + (max(depth) - min(depth)) * (0.1 + i * 0.15), 
                name, ha='center', va='center', fontsize=8,
                bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7))
    
    # 2. GR曲线
    ax2 = axes[1]
    ax2.plot(gr, depth, 'r-', linewidth=0.5, alpha=0.8)
    ax2.set_ylim(max(depth), min(depth))
    ax2.set_xlabel('GR (API)')
    ax2.grid(True, alpha=0.3)
    ax2.set_xlim(30, 150)
    
    # 3. AC曲线
    ax3 = axes[2]
    ax3.plot(ac, depth, 'b-', linewidth=0.5, alpha=0.8)
    ax3.set_ylim(max(depth), min(depth))
    ax3.set_xlabel('AC (μs/m)')
    ax3.grid(True, alpha=0.3)
    ax3.set_xlim(180, 320)
    
    # 4. CNL曲线
    ax4 = axes[3]
    ax4.plot(cnl, depth, 'g-', linewidth=0.5, alpha=0.8)
    ax4.set_ylim(max(depth), min(depth))
    ax4.set_xlabel('CNL')
    ax4.grid(True, alpha=0.3)
    ax4.set_xlim(0.05, 0.35)
    
    # 5. DEN曲线
    ax5 = axes[4]
    ax5.plot(den, depth, 'm-', linewidth=0.5, alpha=0.8)
    ax5.set_ylim(max(depth), min(depth))
    ax5.set_xlabel('DEN (g/cm³)')
    ax5.grid(True, alpha=0.3)
    ax5.set_xlim(2.0, 2.8)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片
    output_path = 'dense_well_log_visualization.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"✅ 图片已保存: {output_path}")
    
    plt.show()

if __name__ == "__main__":
    create_dense_visualization()
