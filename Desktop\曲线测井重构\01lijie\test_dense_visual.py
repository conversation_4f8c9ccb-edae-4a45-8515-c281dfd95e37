#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试密集视觉效果 - 通过高频波动创建密集曲线
"""

import math
import random

def create_dense_visual_curve(depth_start=1955.8, depth_end=2005.8, num_points=5000):
    """创建视觉上密集的测井曲线"""
    
    # 创建深度数组
    depth_step = (depth_end - depth_start) / (num_points - 1)
    depth = [depth_start + i * depth_step for i in range(num_points)]
    
    # 生成基础GR曲线
    base_gr = []
    for i, d in enumerate(depth):
        # 基础趋势
        base_trend = 80 + math.sin((d - depth_start) / 10) * 20
        base_gr.append(base_trend)
    
    # 创建三种不同密集程度的曲线
    curves = {
        'original': base_gr.copy(),
        'medium_dense': [],
        'super_dense': []
    }
    
    # 中等密集：添加中频波动
    for i, base_val in enumerate(base_gr):
        medium_wave = math.sin(i * 0.1) * 5  # 中频波动
        noise = random.uniform(-2, 2)  # 小噪声
        curves['medium_dense'].append(base_val + medium_wave + noise)
    
    # 超密集：添加多层高频波动
    for i, base_val in enumerate(base_gr):
        # 多层波动叠加
        high_freq1 = math.sin(i * 0.5) * 3    # 高频波动1
        high_freq2 = math.sin(i * 0.8) * 2    # 高频波动2
        high_freq3 = math.sin(i * 1.2) * 1.5  # 超高频波动
        medium_wave = math.sin(i * 0.1) * 4   # 中频波动
        noise = random.uniform(-1.5, 1.5)     # 随机噪声
        
        # 叠加所有波动
        dense_val = base_val + high_freq1 + high_freq2 + high_freq3 + medium_wave + noise
        curves['super_dense'].append(dense_val)
    
    return depth, curves

def analyze_curve_density():
    """分析曲线密集程度"""
    
    print("🎯 测试视觉密集效果")
    print("=" * 60)
    
    depth, curves = create_dense_visual_curve()
    
    # 分析每种曲线的变化频率
    for curve_name, curve_data in curves.items():
        # 计算相邻点的变化幅度
        changes = []
        for i in range(1, len(curve_data)):
            change = abs(curve_data[i] - curve_data[i-1])
            changes.append(change)
        
        avg_change = sum(changes) / len(changes)
        max_change = max(changes)
        
        print(f"\n📊 {curve_name.upper()} 曲线分析:")
        print(f"   平均变化幅度: {avg_change:.3f}")
        print(f"   最大变化幅度: {max_change:.3f}")
        print(f"   数据范围: {min(curve_data):.1f} - {max(curve_data):.1f}")
        
        # 计算波动密度（变化次数）
        direction_changes = 0
        for i in range(2, len(curve_data)):
            if ((curve_data[i] - curve_data[i-1]) * (curve_data[i-1] - curve_data[i-2])) < 0:
                direction_changes += 1
        
        print(f"   方向变化次数: {direction_changes}")
        print(f"   波动密度: {direction_changes / len(curve_data) * 100:.1f}%")
    
    # 显示部分数据对比
    print("\n📋 数据样本对比 (深度1960-1965m区间):")
    start_idx = 1000  # 大约对应1960m附近
    end_idx = start_idx + 20
    
    for i in range(start_idx, min(end_idx, len(depth))):
        print(f"深度{depth[i]:.2f}m: "
              f"原始={curves['original'][i]:.1f}, "
              f"中密={curves['medium_dense'][i]:.1f}, "
              f"超密={curves['super_dense'][i]:.1f}")
    
    print("\n✅ 密集效果分析完成!")
    print("💡 关键发现:")
    print("   🔸 原始曲线：平滑，变化少")
    print("   🔸 中等密集：有一定波动")
    print("   🔸 超密集：大量高频波动，视觉上非常密集")
    print("\n🎨 超密集曲线将在可视化中显示:")
    print("   ✨ 大量细微波动和振荡")
    print("   🌊 高频率的方向变化")
    print("   📈 丰富的细节层次")
    print("   🎯 学术论文级别的专业外观")

if __name__ == "__main__":
    analyze_curve_density()
