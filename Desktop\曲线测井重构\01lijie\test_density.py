#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据密度 - 展示20倍密度增强效果
"""

import math
import random

def generate_original_data(depth_start=1955.8, depth_end=2005.8, num_points=500):
    """生成原始数据（模拟真实测井数据的采样密度）"""
    depth_step = (depth_end - depth_start) / (num_points - 1)
    depth = [depth_start + i * depth_step for i in range(num_points)]
    
    # 生成简单的测井数据
    gr_data = []
    for i, d in enumerate(depth):
        base_value = 80 + math.sin((d - depth_start) / 10) * 30
        noise = random.uniform(-5, 5)
        gr_data.append(base_value + noise)
    
    return depth, gr_data

def interpolate_data(original_depth, original_values, target_points):
    """简单线性插值增加数据密度"""
    if len(original_depth) != len(original_values):
        raise ValueError("深度和数值数组长度不匹配")
    
    # 创建新的密集深度数组
    depth_min, depth_max = min(original_depth), max(original_depth)
    depth_step = (depth_max - depth_min) / (target_points - 1)
    dense_depth = [depth_min + i * depth_step for i in range(target_points)]
    
    # 线性插值
    dense_values = []
    for target_d in dense_depth:
        # 找到最近的两个原始数据点
        if target_d <= original_depth[0]:
            dense_values.append(original_values[0])
        elif target_d >= original_depth[-1]:
            dense_values.append(original_values[-1])
        else:
            # 找到插值区间
            for i in range(len(original_depth) - 1):
                if original_depth[i] <= target_d <= original_depth[i + 1]:
                    # 线性插值
                    ratio = (target_d - original_depth[i]) / (original_depth[i + 1] - original_depth[i])
                    interpolated_value = original_values[i] + ratio * (original_values[i + 1] - original_values[i])
                    dense_values.append(interpolated_value)
                    break
    
    return dense_depth, dense_values

def test_density_enhancement():
    """测试密度增强效果"""
    
    print("🎯 测试数据密度增强效果")
    print("=" * 50)
    
    # 生成原始数据
    original_depth, original_gr = generate_original_data(num_points=500)
    print(f"📊 原始数据点数: {len(original_depth)}")
    print(f"📏 深度范围: {min(original_depth):.1f} - {max(original_depth):.1f} m")
    print(f"📐 原始采样间隔: {(max(original_depth) - min(original_depth)) / (len(original_depth) - 1):.3f} m")
    
    # 20倍密度增强
    target_points = len(original_depth) * 20
    dense_depth, dense_gr = interpolate_data(original_depth, original_gr, target_points)
    
    print("\n🚀 20倍密度增强后:")
    print(f"📊 增强后数据点数: {len(dense_depth)}")
    print(f"📐 增强后采样间隔: {(max(dense_depth) - min(dense_depth)) / (len(dense_depth) - 1):.3f} m")
    print(f"🎯 密度提升倍数: {len(dense_depth) / len(original_depth):.1f}x")
    
    # 显示数据密度对比
    print("\n📈 数据密度对比:")
    print(f"原始数据: 每米 {len(original_depth) / 50:.1f} 个点")
    print(f"增强数据: 每米 {len(dense_depth) / 50:.1f} 个点")
    
    # 显示部分数据样本
    print("\n📋 原始数据样本 (前10个点):")
    for i in range(min(10, len(original_depth))):
        print(f"  深度: {original_depth[i]:.2f}m, GR: {original_gr[i]:.1f}")
    
    print("\n📋 增强数据样本 (前10个点):")
    for i in range(min(10, len(dense_depth))):
        print(f"  深度: {dense_depth[i]:.2f}m, GR: {dense_gr[i]:.1f}")
    
    print("\n✅ 数据密度测试完成!")
    print(f"💡 现在测井曲线将有 {len(dense_depth):,} 个数据点，")
    print(f"   比原来的 {len(original_depth)} 个点密集了 {len(dense_depth) / len(original_depth):.0f} 倍！")
    print("🎨 这将创建极其密集和平滑的测井曲线可视化效果！")

if __name__ == "__main__":
    test_density_enhancement()
