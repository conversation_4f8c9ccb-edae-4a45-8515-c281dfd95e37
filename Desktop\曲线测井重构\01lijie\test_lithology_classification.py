#!/usr/bin/env python3
"""
测试岩性分类逻辑的简单脚本
基于地质知识将原始数据重新分类为三个主要岩性类型
"""

def load_and_classify_lithology():
    """
    加载数据并基于地质知识重新分类岩性
    """
    import csv
    
    # 读取CSV数据
    data_file = r'C:\Users\<USER>\Desktop\曲线测井重构\实验\data\final_data_for_figure.csv'
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)  # 跳过标题行
            
            # 找到需要的列索引
            depth_idx = header.index('Depth')
            gr_idx = header.index('GR')
            rhob_idx = header.index('RHOB')
            dpor_idx = header.index('DPOR')
            
            # 读取数据
            depths = []
            grs = []
            rhobs = []
            dpors = []
            
            for row in reader:
                if len(row) > max(depth_idx, gr_idx, rhob_idx, dpor_idx):
                    depths.append(float(row[depth_idx]))
                    grs.append(float(row[gr_idx]))
                    rhobs.append(float(row[rhob_idx]))
                    dpors.append(float(row[dpor_idx]))
        
        print(f"✅ 成功加载数据，共 {len(depths)} 个数据点")
        print(f"📊 深度范围: {min(depths):.1f} - {max(depths):.1f} m")

        # 先分析数据分布
        print(f"\n📈 测井数据分布分析:")
        print(f"   GR范围: {min(grs):.1f} - {max(grs):.1f}")
        print(f"   RHOB范围: {min(rhobs):.3f} - {max(rhobs):.3f}")
        print(f"   DPOR范围: {min(dpors):.3f} - {max(dpors):.3f}")

        # 计算分位数来确定合理的分类阈值
        grs_sorted = sorted(grs)
        rhobs_sorted = sorted(rhobs)

        gr_33 = grs_sorted[len(grs_sorted)//3]
        gr_67 = grs_sorted[2*len(grs_sorted)//3]
        rhob_33 = rhobs_sorted[len(rhobs_sorted)//3]
        rhob_67 = rhobs_sorted[2*len(rhobs_sorted)//3]

        print(f"   GR三分位数: {gr_33:.1f}, {gr_67:.1f}")
        print(f"   RHOB三分位数: {rhob_33:.3f}, {rhob_67:.3f}")

        # 基于地质知识和数据分布进行岩性分类
        lithology_codes = []
        lithology_names = []

        # 调整后的岩性分类标准（基于数据的实际分布）：
        # 1. 砂岩 (Sandstone): 低GR + 高密度
        # 2. 泥岩 (Mudstone): 高GR + 低密度
        # 3. 粉砂岩 (Siltstone): 中等特征

        for i in range(len(depths)):
            gr = grs[i]
            rhob = rhobs[i]

            if gr > gr_67 and rhob < rhob_33:  # 高GR，低密度 -> 泥岩
                lithology_codes.append(2)
                lithology_names.append('泥岩')
            elif gr < gr_33 and rhob > rhob_67:  # 低GR，高密度 -> 砂岩
                lithology_codes.append(1)
                lithology_names.append('砂岩')
            else:  # 中等特征 -> 粉砂岩
                lithology_codes.append(3)
                lithology_names.append('粉砂岩')
        
        # 应用滑动窗口平滑，形成大段连续的岩性层段
        window_size = max(8, len(depths) // 25)  # 增大窗口大小
        smoothed_codes = lithology_codes.copy()

        for i in range(window_size, len(depths) - window_size):
            window = lithology_codes[i-window_size:i+window_size+1]
            # 计算众数
            counts = {}
            for code in window:
                counts[code] = counts.get(code, 0) + 1
            smoothed_codes[i] = max(counts, key=counts.get)

        # 进一步优化：合并短段
        min_segment_length = max(8, len(depths) // 25)
        final_codes = smoothed_codes.copy()

        # 多次迭代合并短段
        for _ in range(3):
            i = 0
            while i < len(depths):
                current_lith = final_codes[i]
                segment_start = i

                # 找到当前岩性段的结束位置
                while i < len(depths) and final_codes[i] == current_lith:
                    i += 1
                segment_end = i

                # 如果段太短，合并到相邻的主要岩性
                if segment_end - segment_start < min_segment_length:
                    if segment_start > 0 and segment_end < len(depths):
                        # 选择前后相邻段中较长的一个进行合并
                        prev_lith = final_codes[segment_start-1]
                        next_lith = final_codes[segment_end] if segment_end < len(depths) else prev_lith

                        # 计算前后段的长度来决定合并方向
                        prev_count = sum(1 for x in final_codes[:segment_start] if x == prev_lith)
                        next_count = sum(1 for x in final_codes[segment_end:] if x == next_lith) if segment_end < len(depths) else 0

                        merge_to = prev_lith if prev_count >= next_count else next_lith
                        for j in range(segment_start, segment_end):
                            final_codes[j] = merge_to
                    elif segment_start > 0:
                        for j in range(segment_start, segment_end):
                            final_codes[j] = final_codes[segment_start-1]
                    elif segment_end < len(depths):
                        for j in range(segment_start, segment_end):
                            final_codes[j] = final_codes[segment_end]

        smoothed_codes = final_codes
        
        # 更新岩性名称
        lithology_map = {1: '砂岩', 2: '泥岩', 3: '粉砂岩'}
        final_names = [lithology_map[code] for code in smoothed_codes]
        
        # 统计结果
        unique_codes = list(set(smoothed_codes))
        unique_codes.sort()
        
        print("\n🎯 岩性重新分类结果:")
        for code in unique_codes:
            count = smoothed_codes.count(code)
            percentage = count / len(smoothed_codes) * 100
            print(f"   - {lithology_map[code]}: {count}个点 ({percentage:.1f}%)")
        
        # 检查连续性
        print("\n📈 岩性层段分析:")
        current_lith = smoothed_codes[0]
        segment_start = 0
        segments = []
        
        for i in range(1, len(smoothed_codes)):
            if smoothed_codes[i] != current_lith:
                segments.append({
                    'lithology': lithology_map[current_lith],
                    'start_depth': depths[segment_start],
                    'end_depth': depths[i-1],
                    'thickness': depths[i-1] - depths[segment_start],
                    'points': i - segment_start
                })
                current_lith = smoothed_codes[i]
                segment_start = i
        
        # 添加最后一段
        segments.append({
            'lithology': lithology_map[current_lith],
            'start_depth': depths[segment_start],
            'end_depth': depths[-1],
            'thickness': depths[-1] - depths[segment_start],
            'points': len(smoothed_codes) - segment_start
        })
        
        for i, seg in enumerate(segments):
            print(f"   段{i+1}: {seg['lithology']} ({seg['start_depth']:.1f}-{seg['end_depth']:.1f}m, "
                  f"厚度{seg['thickness']:.1f}m, {seg['points']}个点)")
        
        print(f"\n✅ 成功将岩性重新分类为 {len(unique_codes)} 个主要类型")
        print("🎨 现在可以用于可视化，形成大段连续的岩性层段")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理数据时出错: {e}")
        return False

if __name__ == "__main__":
    print("🔬 开始测试岩性分类逻辑...")
    success = load_and_classify_lithology()
    if success:
        print("\n🎉 岩性分类测试完成！")
    else:
        print("\n⚠️ 岩性分类测试失败")
