#!/usr/bin/env python3
"""
测试主函数中的岩性分类功能
"""

import numpy as np
import os

def reorganize_lithology_data_from_real_data(depth, gr, rhob, dpor):
    """
    基于真实测井数据和地质知识，将岩性重新划分为三个主要类型
    """
    n_points = len(depth)
    new_lithology = np.zeros(n_points, dtype=int)
    
    # 计算分位数来确定合理的分类阈值
    gr_sorted = np.sort(gr)
    rhob_sorted = np.sort(rhob)
    
    gr_33 = gr_sorted[n_points//3]
    gr_67 = gr_sorted[2*n_points//3]
    rhob_33 = rhob_sorted[n_points//3]
    rhob_67 = rhob_sorted[2*n_points//3]
    
    print(f"分类阈值: GR({gr_33:.1f}, {gr_67:.1f}), RHOB({rhob_33:.3f}, {rhob_67:.3f})")
    
    for i in range(n_points):
        # 基于数据分布和地质知识进行岩性分类
        if gr[i] > gr_67 and rhob[i] < rhob_33:  # 高GR，低密度 -> 泥岩
            new_lithology[i] = 2  # 泥岩
        elif gr[i] < gr_33 and rhob[i] > rhob_67:  # 低GR，高密度 -> 砂岩
            new_lithology[i] = 1  # 砂岩
        else:  # 中等特征 -> 粉砂岩
            new_lithology[i] = 3  # 粉砂岩
    
    # 应用滑动窗口平滑，形成大段连续的岩性层段
    window_size = max(8, n_points // 25)
    smoothed_lithology = np.copy(new_lithology)
    
    for i in range(window_size, n_points - window_size):
        window = new_lithology[i-window_size:i+window_size+1]
        # 使用众数平滑
        unique, counts = np.unique(window, return_counts=True)
        smoothed_lithology[i] = unique[np.argmax(counts)]
    
    # 进一步优化：确保每个岩性段至少有一定长度
    min_segment_length = max(8, n_points // 25)
    final_lithology = np.copy(smoothed_lithology)
    
    # 多次迭代合并短段
    for _ in range(3):
        i = 0
        while i < n_points:
            current_lith = final_lithology[i]
            segment_start = i
            
            # 找到当前岩性段的结束位置
            while i < n_points and final_lithology[i] == current_lith:
                i += 1
            segment_end = i
            
            # 如果段太短，合并到相邻的主要岩性
            if segment_end - segment_start < min_segment_length:
                if segment_start > 0 and segment_end < n_points:
                    # 选择前后相邻段中较长的一个进行合并
                    prev_lith = final_lithology[segment_start-1]
                    next_lith = final_lithology[segment_end] if segment_end < n_points else prev_lith
                    
                    # 计算前后段的长度来决定合并方向
                    prev_count = np.sum(final_lithology[:segment_start] == prev_lith)
                    next_count = np.sum(final_lithology[segment_end:] == next_lith) if segment_end < n_points else 0
                    
                    merge_to = prev_lith if prev_count >= next_count else next_lith
                    final_lithology[segment_start:segment_end] = merge_to
                elif segment_start > 0:
                    final_lithology[segment_start:segment_end] = final_lithology[segment_start-1]
                elif segment_end < n_points:
                    final_lithology[segment_start:segment_end] = final_lithology[segment_end]
    
    # 转换为字符串标签
    lithology_map = {1: '砂岩', 2: '泥岩', 3: '粉砂岩'}
    new_lithology_labels = np.array([lithology_map[code] for code in final_lithology])
    
    return new_lithology_labels, final_lithology

def test_load_real_data():
    """测试加载真实数据的函数"""
    
    print("📖 测试加载重新规划的岩性数据...")
    
    try:
        # 使用numpy加载CSV数据
        data_path = r"C:\Users\<USER>\Desktop\曲线测井重构\实验\data\final_data_for_figure.csv"
        data = np.loadtxt(data_path, delimiter=',', skiprows=1)

        print(f"✅ 成功加载数据，共 {len(data)} 个数据点")
        print(f"📊 深度范围: {data[:, 0].min():.1f} - {data[:, 0].max():.1f} m")

        # 使用全部数据
        selected_data = data

        print(f"🎯 使用完整数据集，深度范围 {selected_data[:, 0].min():.1f}-{selected_data[:, 0].max():.1f}m，共 {len(selected_data)} 个数据点")

    except Exception as e:
        print(f"⚠️ 无法加载原始数据: {e}")
        return None

    # 提取深度和测井曲线信息 (根据CSV文件的列顺序)
    depth = selected_data[:, 0]      # Depth
    gr = selected_data[:, 12]        # GR (自然伽马)
    rhob = selected_data[:, 8]       # RHOB (体积密度)
    dpor = selected_data[:, 10]      # DPOR (密度孔隙度)

    # 基于真实测井数据重新分类岩性为三个主要类型
    lithology, lithology_code = reorganize_lithology_data_from_real_data(depth, gr, rhob, dpor)

    print(f"✅ 数据加载完成:")
    print(f"   - 深度范围: {depth.min():.1f} - {depth.max():.1f} m")
    print(f"   - 岩性类型: {set(lithology)}")
    print(f"   - 数据点数: {len(depth)}")

    # 统计岩性分布
    unique, counts = np.unique(lithology, return_counts=True)
    lithology_distribution = dict(zip(unique, counts))
    print(f"🎯 岩性分布: {lithology_distribution}")
    
    # 分析层段
    print("\n📈 岩性层段分析:")
    current_lith = lithology_code[0]
    segment_start = 0
    segments = []
    
    for i in range(1, len(lithology_code)):
        if lithology_code[i] != current_lith:
            lithology_map = {1: '砂岩', 2: '泥岩', 3: '粉砂岩'}
            segments.append({
                'lithology': lithology_map[current_lith],
                'start_depth': depth[segment_start],
                'end_depth': depth[i-1],
                'thickness': depth[i-1] - depth[segment_start],
                'points': i - segment_start
            })
            current_lith = lithology_code[i]
            segment_start = i
    
    # 添加最后一段
    lithology_map = {1: '砂岩', 2: '泥岩', 3: '粉砂岩'}
    segments.append({
        'lithology': lithology_map[current_lith],
        'start_depth': depth[segment_start],
        'end_depth': depth[-1],
        'thickness': depth[-1] - depth[segment_start],
        'points': len(lithology_code) - segment_start
    })
    
    for i, seg in enumerate(segments):
        print(f"   段{i+1}: {seg['lithology']} ({seg['start_depth']:.1f}-{seg['end_depth']:.1f}m, "
              f"厚度{seg['thickness']:.1f}m, {seg['points']}个点)")

    return depth, lithology, lithology_code

if __name__ == "__main__":
    print("🔬 开始测试主函数的岩性分类功能...")
    result = test_load_real_data()
    if result is not None:
        print("\n🎉 主函数测试完成！岩性已成功重新分类为三个主要类型")
    else:
        print("\n⚠️ 主函数测试失败")
