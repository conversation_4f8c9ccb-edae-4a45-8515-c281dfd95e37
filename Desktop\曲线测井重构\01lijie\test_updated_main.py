#!/usr/bin/env python3
"""
测试更新后的主代码中的岩性分类功能
"""

def estimate_mineral_composition(gr, rhob, dpor, cnls):
    """基于测井数据估算矿物组分"""
    
    # 1. 粘土矿物含量估算 (基于GR)
    gr_clean = 30  # 纯砂岩GR基线
    gr_shale = 150  # 纯泥岩GR值
    
    if gr <= gr_clean:
        clay = 0
    elif gr >= gr_shale:
        clay = 80  # 最大粘土含量80%
    else:
        clay = 80 * (gr - gr_clean) / (gr_shale - gr_clean)
    
    # 2. 石英含量估算 (基于密度和孔隙度)
    matrix_density = rhob / (1 - dpor/100) if dpor < 50 else rhob
    
    if matrix_density >= 2.65:
        quartz_from_density = min(70, (matrix_density - 2.55) * 70 / 0.1)
    else:
        quartz_from_density = max(0, (matrix_density - 2.45) * 50 / 0.2)
    
    # 3. 长石含量估算 (基于中子-密度差异)
    neutron_density_diff = abs(cnls - dpor)
    
    if neutron_density_diff < 5:
        feldspar_from_nd = 30  # 中等长石含量
    elif neutron_density_diff > 15:
        feldspar_from_nd = 10  # 低长石含量
    else:
        feldspar_from_nd = 30 - (neutron_density_diff - 5) * 20 / 10
    
    # 4. 综合计算和归一化
    remaining = 100 - clay
    
    if remaining <= 0:
        quartz = 0
        feldspar = 0
    else:
        quartz_raw = quartz_from_density * remaining / 100
        feldspar_raw = feldspar_from_nd * remaining / 100
        
        total_qf = quartz_raw + feldspar_raw
        if total_qf > remaining:
            quartz = quartz_raw * remaining / total_qf
            feldspar = feldspar_raw * remaining / total_qf
        else:
            quartz = quartz_raw
            feldspar = feldspar_raw
    
    # 确保总和为100%
    total = clay + quartz + feldspar
    if total > 0:
        clay = clay * 100 / total
        quartz = quartz * 100 / total
        feldspar = feldspar * 100 / total
    
    return quartz, feldspar, clay

def classify_lithology_by_minerals(quartz, feldspar, clay):
    """基于矿物组分进行岩性分类"""
    
    silicate_total = quartz + feldspar
    
    if clay > 35:
        return 2  # 泥岩
    elif silicate_total > 75 and clay < 15:
        return 1  # 砂岩
    else:
        return 3  # 粉砂岩

def test_updated_load_real_data():
    """测试更新后的加载数据函数"""
    import csv
    
    print("📖 测试更新后的岩性分类功能...")
    
    try:
        # 读取CSV数据
        data_file = r'C:\Users\<USER>\Desktop\曲线测井重构\实验\data\final_data_for_figure.csv'
        
        with open(data_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)  # 跳过标题行
            
            # 找到需要的列索引
            depth_idx = header.index('Depth')
            gr_idx = header.index('GR')
            rhob_idx = header.index('RHOB')
            dpor_idx = header.index('DPOR')
            cnls_idx = header.index('CNLS')
            
            # 读取数据
            depths = []
            grs = []
            rhobs = []
            dpors = []
            cnlss = []
            
            for row in reader:
                if len(row) > max(depth_idx, gr_idx, rhob_idx, dpor_idx, cnls_idx):
                    depths.append(float(row[depth_idx]))
                    grs.append(float(row[gr_idx]))
                    rhobs.append(float(row[rhob_idx]))
                    dpors.append(float(row[dpor_idx]))
                    cnlss.append(float(row[cnls_idx]))

        print(f"✅ 成功加载数据，共 {len(depths)} 个数据点")
        print(f"📊 深度范围: {min(depths):.1f} - {max(depths):.1f} m")

        # 基于矿物组分进行岩性分类
        lithology_codes = []
        quartz_contents = []
        feldspar_contents = []
        clay_contents = []
        
        for i in range(len(depths)):
            # 估算矿物组分
            quartz, feldspar, clay = estimate_mineral_composition(
                grs[i], rhobs[i], dpors[i], cnlss[i]
            )
            
            # 基于矿物组分分类
            code = classify_lithology_by_minerals(quartz, feldspar, clay)
            
            lithology_codes.append(code)
            quartz_contents.append(quartz)
            feldspar_contents.append(feldspar)
            clay_contents.append(clay)

        # 应用滑动窗口平滑
        window_size = max(8, len(depths) // 25)
        smoothed_codes = lithology_codes.copy()
        
        for i in range(window_size, len(depths) - window_size):
            window = lithology_codes[i-window_size:i+window_size+1]
            # 计算众数
            counts = {}
            for code in window:
                counts[code] = counts.get(code, 0) + 1
            smoothed_codes[i] = max(counts, key=counts.get)

        # 合并短段
        min_segment_length = max(8, len(depths) // 25)
        final_codes = smoothed_codes.copy()
        
        for _ in range(3):  # 多次迭代
            i = 0
            while i < len(depths):
                current_lith = final_codes[i]
                segment_start = i
                
                # 找到当前岩性段的结束位置
                while i < len(depths) and final_codes[i] == current_lith:
                    i += 1
                segment_end = i
                
                # 如果段太短，合并到相邻的主要岩性
                if segment_end - segment_start < min_segment_length:
                    if segment_start > 0 and segment_end < len(depths):
                        prev_lith = final_codes[segment_start-1]
                        next_lith = final_codes[segment_end] if segment_end < len(depths) else prev_lith
                        
                        # 计算前后段的长度
                        prev_count = sum(1 for x in final_codes[:segment_start] if x == prev_lith)
                        next_count = sum(1 for x in final_codes[segment_end:] if x == next_lith) if segment_end < len(depths) else 0
                        
                        merge_to = prev_lith if prev_count >= next_count else next_lith
                        for j in range(segment_start, segment_end):
                            final_codes[j] = merge_to
                    elif segment_start > 0:
                        for j in range(segment_start, segment_end):
                            final_codes[j] = final_codes[segment_start-1]
                    elif segment_end < len(depths):
                        for j in range(segment_start, segment_end):
                            final_codes[j] = final_codes[segment_end]

        # 转换为字符串标签
        lithology_map = {1: '砂岩', 2: '泥岩', 3: '粉砂岩'}
        final_names = [lithology_map[code] for code in final_codes]

        print(f"✅ 数据加载完成:")
        print(f"   - 深度范围: {min(depths):.1f} - {max(depths):.1f} m")
        print(f"   - 岩性类型: {set(final_names)}")
        print(f"   - 数据点数: {len(depths)}")

        # 统计岩性分布
        unique_codes = list(set(final_codes))
        unique_codes.sort()
        
        print("\n🎯 基于矿物组分的岩性分类结果:")
        for code in unique_codes:
            count = final_codes.count(code)
            percentage = count / len(final_codes) * 100
            print(f"   - {lithology_map[code]}: {count}个点 ({percentage:.1f}%)")

        # 显示平均矿物组分
        print("\n🧪 平均矿物组分:")
        avg_quartz = sum(quartz_contents) / len(quartz_contents)
        avg_feldspar = sum(feldspar_contents) / len(feldspar_contents)
        avg_clay = sum(clay_contents) / len(clay_contents)
        print(f"   - 石英: {avg_quartz:.1f}%")
        print(f"   - 长石: {avg_feldspar:.1f}%")
        print(f"   - 粘土矿物: {avg_clay:.1f}%")

        # 分析层段
        print("\n📈 岩性层段分析:")
        current_lith = final_codes[0]
        segment_start = 0
        segments = []
        
        for i in range(1, len(final_codes)):
            if final_codes[i] != current_lith:
                segments.append({
                    'lithology': lithology_map[current_lith],
                    'start_depth': depths[segment_start],
                    'end_depth': depths[i-1],
                    'thickness': depths[i-1] - depths[segment_start],
                    'points': i - segment_start
                })
                current_lith = final_codes[i]
                segment_start = i
        
        # 添加最后一段
        segments.append({
            'lithology': lithology_map[current_lith],
            'start_depth': depths[segment_start],
            'end_depth': depths[-1],
            'thickness': depths[-1] - depths[segment_start],
            'points': len(final_codes) - segment_start
        })
        
        for i, seg in enumerate(segments):
            print(f"   段{i+1}: {seg['lithology']} ({seg['start_depth']:.1f}-{seg['end_depth']:.1f}m, "
                  f"厚度{seg['thickness']:.1f}m, {seg['points']}个点)")

        return depths, final_names, final_codes

    except Exception as e:
        print(f"❌ 处理数据时出错: {e}")
        return None

if __name__ == "__main__":
    print("🔬 开始测试更新后的主代码岩性分类功能...")
    result = test_updated_load_real_data()
    if result is not None:
        print("\n🎉 更新后的主代码测试完成！")
        print("📝 现在主代码已经基于矿物组分进行三种岩性的合理划分")
    else:
        print("\n⚠️ 主代码测试失败")
