#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1D序列数据注意力稳定性可视化方案
基于AGPNet和Scientific Reports的扰动测试思路
适用于测井数据的GIAT模型注意力分析
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from matplotlib.gridspec import GridSpec
import matplotlib.patches as patches
from scipy.stats import pearsonr
from skimage.metrics import structural_similarity as ssim

def create_1d_attention_stability_visualization(
    depth, 
    original_sequence, 
    perturbed_sequence,
    giat_attention_orig,
    giat_attention_pert, 
    transformer_attention_orig,
    transformer_attention_pert,
    geological_boundaries=None
):
    """
    创建1D序列数据的注意力稳定性可视化
    
    参数:
    - depth: 深度数组
    - original_sequence: 原始测井序列
    - perturbed_sequence: 扰动后的测井序列  
    - giat_attention_orig/pert: GIAT模型的原始/扰动注意力权重
    - transformer_attention_orig/pert: Transformer的原始/扰动注意力权重
    - geological_boundaries: 地质边界位置列表
    """
    
    # 设置顶级期刊风格
    plt.style.use('default')
    plt.rcParams.update({
        'font.family': 'sans-serif',
        'font.sans-serif': ['Arial', 'DejaVu Sans'],
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 11,
        'axes.titleweight': 'bold',
        'axes.labelweight': 'bold',
        'figure.dpi': 100
    })
    
    # 创建图形布局
    fig = plt.figure(figsize=(18, 12))
    gs = GridSpec(3, 4, figure=fig, 
                  height_ratios=[1, 1, 0.8], 
                  width_ratios=[1, 1, 1, 0.8],
                  hspace=0.3, wspace=0.3)
    
    # 主标题
    fig.suptitle('GIAT Attention Stability Analysis: 1D Sequence Perturbation Test', 
                 fontsize=18, weight='bold', y=0.95)
    
    # === 第一行：输入序列对比 ===
    
    # 原始输入序列
    ax1 = fig.add_subplot(gs[0, 0])
    ax1.plot(original_sequence, depth, 'b-', linewidth=2, label='Original')
    ax1.set_title('Original Input\nSequence', weight='bold')
    ax1.set_ylabel('Depth (m)', weight='bold')
    ax1.set_xlabel('Log Value', weight='bold')
    ax1.invert_yaxis()
    ax1.grid(True, alpha=0.3)
    
    # 扰动输入序列
    ax2 = fig.add_subplot(gs[0, 1])
    ax2.plot(original_sequence, depth, 'b-', linewidth=1, alpha=0.5, label='Original')
    ax2.plot(perturbed_sequence, depth, 'r-', linewidth=2, label='Perturbed (+5% Noise)')
    ax2.set_title('Perturbed Input\nSequence', weight='bold')
    ax2.set_xlabel('Log Value', weight='bold')
    ax2.invert_yaxis()
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 扰动差异
    ax3 = fig.add_subplot(gs[0, 2])
    noise = perturbed_sequence - original_sequence
    ax3.plot(noise, depth, 'orange', linewidth=2)
    ax3.set_title('Perturbation\nNoise', weight='bold')
    ax3.set_xlabel('Noise Amplitude', weight='bold')
    ax3.invert_yaxis()
    ax3.grid(True, alpha=0.3)
    ax3.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    # === 第二行：注意力权重对比 ===
    
    # GIAT注意力稳定性
    ax4 = fig.add_subplot(gs[1, 0])
    
    # 创建注意力热力图（1D转2D技巧）
    attention_matrix_giat = np.column_stack([giat_attention_orig, giat_attention_pert])
    im1 = ax4.imshow(attention_matrix_giat.T, aspect='auto', cmap='Blues', 
                     extent=[depth[0], depth[-1], 0, 2])
    ax4.set_title('GIAT Attention\nStability', weight='bold', color='blue')
    ax4.set_xlabel('Depth (m)', weight='bold')
    ax4.set_yticks([0.5, 1.5])
    ax4.set_yticklabels(['Original', 'Perturbed'])
    
    # 添加地质边界
    if geological_boundaries:
        for boundary in geological_boundaries:
            ax4.axvline(x=boundary, color='red', linestyle='-', linewidth=2, alpha=0.8)
    
    # 计算GIAT稳定性指标
    pcc_giat = pearsonr(giat_attention_orig, giat_attention_pert)[0]
    ssim_giat = ssim(giat_attention_orig.reshape(-1, 1), 
                     giat_attention_pert.reshape(-1, 1), data_range=1.0)
    
    ax4.text(0.02, 0.98, f'PCC: {pcc_giat:.3f}\nSSIM: {ssim_giat:.3f}', 
             transform=ax4.transAxes, va='top', ha='left',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.8),
             fontsize=10, weight='bold')
    
    # Transformer注意力不稳定性
    ax5 = fig.add_subplot(gs[1, 1])
    
    attention_matrix_trans = np.column_stack([transformer_attention_orig, transformer_attention_pert])
    im2 = ax5.imshow(attention_matrix_trans.T, aspect='auto', cmap='Reds',
                     extent=[depth[0], depth[-1], 0, 2])
    ax5.set_title('Transformer Attention\nInstability', weight='bold', color='red')
    ax5.set_xlabel('Depth (m)', weight='bold')
    ax5.set_yticks([0.5, 1.5])
    ax5.set_yticklabels(['Original', 'Perturbed'])
    
    # 添加地质边界
    if geological_boundaries:
        for boundary in geological_boundaries:
            ax5.axvline(x=boundary, color='blue', linestyle='-', linewidth=2, alpha=0.8)
    
    # 计算Transformer稳定性指标
    pcc_trans = pearsonr(transformer_attention_orig, transformer_attention_pert)[0]
    ssim_trans = ssim(transformer_attention_orig.reshape(-1, 1), 
                      transformer_attention_pert.reshape(-1, 1), data_range=1.0)
    
    ax5.text(0.02, 0.98, f'PCC: {pcc_trans:.3f}\nSSIM: {ssim_trans:.3f}', 
             transform=ax5.transAxes, va='top', ha='left',
             bbox=dict(boxstyle='round,pad=0.3', facecolor='lightcoral', alpha=0.8),
             fontsize=10, weight='bold')
    
    # 注意力差异分析
    ax6 = fig.add_subplot(gs[1, 2])
    
    giat_diff = np.abs(giat_attention_orig - giat_attention_pert)
    trans_diff = np.abs(transformer_attention_orig - transformer_attention_pert)
    
    ax6.plot(giat_diff, depth, 'b-', linewidth=2, label='GIAT Difference')
    ax6.plot(trans_diff, depth, 'r-', linewidth=2, label='Transformer Difference')
    ax6.set_title('Attention\nDifference', weight='bold')
    ax6.set_xlabel('|Attention Difference|', weight='bold')
    ax6.invert_yaxis()
    ax6.legend()
    ax6.grid(True, alpha=0.3)
    
    # === 第三行：综合分析 ===
    
    # 稳定性指标对比
    ax7 = fig.add_subplot(gs[2, 0])
    
    metrics = ['PCC', 'SSIM']
    giat_scores = [pcc_giat, ssim_giat]
    trans_scores = [pcc_trans, ssim_trans]
    
    x = np.arange(len(metrics))
    width = 0.35
    
    bars1 = ax7.bar(x - width/2, giat_scores, width, label='GIAT', 
                    color='steelblue', alpha=0.8)
    bars2 = ax7.bar(x + width/2, trans_scores, width, label='Transformer', 
                    color='lightcoral', alpha=0.8)
    
    ax7.set_title('Stability Metrics\nComparison', weight='bold')
    ax7.set_ylabel('Score', weight='bold')
    ax7.set_xticks(x)
    ax7.set_xticklabels(metrics)
    ax7.legend()
    ax7.set_ylim(0, 1)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        ax7.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', weight='bold')
    
    for bar in bars2:
        height = bar.get_height()
        ax7.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{height:.3f}', ha='center', va='bottom', weight='bold')
    
    # 地质一致性分析
    ax8 = fig.add_subplot(gs[2, 1])
    
    if geological_boundaries:
        # 计算注意力峰值与地质边界的对齐度
        giat_peaks = find_attention_peaks(giat_attention_orig, depth)
        trans_peaks = find_attention_peaks(transformer_attention_orig, depth)
        
        giat_alignment = calculate_boundary_alignment(giat_peaks, geological_boundaries)
        trans_alignment = calculate_boundary_alignment(trans_peaks, geological_boundaries)
        
        alignment_data = [giat_alignment, trans_alignment]
        colors = ['steelblue', 'lightcoral']
        labels = ['GIAT', 'Transformer']
        
        bars = ax8.bar(labels, alignment_data, color=colors, alpha=0.8)
        ax8.set_title('Geological Boundary\nAlignment', weight='bold')
        ax8.set_ylabel('Alignment Score (%)', weight='bold')
        ax8.set_ylim(0, 100)
        
        # 添加数值标签
        for bar, score in zip(bars, alignment_data):
            ax8.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 1,
                    f'{score:.1f}%', ha='center', va='bottom', weight='bold')
    
    # 结论总结
    ax9 = fig.add_subplot(gs[2, 2])
    ax9.axis('off')
    
    conclusion_text = f"""
    🎯 Key Findings:
    
    ✅ GIAT Advantages:
    • PCC: {pcc_giat:.3f} vs {pcc_trans:.3f}
    • SSIM: {ssim_giat:.3f} vs {ssim_trans:.3f}
    • {(pcc_giat/pcc_trans-1)*100:+.1f}% stability improvement
    
    🔬 Technical Insights:
    • Geological-guided attention bias
    • CSC filter stabilization
    • Robust to input perturbations
    
    📊 Practical Impact:
    • More reliable predictions
    • Better geological consistency
    • Enhanced interpretability
    """
    
    ax9.text(0.05, 0.95, conclusion_text, transform=ax9.transAxes, 
             va='top', ha='left', fontsize=11,
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.3))
    
    # 添加颜色条
    ax_cbar = fig.add_subplot(gs[:2, 3])
    cbar = plt.colorbar(im1, cax=ax_cbar)
    cbar.set_label('Attention Weight', weight='bold', fontsize=12)
    
    plt.tight_layout(rect=[0, 0, 1, 0.92])
    
    return fig

def find_attention_peaks(attention_weights, depth, prominence=0.1):
    """寻找注意力权重的峰值位置"""
    from scipy.signal import find_peaks
    peaks, _ = find_peaks(attention_weights, prominence=prominence)
    return depth[peaks]

def calculate_boundary_alignment(attention_peaks, geological_boundaries, tolerance=5.0):
    """计算注意力峰值与地质边界的对齐度"""
    if not geological_boundaries or len(attention_peaks) == 0:
        return 0.0
    
    aligned_count = 0
    for peak in attention_peaks:
        for boundary in geological_boundaries:
            if abs(peak - boundary) <= tolerance:
                aligned_count += 1
                break
    
    return (aligned_count / len(attention_peaks)) * 100

# 示例使用
if __name__ == "__main__":
    # 生成示例数据
    np.random.seed(42)
    depth = np.linspace(2000, 2100, 100)
    
    # 原始测井序列
    original_sequence = np.sin(depth/10) + 0.5*np.cos(depth/5) + np.random.normal(0, 0.1, 100)
    
    # 扰动序列（添加5%噪声）
    noise_level = 0.05
    noise = np.random.normal(0, noise_level * np.std(original_sequence), 100)
    perturbed_sequence = original_sequence + noise
    
    # 模拟GIAT注意力权重（稳定）
    giat_attention_orig = np.exp(-((depth - 2050)**2) / 200) + 0.3*np.exp(-((depth - 2080)**2) / 100)
    giat_attention_orig = giat_attention_orig / np.max(giat_attention_orig)
    
    # GIAT扰动后注意力（高度相似）
    giat_attention_pert = giat_attention_orig + np.random.normal(0, 0.02, 100)
    giat_attention_pert = np.clip(giat_attention_pert, 0, 1)
    
    # 模拟Transformer注意力权重（不稳定）
    transformer_attention_orig = np.random.beta(2, 5, 100)
    transformer_attention_pert = np.random.beta(2, 5, 100)  # 完全不同的模式
    
    # 地质边界
    geological_boundaries = [2020, 2050, 2080]
    
    # 创建可视化
    fig = create_1d_attention_stability_visualization(
        depth, original_sequence, perturbed_sequence,
        giat_attention_orig, giat_attention_pert,
        transformer_attention_orig, transformer_attention_pert,
        geological_boundaries
    )
    
    plt.savefig('1d_attention_stability_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 1D注意力稳定性可视化已生成！")
    print("📊 展示内容：输入扰动 + 注意力对比 + 稳定性指标 + 地质一致性")
    print("🎯 核心优势：GIAT注意力稳定性显著优于Transformer")
