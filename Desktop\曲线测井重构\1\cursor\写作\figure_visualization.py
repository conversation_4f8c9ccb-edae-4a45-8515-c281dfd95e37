import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.gridspec import GridSpec
from scipy.stats import pearsonr

def visualize_figure1():
    """
    可视化图1：GIAT模型总体架构图 (V2 - Improved Layout)
    """
    fig, ax = plt.subplots(figsize=(10, 6))
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 7)
    ax.axis('off')
    fig.suptitle('Figure 1 Mockup: Overall Architecture of GIAT (Improved Layout)', fontsize=16)

    # --- Colors ---
    colors = {
        'input': '#ADD8E6',
        'innovation': '#90EE90',
        'bias_matrix': '#FFA500',
        'highlight': '#FF0000',
        'standard': '#D3D3D3'
    }

    # --- Left Column: Bias Generation ---
    ax.text(2.5, 6.5, 'Geological Bias Generation', ha='center', weight='bold', fontsize=11)
    
    # Input X
    p_in_left = patches.Rectangle((1.5, 5.5), 2, 0.5, facecolor=colors['input'], edgecolor='k', linewidth=1)
    ax.add_patch(p_in_left)
    ax.text(2.5, 5.75, 'Input Well Log X', ha='center', va='center', fontsize=9)

    # Arrow
    ax.arrow(2.5, 5.5, 0, -0.5, head_width=0.15, head_length=0.2, fc='k', ec='k')

    # Conv
    p_conv = patches.Rectangle((1.5, 4.5), 2, 0.5, facecolor=colors['innovation'], edgecolor='k', linewidth=1)
    ax.add_patch(p_conv)
    ax.text(2.5, 4.75, '1D Conv with CSC Filters', ha='center', va='center', fontsize=9)

    # Arrow
    ax.arrow(2.5, 4.5, 0, -0.5, head_width=0.15, head_length=0.2, fc='k', ec='k')

    # Similarity
    p_sim = patches.Rectangle((1.5, 3.5), 2, 0.5, facecolor=colors['innovation'], edgecolor='k', linewidth=1)
    ax.add_patch(p_sim)
    ax.text(2.5, 3.75, 'Compute Geo-Similarity', ha='center', va='center', fontsize=9)
    
    # --- Right Column: GIAT Block ---
    ax.text(8.5, 6.5, 'GIAT Block', ha='center', weight='bold', fontsize=11)
    
    giat_block = patches.Rectangle((5.5, 0.5), 6, 5.5, facecolor='none', edgecolor='k', linestyle='--', linewidth=1.5)
    ax.add_patch(giat_block)

    # Input X -> LN
    ax.text(6.5, 5.75, 'Input X', ha='center', va='center', fontsize=9, bbox=dict(facecolor=colors['input'], edgecolor='k'))
    ax.arrow(6.5, 5.5, 0, -0.5, head_width=0.15, head_length=0.2, fc='k', ec='k')
    
    ax.text(6.5, 4.75, 'LayerNorm', ha='center', va='center', fontsize=9, bbox=dict(facecolor=colors['standard'], edgecolor='k'))
    
    # Q, K, V branches
    ax.arrow(6.5, 4.5, 0, -0.5, head_width=0.15, head_length=0.2, fc='k', ec='k')
    ax.plot([6.5, 6], [4, 4], 'k-') # to Q
    ax.plot([6.5, 7.5], [4, 4], 'k-') # to K
    ax.plot([6.5, 9.5], [4, 4], 'k-') # to V
    
    ax.text(6, 3.75, 'Q', ha='center', va='center', bbox=dict(facecolor=colors['standard'], edgecolor='k'))
    ax.text(7.5, 3.75, 'K', ha='center', va='center', bbox=dict(facecolor=colors['standard'], edgecolor='k'))
    ax.text(9.5, 3.75, 'V', ha='center', va='center', bbox=dict(facecolor=colors['standard'], edgecolor='k'))

    # Attention Calculation
    ax.arrow(6, 3.5, 1, -0.5, head_width=0.15, head_length=0.2, fc='k', ec='k')
    ax.arrow(7.5, 3.5, -0.5, -0.5, head_width=0.15, head_length=0.2, fc='k', ec='k')
    
    p_dot1 = patches.Circle((7, 2.75), 0.25, facecolor=colors['standard'], edgecolor='k')
    ax.add_patch(p_dot1)
    ax.text(7, 2.75, 'X', ha='center', va='center', fontsize=10)
    ax.text(7, 3.1, r'$Q \cdot K^T$', ha='center', va='center', fontsize=9)
    
    ax.arrow(7, 2.5, 0, -0.4, head_width=0.15, head_length=0.2, fc='k', ec='k')
    
    ax.text(7, 1.9, 'Scaling', ha='center', va='center', fontsize=9, bbox=dict(facecolor=colors['standard'], edgecolor='k'))
    
    # --- The Core Injection ---
    arrow_m = patches.FancyArrowPatch((3.5, 3.75), (6.8, 1.55), connectionstyle="arc3,rad=.2", 
                                      color=colors['bias_matrix'], linewidth=2,
                                      arrowstyle='->,head_length=5,head_width=3')
    ax.add_patch(arrow_m)
    ax.text(4.5, 2.7, 'Attention Bias\nMatrix M', ha='center', color=colors['bias_matrix'], weight='bold', fontsize=10)

    ax.arrow(7, 1.7, 0, -0.15, head_width=0.15, head_length=0.2, fc='k', ec='k')

    p_add = patches.Circle((7, 1.3), 0.25, facecolor=colors['highlight'], edgecolor='k')
    ax.add_patch(p_add)
    ax.text(7, 1.3, '+', ha='center', va='center', color='white', weight='bold', fontsize=12)
    ax.text(8.25, 1.3, r'$... \bf{+ M}$', ha='center', va='center', color=colors['highlight'], weight='bold', fontsize=12)

    ax.arrow(7, 1.05, 0, -0.2, head_width=0.15, head_length=0.2, fc='k', ec='k')
    
    ax.text(7, 0.7, 'Softmax', ha='center', va='center', fontsize=9, bbox=dict(facecolor=colors['standard'], edgecolor='k'))

    ax.arrow(7, 0.5, 2, 0, head_width=0.15, head_length=0.2, fc='k', ec='k')
    ax.arrow(9.5, 3.5, 0, -2.75, head_width=0.15, head_length=0.2, fc='k', ec='k')

    p_dot2 = patches.Circle((9, 0.5), 0.25, facecolor=colors['standard'], edgecolor='k')
    ax.add_patch(p_dot2)
    ax.text(9, 0.5, 'X', ha='center', va='center', fontsize=10)
    
    ax.arrow(9.25, 0.5, 1, 0, head_width=0.15, head_length=0.2, fc='k', ec='k')
    ax.text(10.75, 0.5, 'Output', ha='center', va='center', fontsize=9, bbox=dict(facecolor=colors['input'], edgecolor='k'))
    
    plt.tight_layout()


def visualize_figure2():
    """
    可视化图2：地质引导注意力偏置生成流程图
    """
    # 1. Mock Data
    L, P, C = 200, 3, 2  # Length, num_curves, num_classes
    np.random.seed(42)
    
    # Panel A data
    t = np.linspace(0, 10, L)
    X = np.zeros((L, P))
    X[:, 0] = np.sin(t*2) + np.random.randn(L) * 0.1
    X[:, 1] = np.cos(t) + (t > 5) * 0.5 + np.random.randn(L) * 0.1
    X[:, 2] = 2 * (t > 5) - 1 + np.random.randn(L) * 0.2
    
    filter_len = 21
    K = np.random.randn(C, P, filter_len)
    K[0,0,:] = np.sin(np.linspace(0, 4, filter_len)) # Make filters look different
    K[0,1,:] = np.cos(np.linspace(0, 4, filter_len))
    K[1,0,:] = (np.linspace(0, 4, filter_len) > 2) * 1
    K[1,1,:] = (np.linspace(0, 4, filter_len) > 2) * -1
    
    # Panel B data
    response_maps = np.zeros((L, C, P))
    for c in range(C):
        for p in range(P):
            response_maps[:, c, p] = np.convolve(X[:, p], K[c, p, ::-1], mode='same')
            
    # Panel C & D data
    # To make blocks clearer for visualization, we use a mocked similarity matrix
    S_mock = np.zeros((L, L))
    zone1 = slice(0, L//2)
    zone2 = slice(L//2, L)
    S_mock[zone1, zone1] = 1.0
    S_mock[zone2, zone2] = 0.8
    S_mock += np.random.rand(L, L) * 0.1
    S_mock = (S_mock + S_mock.T) / 2
    np.fill_diagonal(S_mock, 1.2)
    
    M = np.log(np.maximum(S_mock, 1e-6))
    
    # 2. Visualization
    fig = plt.figure(figsize=(18, 9))
    gs = GridSpec(2, 4, figure=fig, width_ratios=[1.5, 0.5, 2, 2])
    fig.suptitle('Figure 2: Generation of Geologically-Informed Attention Bias Matrix (M)', fontsize=16)

    # Panel A
    ax_A1 = fig.add_subplot(gs[0, 0])
    ax_A1.set_title("A1: Input Well Logs (X)")
    for i in range(P):
        ax_A1.plot(X[:, i], range(L), label=f'Curve {i+1}')
    ax_A1.invert_yaxis()
    ax_A1.legend()
    ax_A1.set_xlabel("Value")
    ax_A1.set_ylabel("Depth")

    ax_A2 = fig.add_subplot(gs[1, 0])
    ax_A2.set_title("A2: Pre-computed CSC Filters (K)")
    for c in range(C):
        for p in range(P):
            ax_A2.plot(range(filter_len), K[c,p,:] + (c*P+p)*2, label=f'K(c={c},p={p})')
    ax_A2.set_xlabel("Filter Tap")
    ax_A2.get_yaxis().set_visible(False)

    # Panel B
    ax_B = fig.add_subplot(gs[:, 1])
    ax_B.set_title("B: Response Maps")
    for c in range(C):
        for p in range(P):
            ax_B.plot(response_maps[:, c, p], range(L))
    ax_B.invert_yaxis()
    ax_B.get_yaxis().set_visible(False)
    ax_B.set_xlabel("Response")

    # Panel C
    ax_C = fig.add_subplot(gs[:, 2])
    ax_C.set_title("C: Geological Similarity Matrix (S)")
    im_c = ax_C.imshow(S_mock, cmap='viridis', aspect='auto', origin='lower')
    ax_C.set_xlabel("Depth Index")
    ax_C.set_ylabel("Depth Index")
    fig.colorbar(im_c, ax=ax_C, orientation='vertical', label='Similarity Score')
    ax_C.text(L/4, L/4, 'High\nIntra-Zone\nSimilarity', ha='center', va='center', color='white', fontsize=10)
    ax_C.text(L*3/4, L*3/4, 'High\nIntra-Zone\nSimilarity', ha='center', va='center', color='white', fontsize=10)

    # Panel D
    ax_D = fig.add_subplot(gs[:, 3])
    ax_D.set_title("D: Final Attention Bias Matrix (M)")
    im_d = ax_D.imshow(M, cmap='cividis', aspect='auto', origin='lower')
    ax_D.set_xlabel("Depth Index")
    ax_D.set_ylabel("Depth Index")
    fig.colorbar(im_d, ax=ax_D, orientation='vertical', label='Bias Value')
    
    # Arrows
    arrow_a_b = patches.FancyArrowPatch((0.23, 0.5), (0.28, 0.5), transform=fig.transFigure, 
                                        connectionstyle="arc3,rad=0", color='k', 
                                        arrowstyle='->,head_length=5,head_width=3')
    fig.text(0.255, 0.52, "1D Conv", ha='center', va='center', fontsize=12)

    arrow_b_c = patches.FancyArrowPatch((0.36, 0.5), (0.43, 0.5), transform=fig.transFigure, 
                                        connectionstyle="arc3,rad=0", color='k', 
                                        arrowstyle='->,head_length=5,head_width=3')
    fig.text(0.395, 0.52, "Compute\nPairwise\nSimilarity", ha='center', va='center', fontsize=12)
    
    arrow_c_d = patches.FancyArrowPatch((0.68, 0.5), (0.75, 0.5), transform=fig.transFigure, 
                                        connectionstyle="arc3,rad=0", color='k', 
                                        arrowstyle='->,head_length=5,head_width=3')
    fig.text(0.715, 0.52, "Post-\nprocess", ha='center', va='center', fontsize=12)

    fig.patches.extend([arrow_a_b, arrow_b_c, arrow_c_d])

    plt.tight_layout(rect=[0, 0, 1, 0.95])
    plt.subplots_adjust(wspace=0.9, hspace=0.3)


def visualize_figure3_v2():
    """
    可视化图3 (V2): 结合了预测结果、注意力图和忠实度验证的综合图
    """
    # 1. Mock Data
    np.random.seed(42)
    L = 100
    C = 3
    
    # Ground Truth & Prediction
    litho_true = np.zeros(L, dtype=int)
    litho_true[25:50] = 1
    litho_true[50:80] = 2
    litho_pred = np.copy(litho_true)
    litho_pred[75:78] = 1 # A small error
    
    # Well Log
    log_curve = np.random.randn(L) * 0.5
    log_curve[litho_true==1] += 2.0
    log_curve[litho_true==2] -= 1.5

    # Attention Maps
    # GIAT - stable and blocky, aligned with ground truth
    att_giat = np.zeros((L, L))
    for c in np.unique(litho_true):
        indices = np.where(litho_true == c)[0]
        for i in indices:
            att_giat[i, indices] = 1.0
    att_giat += np.random.rand(L,L) * 0.1
    att_giat_perturbed = att_giat + np.random.rand(L,L) * 0.05

    # Standard Transformer - unstable and less structured
    att_std = np.random.rand(L,L)
    att_std_perturbed = np.random.rand(L,L)
    
    # Pearson correlation
    corr_giat, _ = pearsonr(att_giat.flatten(), att_giat_perturbed.flatten())
    corr_std, _ = pearsonr(att_std.flatten(), att_std_perturbed.flatten())

    # 2. Visualization
    fig = plt.figure(figsize=(12, 7))
    gs = GridSpec(2, 3, figure=fig, width_ratios=[0.8, 2, 2], height_ratios=[1,1])
    fig.suptitle('Figure 3 (V2): Performance, Interpretability, and Faithfulness of GIAT', fontsize=16)

    # Column 1: Prediction & Data
    ax1 = fig.add_subplot(gs[:, 0])
    ax1.plot(log_curve, range(L), 'k-', label='GR Log')
    ax1.set_xlabel('GR Value')
    ax1.set_ylabel('Depth')
    ax1.invert_yaxis()
    ax1.twiny() # Create a second x-axis for lithology
    ax1.set_xlim(0, 2)
    ax1.set_xticks([0.5, 1.5])
    ax1.set_xticklabels(['True', 'Pred'])
    ax1.imshow(litho_true.reshape(-1, 1), extent=(0, 1, L, 0), cmap='viridis', aspect='auto', interpolation='nearest')
    ax1.imshow(litho_pred.reshape(-1, 1), extent=(1, 2, L, 0), cmap='viridis', aspect='auto', interpolation='nearest')
    ax1.set_title("A: Prediction Result")

    # Column 2: GIAT Attention Map
    ax2 = fig.add_subplot(gs[:, 1])
    ax2.imshow(att_giat, cmap='magma', aspect='auto')
    ax2.set_title("B: GIAT Attention Map (Interpretability)")
    ax2.set_xlabel("Sequence Position")
    ax2.set_ylabel("Sequence Position")

    # Column 3: Faithfulness Verification
    ax3_top = fig.add_subplot(gs[0, 2])
    ax3_top.imshow(att_std_perturbed, cmap='magma')
    ax3_top.set_title(f"C: Standard Transformer Faithfulness\n(Pearson r = {corr_std:.2f})", color='red')
    ax3_top.set_xticks([])
    
    ax3_bottom = fig.add_subplot(gs[1, 2])
    ax3_bottom.imshow(att_giat_perturbed, cmap='magma')
    ax3_bottom.set_title(f"D: Our GIAT Faithfulness\n(Pearson r = {corr_giat:.2f})", color='green')
    ax3_bottom.set_xticks([])

    plt.tight_layout(rect=[0, 0, 1, 0.95])


def visualize_figure3():
    """
    可视化图3：注意力图忠实度对比实验可视化
    """
    # 1. Mock Data
    np.random.seed(0)
    L = 50 # Attention map size
    
    # Standard Transformer: low stability
    A_std = np.random.rand(L, L)
    A_std_perturbed = np.random.rand(L, L) # A completely different map
    
    # Our GIAT: high stability
    base = np.zeros((L, L))
    zone1 = slice(0, L//2)
    zone2 = slice(L//2, L)
    base[zone1, zone1] = 1
    base[zone2, zone2] = 0.5
    A_giat = base + np.random.randn(L, L) * 0.1
    A_giat_perturbed = A_giat + np.random.randn(L, L) * 0.05 # Only slightly perturbed
    
    # Calculate similarity
    corr_std, _ = pearsonr(A_std.flatten(), A_std_perturbed.flatten())
    corr_giat, _ = pearsonr(A_giat.flatten(), A_giat_perturbed.flatten())

    # 2. Visualization
    fig, axes = plt.subplots(2, 2, figsize=(8, 8))
    fig.suptitle('Figure 3: Interpretation Faithfulness Verification', fontsize=16)

    cmap = 'magma'
    # Row 1: Standard Transformer
    axes[0, 0].imshow(A_std, cmap=cmap)
    axes[0, 0].set_title('Standard Transformer\n(Original Attention)')
    axes[0, 1].imshow(A_std_perturbed, cmap=cmap)
    axes[0, 1].set_title('Standard Transformer\n(Perturbed Attention)')
    fig.text(0.5, 0.51, f'Pearson Correlation: {corr_std:.2f}', ha='center', va='center', fontsize=12, color='red', weight='bold')

    # Row 2: Our GIAT
    axes[1, 0].imshow(A_giat, cmap=cmap)
    axes[1, 0].set_title('Our GIAT\n(Original Attention)')
    axes[1, 1].imshow(A_giat_perturbed, cmap=cmap)
    axes[1, 1].set_title('Our GIAT\n(Perturbed Attention)')
    fig.text(0.5, 0.08, f'Pearson Correlation: {corr_giat:.2f}', ha='center', va='center', fontsize=12, color='green', weight='bold')

    for ax in axes.flat:
        ax.set_xticks([])
        ax.set_yticks([])

    plt.tight_layout(rect=[0, 0.1, 1, 0.95])


def visualize_figure3_v3():
    """
    可视化图3 (V3): 最终优化版，增强了可读性和视觉关联
    """
    # 1. Mock Data
    np.random.seed(42)
    L = 100
    C = 3
    
    # Ground Truth & Prediction
    litho_true = np.zeros(L, dtype=int)
    litho_bounds = [0, 25, 50, 80, 100]
    litho_classes = [0, 1, 2, 0]
    for i in range(len(litho_bounds)-1):
        litho_true[litho_bounds[i]:litho_bounds[i+1]] = litho_classes[i]

    litho_pred = np.copy(litho_true)
    litho_pred[75:78] = 1 # A small error
    
    # Well Log
    log_curve = np.random.randn(L) * 0.5
    log_curve[litho_true==1] += 2.0
    log_curve[litho_true==2] -= 1.5

    # Attention Maps
    att_giat = np.zeros((L, L))
    for i in range(len(litho_bounds)-1):
        s, e = litho_bounds[i], litho_bounds[i+1]
        att_giat[s:e, s:e] = 1.0 - i*0.2
    att_giat += np.random.rand(L,L) * 0.05
    att_giat_perturbed = att_giat + np.random.rand(L,L) * 0.02
    
    att_std = np.random.rand(L,L)
    att_std_perturbed = np.random.rand(L,L)
    
    corr_giat, _ = pearsonr(att_giat.flatten(), att_giat_perturbed.flatten())
    corr_std, _ = pearsonr(att_std.flatten(), att_std_perturbed.flatten())

    # 2. Visualization
    fig = plt.figure(figsize=(14, 6))
    gs = GridSpec(2, 4, figure=fig, width_ratios=[0.4, 0.6, 2, 2], height_ratios=[1,1], wspace=0.4, hspace=0.3)
    fig.suptitle('Figure 3 (Final Version): GIAT Delivers Performance, Interpretability, and Faithfulness', fontsize=16)

    # --- Column 1: Performance ---
    ax1 = fig.add_subplot(gs[:, 0])
    cmap_litho = plt.get_cmap('viridis', C)
    ax1.imshow(litho_true.reshape(-1, 1), extent=(0, 1, L, 0), cmap=cmap_litho, aspect='auto', interpolation='nearest')
    ax1.imshow(litho_pred.reshape(-1, 1), extent=(1, 2, L, 0), cmap=cmap_litho, aspect='auto', interpolation='nearest')
    ax1.set_title("A: Prediction vs. Truth")
    ax1.set_xticks([0.5, 1.5]); ax1.set_xticklabels(['True', 'Pred'], rotation=90)
    ax1.set_ylabel("Depth")

    ax2 = fig.add_subplot(gs[:, 1])
    ax2.plot(log_curve, range(L), 'k-')
    ax2.set_title("B: Input Well Log")
    ax2.invert_yaxis(); ax2.set_yticklabels([]); ax2.set_xlabel("GR Value")

    # --- Column 2: Interpretability ---
    ax3 = fig.add_subplot(gs[:, 2])
    ax3.imshow(att_giat, cmap='magma', aspect='auto')
    ax3.set_title("C: GIAT's Attention is Interpretable")
    # Add colored overlays
    for i in range(len(litho_bounds)-1):
        s, e = litho_bounds[i], litho_bounds[i+1]
        rect = patches.Rectangle((s,s), e-s, e-s, linewidth=2, edgecolor=cmap_litho(litho_classes[i]), facecolor='none', alpha=0.7)
        ax3.add_patch(rect)
    ax3.set_xlabel("Sequence Position"); ax3.set_ylabel("Sequence Position")

    # --- Column 3: Faithfulness ---
    ax4_top = fig.add_subplot(gs[0, 3])
    ax4_top.imshow(att_std_perturbed, cmap='magma')
    ax4_top.set_title(f"D: Unstable Attention (Standard Trans.)\nPearson r = {corr_std:.2f}", color='red')
    ax4_top.set_xticks([]); ax4_top.set_yticks([])
    
    ax4_bottom = fig.add_subplot(gs[1, 3])
    ax4_bottom.imshow(att_giat_perturbed, cmap='magma')
    ax4_bottom.set_title(f"E: Stable Attention (Our GIAT)\nPearson r = {corr_giat:.2f}", color='green')
    ax4_bottom.set_xticks([]); ax4_bottom.set_yticks([])

    plt.tight_layout(rect=[0, 0, 1, 0.95])


if __name__ == '__main__':
    # --- 选择要显示的图 ---
    figure_to_show = '3_v3' # 可选 1, 2, 3, '3_v2', or '3_v3'

    # 根据选择的图来调用对应的函数
    if figure_to_show == 1:
        visualize_figure1()
    elif figure_to_show == 2:
        visualize_figure2()
    elif figure_to_show == 3:
        visualize_figure3()
    elif figure_to_show == '3_v2':
        visualize_figure3_v2()
    elif figure_to_show == '3_v3':
        visualize_figure3_v3()
    
    # 构建文件名并保存图像
    # 这应该在 plt.show() 之前完成
    filename = f"figure_{figure_to_show}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"图像已成功保存到文件: {filename}")

    plt.show()