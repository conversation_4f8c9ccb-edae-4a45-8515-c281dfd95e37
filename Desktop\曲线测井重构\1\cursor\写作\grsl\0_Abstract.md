<!-- 模板论文字数参考: ~150 words -->
Accurate lithology identification from well logs is crucial for subsurface resource evaluation. Although Transformer-based models excel at sequence modeling, their "black-box" nature and lack of geological guidance limit their performance and trustworthiness. To overcome these limitations, this letter proposes the Geologically-Informed Attention Transformer (GIAT), a novel framework that deeply fuses data-driven geological priors with the Transformer's attention mechanism. The core of GIAT is a new attention-biasing mechanism. We repurpose Category-Wise Sequence Correlation (CSC) filters to generate a geologically-informed relational matrix, which is injected into the self-attention calculation to explicitly guide the model toward geologically coherent patterns. On two challenging datasets, GIAT achieves state-of-the-art performance with an accuracy of up to 95.4%, significantly outperforming existing models. More importantly, GIAT demonstrates exceptional interpretation faithfulness under input perturbations and generates geologically coherent predictions. Our work presents a new paradigm for building more accurate, reliable, and interpretable deep learning models for geoscience applications.

---
**中文翻译:**

从测井数据中准确识别岩性对于地下资源评价至关重要。尽管基于Transformer的模型在序列建模方面表现出色，但其"黑箱"特性以及缺乏地质学引导的缺点，限制了它们的性能和可信度。为克服这些局限，本文提出了一种地质引导注意力Transformer（GIAT），这是一个将数据驱动的地质先验知识与Transformer的注意力机制进行深度融合的新颖框架。GIAT的核心是一种新的注意力偏置机制。我们重新利用了类别感知序列相关性（CSC）滤波器来生成一个蕴含地质信息的关系矩阵，并将其注入自注意力计算中，以显式地引导模型关注符合地质规律的模式。在两个具有挑战性的数据集上，GIAT取得了领先的性能，准确率高达95.4%，显著超越了现有模型。更重要的是，GIAT在输入扰动下表现出卓越的解释忠实度，并能生成地质上连贯的预测结果。我们的工作为构建更准确、更可靠、更可解释的地球科学应用深度学习模型提供了一种新范式。 