<!-- Introduction Section Layout for GIAT Letter (GRSL Style) -->

# I. INTRODUCTION


**English Version:**
Lithology identification from well logs serves as a fundamental cornerstone for oil and gas exploration and reservoir evaluation, directly influencing hydrocarbon accumulation assessment and optimal drilling strategies [12]. Recent advances in deep learning, particularly Transformer architectures, have demonstrated remarkable potential for sequential data modeling, offering new paradigms for automated geological interpretation with enhanced accuracy and efficiency [15].

**中文版本:**
测井岩性识别是油气勘探和储层评价的基础性工作，直接影响油气聚集评估和最优钻井策略制定[12]。近年来深度学习技术，特别是Transformer架构，在序列数据建模方面展现出卓越潜力，为自动化地质解释提供了新的技术范式，显著提升了预测精度和效率[15]。


**English Version:**
Recent advances in deep learning have introduced sophisticated Transformer-based architectures for lithology identification, achieving remarkable performance improvements. The Adaboost-Transformer method demonstrates superior accuracy rates of 95.20% and 95.50% compared to standalone models [2], while ReFormer integrates recurrent dynamics with multi-scale attention mechanisms for enhanced sequential data processing [3]. However, these approaches suffer from critical limitations that hinder their practical deployment in geological applications. First, they exhibit inherent "black-box" characteristics with unstable interpretation patterns, where attention mechanisms are fragile to adversarial perturbations and can be easily altered by slight input variations [8,9]. Second, current Transformer models lack geological domain constraints, operating purely on data-driven patterns without incorporating established geological principles [2,3]. These limitations significantly impact geological decision-making processes, where interpretability and domain knowledge integration are essential for reliable subsurface characterization and risk assessment.

**中文版本:**
近年来深度学习技术引入了基于Transformer的复杂架构用于岩性识别，取得了显著的性能提升。Adaboost-Transformer方法相比单独模型实现了95.20%和95.50%的优异准确率[2]，而ReFormer将循环动态与多尺度注意力机制相结合，增强了序列数据处理能力[3]。然而，这些方法存在关键局限性，阻碍了其在地质应用中的实际部署。首先，它们表现出固有的"黑箱"特性和不稳定的解释模式，注意力机制对对抗性扰动脆弱，容易被轻微的输入变化所改变[8,9]。其次，当前Transformer模型缺乏地质领域约束，纯粹基于数据驱动模式运行，未融入已建立的地质原理[2,3]。这些局限性显著影响地质决策过程，而可解释性和领域知识融合对于可靠的地下表征和风险评估至关重要。


**English Version:**
Parallel to deep learning approaches, geological prior-based feature engineering methods have demonstrated significant potential in lithology identification. The Category-wise Sequence Correlation (CSC) filter represents a data-driven, model-free approach that captures sequence structural variations by leveraging labeling information to enhance discrimination between different lithology types [7]. Domain knowledge (DK) constraint methods integrate geological expertise through formation lithology indices (FLI) to filter and optimize training data, achieving improved accuracy and stability in well log reconstruction [1]. Additionally, correlation analysis combined with multi-scale median filtering techniques effectively extract geological features while reducing noise interference, enabling BiLSTM networks to better capture bidirectional sequence dependencies [6]. Wavelet transform-based approaches further enhance model interpretability by incorporating stratigraphic information, with studies showing average performance improvements of 6.25% through geological information integration [11]. However, these geological prior methods face critical limitations in practical applications. They exhibit insufficient integration with deep learning architectures, often operating as preprocessing steps rather than being deeply embedded within model structures. Furthermore, their utilization of temporal and contextual information remains inadequate, failing to fully exploit the sequential nature and spatial correlations inherent in well logging data, ultimately constraining their performance potential.

**中文版本:**
与深度学习方法并行，基于地质先验的特征工程方法在岩性识别中展现出显著潜力。类别感知序列相关性(CSC)滤波器代表了一种数据驱动、无模型的方法，通过利用标签信息捕获序列结构变化，增强不同岩性类型间的区分度[7]。领域知识(DK)约束方法通过地层岩性指数(FLI)整合地质专业知识来过滤和优化训练数据，在测井曲线重构中实现了更高的准确性和稳定性[1]。此外，相关性分析结合多尺度中值滤波技术有效提取地质特征并减少噪声干扰，使BiLSTM网络能够更好地捕获双向序列依赖关系[6]。基于小波变换的方法通过融入地层信息进一步增强模型可解释性，研究表明通过地质信息整合可实现平均6.25%的性能提升[11]。然而，这些地质先验方法在实际应用中面临关键局限性。它们与深度学习架构的融合不充分，往往作为预处理步骤运行，而非深度嵌入模型结构中。此外，它们对时序和上下文信息的利用仍然不足，未能充分挖掘测井数据固有的序列特性和空间相关性，最终限制了其性能潜力。


**English Version:**
Analysis of current methodologies demonstrates that both deep learning and geological prior-based approaches possess distinct advantages and limitations in lithology identification. Deep learning models effectively capture complex nonlinear patterns and achieve high prediction accuracy. For example, attention-based convolutional neural networks can prioritize key features in seismic data [10], but their interpretability is limited and they lack geological domain constraints. In contrast, geological prior methods enhance model interpretability and domain knowledge integration, and have improved accuracy in metamorphic rock classification tasks [14]. However, these methods are insufficiently integrated with deep learning architectures and do not fully exploit temporal and spatial dependencies. Efficient integration of geological priors with deep learning models, balancing performance and interpretability, has become a central scientific task in lithology identification. Most current fusion attempts remain at the preprocessing stage and lack deep architectural integration. Therefore, a unified framework that organically combines the pattern recognition capabilities of deep learning with the domain expertise embedded in geological priors is essential for advancing lithology identification technology.

**中文版本:**
对现有方法的分析表明，深度学习与地质先验方法在岩性识别中各具优势与局限。深度学习方法能够有效捕获复杂的非线性模式，实现较高的预测精度，例如基于注意力机制的卷积神经网络能够优先关注地震数据中的关键特征[10]，但其可解释性有限且缺乏地质领域约束。相比之下，地质先验方法在提升模型可解释性和领域知识融合方面表现突出，并在变质岩分类任务中取得了精度提升[14]，但与深度学习架构的深度融合不足，且对时空依赖关系的挖掘有限。目前，地质先验知识与深度学习模型的高效融合以兼顾模型性能与地质可解释性，已成为岩性识别领域亟需解决的核心科学任务。现有的融合尝试多停留于浅层预处理阶段，尚未实现深度架构层面的有机集成。因此，提出一种统一框架，将深度学习的模式识别能力与地质先验中蕴含的领域知识有机结合，对于推动岩性识别技术的进步具有重要意义。


**English Version:**
To address the challenge of integrating geological prior knowledge with deep learning models, this letter presents the Geologically-Informed Attention Transformer (GIAT) framework. The core innovation transforms data-driven geological priors from CSC filters into explicit attention bias matrices, directly injected into the Transformer's self-attention mechanism. By constructing a geological similarity matrix and transforming it into a dynamic bias matrix M, GIAT regularizes the learning process, addressing interpretation instability in existing Transformer architectures [8,9] while ensuring predictions are both data-driven and geologically constrained. Experiments on two challenging datasets demonstrate GIAT's superior performance, achieving 94.7% accuracy on the Kansas dataset and surpassing the best baseline by 3.9%, with exceptional interpretation stability under perturbations and PCC improvements of 46.6% and 62.9% over Transformer.

**中文版本:**
为解决地质先验知识与深度学习模型融合的挑战，本文提出了地质信息引导注意力Transformer (GIAT)框架。核心创新在于将CSC滤波器的数据驱动地质先验转化为显式注意力偏置矩阵，直接注入Transformer自注意力机制。通过构建地质相似性矩阵并转化为动态偏置矩阵M，GIAT正则化了学习过程，解决了现有Transformer架构的解释不稳定性问题[8,9]，确保预测既是数据驱动的又受地质约束。在两个挑战性数据集上的实验表明，GIAT表现卓越，在Kansas数据集上达到94.7%准确率，超越最佳基线3.9%，同时在扰动下实现卓越的解释稳定性，PCC相比标准Transformer提升46.6%和62.9%。

---

**总字数控制：约270-300字，严格Letter篇幅。**
