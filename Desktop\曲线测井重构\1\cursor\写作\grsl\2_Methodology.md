<!-- 模板论文字数参考: ~600 words (纯文本部分) -->

# II. METHODOLOGY

<div align="center">
    <img src="./images/图1网络架构图.png" width="90%">
</div>

**Fig. 1. The overall architecture of the proposed Geologically-Informed Attention Transformer (GIAT) model.** The framework consists of three main components: (a) CSC Filter Learning; (b) Geological Attention Fusion  (c) Geologically-Informed Self-Attention

As illustrated in Fig. 1, GIAT integrates three core components. Module (a) learns category-specific CSC filters from well logs to establish geological prior templates. Module (b) generates geological attention maps based on these filters and combines them with standard Transformer attention to create a guidance matrix $M$. Module (c) utilizes context vectors refined through this geologically-guided attention to produce final lithology predictions. The model's core innovation lies in this synergy: offline-captured prior knowledge is transformed online into explicit attention bias, thereby regularizing the learning process. This ensures that final predictions are both data-driven and constrained by geological principles, enhancing accuracy and interpretability.
---
**中文翻译:**

如图1所示，GIAT集成了三个核心组件。模块(a)从测井曲线中学习类别特定的CSC滤波器，以建立地质先验模板;模块（b）根据这些滤波器生成地质注意力图，并将其与Transformer的标准注意力相结合，创建一个引导矩阵$M$;模块（c）使用经过这种地质引导注意力提炼的上下文向量来生成最终的岩性预测。该模型的核心创新在于这种协同作用：离线捕获的先验知识被在线转化为显式的注意力偏置，从而对模型的学习过程进行正则化。这确保了最终的预测不仅是数据驱动的，而且受到地质原则的约束，从而提升了准确性和可解释性。

---

## A. CSC Filter Learning

We use CSC filter [7] to establish geological priors. This data-driven tool captures unique sequential structural features for each lithology class from training data statistics. Each filter represents a typical pattern learned for a specific class on a specific curve. Unlike learned convolutional kernels, these filters provide a stable and interpretable prior. Rather than direct feature extraction, we use them as expert templates to evaluate geological similarity across the sequence.

---
**中文翻译:**

**A. 地质先验提取**

我们使用类别感知序列相关性（CSC）滤波器[7]来建立地质先验。它是一种数据驱动的工具，能从训练数据统计中为每个岩性类别捕捉独特的序列结构特征。每个滤波器代表了特定类别在特定曲线上学习到的典型标志。与学习得到的卷积核不同，这些滤波器提供了稳定且可解释的先验。我们不直接用它提取特征，而是将其作为专家模板来评估整个序列的地质相似性。

---

## B. Geological Attention Fusion

The core of our method converts prior knowledge into a dynamic attention bias matrix $M$, addressing interpretation instability in existing Transformer architectures [4]. The input sequence is convolved with pre-computed CSC filters to generate a response map tensor. A geological feature vector $g(u)$ is constructed for each sequence position $u$ by concatenating response values, quantifying the match between local data patterns and geological prototypes. A relational similarity matrix $S \in \mathbb{R}^{L \times L}$ is constructed by computing cosine similarity between feature vectors:
$$
S_{i,k} = \frac{g(i) \cdot g(k)}{\|g(i)\| \|g(k)\|} \quad (1)
$$
where $S_{i,k}$ represents the geological consistency between positions $i$ and $k$. Finally, this similarity matrix $S$ is processed to form the final attention bias matrix $M$.

---
**中文翻译:**

**B. 在线地质注意力融合**

我们的方法核心在于将先验知识转化为动态注意力偏置矩阵 $M$。输入序列与预计算的CSC滤波器卷积，生成响应图张量。为每个序列位置 $u$ 构建地质特征向量 $g(u)$，由该位置的响应值拼接而成，量化局部数据模式与地质原型的匹配度。通过计算特征向量间的余弦相似度构建 $L \times L$ 关系相似性矩阵 $S$：
$$
S_{i,k} = \frac{g(i) \cdot g(k)}{\|g(i)\| \|g(k)\|} \quad (1)
$$
其中 $S_{i,k}$ 表示位置 $i$ 和 $k$ 之间的地质一致性。最后，该相似性矩阵 $S$ 经过处理（如缩放）形成最终的注意力偏置矩阵 $M$。

---
**中文翻译:**

**C. 地质引导的自注意力**

Standard self-attention mechanisms are data-agnostic. Our GIAT model addresses this limitation by incorporating bias matrix $M$ into attention score calculation:
$$
\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}} + M\right)V \quad (2)
$$
Adding matrix $M$ before softmax normalization provides inductive bias that guides focus on geologically similar position pairs[13]. High values in $M_{i,k}$ enhance attention scores $(QK^T)_{i,k}$, directing attention toward positions with similar geological characteristics. This reshapes learning where attention is guided by geological principles rather than learned from scratch. The mechanism ensures attention patterns are data-driven and geologically meaningful, improving performance and interpretive fidelity.

标准的自注意力机制是与数据无关的。我们的GIAT模型通过将偏置矩阵 $M$ 引入注意力分数计算来解决这个问题：
$$
\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{QK^T}{\sqrt{d_k}} + M\right)V \quad (2)
$$
在softmax归一化前加入矩阵 $M$ 提供归纳偏置，引导关注地质相似位置对[13]。$M_{i,k}$ 中的高值增强注意力分数 $(QK^T)_{i,k}$，引导注意力指向具有相似地质特征的位置。这重塑了学习过程，注意力受到地质原则引导而非从零学习。该机制确保注意力模式既数据驱动又具有地质意义，提升性能和解释忠实度。

---

## D. Model Training Objective

GIAT is trained end-to-end as a multi-class classification task. The final layer of the model is a fully connected layer with a softmax activation function, which outputs the probability distribution over the $C$ lithology classes. The model's parameters are optimized by minimizing the standard cross-entropy loss $\mathcal{L}$, a widely used objective function for classification problems, defined as:
$$
\mathcal{L} = - \sum_{i=1}^{N} \sum_{c=1}^{C} y_{i,c} \log(\hat{y}_{i,c}) \quad (3)
$$
where $N$ is the total number of samples, $C$ is the number of classes, $y_{i,c}$ is a binary indicator of the true class, and $\hat{y}_{i,c}$ is the model's predicted probability for that class.

---
**中文翻译:**

**D. 模型训练目标**

GIAT模型作为一个多类别分类任务进行端到端训练。模型的最后一层是一个带有softmax激活函数的全连接层，它为 $C$ 个岩性类别输出概率分布。模型的参数通过最小化标准的交叉熵损失 $\mathcal{L}$ 来进行优化，这是一个广泛用于分类问题的目标函数，定义如下：
$$
\mathcal{L} = - \sum_{i=1}^{N} \sum_{c=1}^{C} y_{i,c} \log(\hat{y}_{i,c}) \quad (3)
$$
其中，$N$ 是样本总数，$C$ 是类别数，$y_{i,c}$ 是真实类别的二进制指示符，$\hat{y}_{i,c}$ 是模型对该类别的预测概率。 