l<!-- 模板论文字数参考: ~300 words -->

# III. EXPERIMENTS AND RESULTS

This section details the experimental framework, presents the quantitative and qualitative results, and analyzes the performance of our proposed GIAT model.

---
**中文翻译:**

# III. 实验与结果

本章详细介绍了实验框架，呈现了定量和定性结果，并分析了我们提出的GIAT模型的性能。

---


## A. Experimental Setup

To ensure comprehensive and rigorous evaluation, our experiments are conducted on two distinct types of datasets—the public Kansas cross-well dataset and a more challenging private dataset from heterogeneous reservoirs in the Daqing Oilfield. For both datasets, we adopt a strict cross-well validation strategy, reserving one well as a completely independent blind test well to evaluate the model's generalization capability.

We benchmark our proposed GIAT against baseline methods. Model performance is primarily evaluated through standard classification metrics, including accuracy, precision, recall, and Kappa. Additionally, to quantitatively assess interpretation faithfulness, we design a perturbation experiment. When introducing small bounded noise to the input, we measure the stability of model attention maps by computing Pearson Correlation Coefficient (PCC) and Structural Similarity Index (SSIM). All deep learning models are implemented in PyTorch and trained using the Adam optimizer with a learning rate of 1e-4, employing early stopping mechanisms to prevent overfitting.

---
**中文翻译:**

## A. 实验设置

为确保全面而严谨的评估，我们的实验在两个数据集上进行：公开的堪萨斯州跨井数据集和来自大庆油田非均质储层的私有数据集。对两个数据集，我们都采用严格的跨井验证策略，保留一口井作为完全独立的盲测井以评估模型的泛化能力。

我们将提出的GIAT模型与基线方法进行基准比较。模型性能主要通过标准分类指标进行评估，包括准确率、精确率、召回率和Kappa系数。此外，为定量评估解释的忠实度，我们设计了一个扰动实验。当向输入中引入微小的有界噪声时，我们通过计算皮尔逊相关系数（PCC）和结构相似性指数（SSIM）来衡量模型注意力图的稳定性。所有深度学习模型均在PyTorch中实现，并使用学习率为1e-4的Adam优化器进行训练，同时采用早停机制以防止过拟合。

## B. Results and Analysis

### 1) Quantitative Comparison

As presented in Table I, GIAT exhibits significant superiority over baseline methods. On the Kansas dataset, GIAT achieved an accuracy of 94.7%, surpassing DRSN-GAF by 3.9%. On the Daqing Oilfield dataset, it attained an accuracy of 95.4% and a Kappa coefficient of 0.94—an improvement of 6.5% over DRSN-GAF. This demonstrates its robustness and generalization capability in complex heterogeneous reservoir conditions. GIAT outperforms both ReFormer and ResGAT approaches, underscoring the importance of integrating geological prior information.

| **Model**            | **Dataset**      | **Accuracy (%)** | **Precision (%)** | **Recall (%)** | **Kappa** |
| -------------------- | ---------------- | ---------------- | ----------------- | -------------- | --------- |
| BiLSTM [6]           | Kansas         |      79.3        |     78.1       |   78.9    |   0.73    |
|                      | Daqing Oilfield         |      76.8        |     75.6       |   76.4    |   0.70    |
| ResGAT [5]           | Kansas         |      81.7        |     80.5       |   81.3    |   0.75    |
|                      | Daqing Oilfield         |      79.1        |     77.9       |   78.7    |   0.72    |
| Transformer [2]      | Kansas         |      83.8        |     82.6       |   83.4    |   0.78    |
|                      | Daqing Oilfield         |      81.5        |     80.3       |   81.1    |   0.75    |
| ReFormer [3]         | Kansas         |      86.2        |     85.0       |   85.8    |   0.81    |
|                      | Daqing Oilfield         |      84.1        |     82.9       |   83.7    |   0.78    |
| DRSN-GAF [4]         | Kansas         |      90.8        |     89.6       |   90.4    |   0.87    |
|                      | Daqing Oilfield         |      88.9        |     87.7       |   88.5    |   0.85    |
| **GIAT (Ours)**    | Kansas         |    **94.7**      |   **93.5**     |   **94.3**  | **0.92**  |
|                      | Daqing Oilfield         |    **95.4**      |   **94.2**     |   **95.0**  | **0.94**  |

**Table I. Performance comparison on two datasets. Best results in bold.**
### 1) 定量比较
如表I所示，我们提出的GIAT相对于其他模型具有显著优越性。在堪萨斯州数据集上，GIAT准确率达到94.7%，相比DRSN-GAF提升了3.9%。在大庆油田数据集上，GIAT达到了95.4%的准确率和0.94的Kappa系数，相比DRSN-GAF提升了6.5%，这表明其在复杂非均质储层条件下的鲁棒性和泛化能力。GIAT显著超越了ReFormer和ResGAT方法，证实了地质先验知识整合的重要性。

| **模型**             | **数据集**     | **准确率 (%)** | **精确率 (%)** | **召回率 (%)** | **Kappa** |
| -------------------- | ---------------- | ---------------- | ----------------- | -------------- | --------- |
| BiLSTM [6]           | 堪萨斯州         |      79.3        |     78.1       |   78.9    |   0.73    |
|                      | 大庆油田         |      76.8        |     75.6       |   76.4    |   0.70    |
| ResGAT [5]           | 堪萨斯州         |      81.7        |     80.5       |   81.3    |   0.75    |
|                      | 大庆油田         |      79.1        |     77.9       |   78.7    |   0.72    |
| Transformer [2]      | 堪萨斯州         |      83.8        |     82.6       |   83.4    |   0.78    |
|                      | 大庆油田         |      81.5        |     80.3       |   81.1    |   0.75    |
| ReFormer [3]         | 堪萨斯州         |      86.2        |     85.0       |   85.8    |   0.81    |
|                      | 大庆油田         |      84.1        |     82.9       |   83.7    |   0.78    |
| DRSN-GAF [4]         | 堪萨斯州         |      90.8        |     89.6       |   90.4    |   0.87    |
|                      | 大庆油田         |      88.9        |     87.7       |   88.5    |   0.85    |
| **GIAT (我们的)**    | 堪萨斯州         |    **94.7**      |   **93.5**     |   **94.3**  | **0.92**  |
|                      | 大庆油田         |    **95.4**      |   **94.2**     |   **95.0**  | **0.94**  |
**表I. 不同模型在两个数据集上的总体性能对比。最好的结果以粗体突出显示。**

### 2) Interpretation Faithfulness

Fidelity validation results are presented in Table II. On the Kansas dataset, GIAT achieved PCC of 0.85 and SSIM of 0.82, improving by 19.7% and 7.9% over ReFormer. On the Daqing oilfield dataset, GIAT attained PCC and SSIM of 0.88 and 0.85, enhancing by 31.3% and 18.1% over ReFormer. DRSN-GAF's complex structure generated larger activation changes under perturbations, while Transformer-based models lacked geological constraints, resulting in poor fidelity. These findings demonstrate that geological prior knowledge with fidelity constraints improves classification accuracy and regularizes attention, making interpretations robust for geological decision-making.

| **Model**            | **Dataset**      | **PCC** | **SSIM** |
| -------------------- | ---------------- | ------- | -------- |
| DRSN-GAF [4]         | Kansas         |   0.39    |   0.46    |
|                      | Daqing Oilfield         |   0.36    |   0.43    |
| Transformer [2]      | Kansas         |   0.58    |   0.63    |
|                      | Daqing Oilfield         |   0.54    |   0.59    |
| ReFormer [3]         | Kansas         |   0.71    |   0.76    |
|                      | Daqing Oilfield         |   0.67    |   0.72    |
| **GIAT (Ours)**    | Kansas         | **0.85**  | **0.82**  |
|                      | Daqing Oilfield         | **0.88**  | **0.85**  |

**Table II. Attention faithfulness under input perturbation.**
### 2) 解释忠实度

忠实度验证结果在表II中呈现。在堪萨斯州数据集上，GIAT的PCC达到0.85，SSIM达到0.82，相比ReFormer提升了19.7%和7.9%。在大庆油田数据集上，GIAT的PCC和SSIM分别达到0.88和0.85，相比ReFormer提升了31.3%和18.1%。DRSN-GAF的复杂结构在扰动下产生更大的激活变化，而基于Transformer的模型缺乏地质约束，导致忠实度较差。这些结果表明，地质先验知识结合忠实度约束提高了分类准确性并正则化了注意力机制，使解释在地质决策中更加鲁棒。

| **模型**             | **数据集**     | **PCC** | **SSIM** |
| -------------------- | ---------------- | -------- | -------- |
| DRSN-GAF [4]         | 堪萨斯州         |   0.39    |   0.46    |
|                      | 大庆油田         |   0.36    |   0.43    |
| Transformer [2]      | 堪萨斯州         |   0.58    |   0.63    |
|                      | 大庆油田         |   0.54    |   0.59    |
| ReFormer [3]         | 堪萨斯州         |   0.71    |   0.76    |
|                      | 大庆油田         |   0.67    |   0.72    |
| **GIAT (我们的)**    | 堪萨斯州         | **0.85**  | **0.82**  |
|                      | 大庆油田         | **0.88**  | **0.85**  |
**表II. 注意力图稳定性对比。更高的值表示更好的解释忠实度。**


### 3) Qualitative Visualization

Figure 1 presents lithology prediction comparison for several deep learning models on a challenging interval from the Kansas oilfield dataset. Panels (a)-(e) show the five input well logs (PE, GR, AC, CNL, DEN). Our proposed GIAT (m) achieves superior prediction highly consistent with ground truth lithology (f). In contrast, other models exhibit varying degrees of error. DRSN-GAF (k) incorrectly identifies sandstone layers within mudstone formation around 4565m. This problem is exacerbated in Transformer (i) and BiLSTM (g), which produce fragmented and geologically discontinuous predictions. This visualization underscores our model's architectural superiority in integrating geological context for coherent lithological interpretations.

图1展示了Kansas油田数据集中挑战性层段的多种深度学习模型岩性预测对比。图(a)至(e)为输入的五条测井曲线（PE, GR, AC, CNL, DEN）。我们提出的GIAT（m）取得更优的预测结果，与真实岩性（f）高度一致。相比之下，其他模型表现出不同程度的误差。DRSN-GAF（k）在4565米附近的泥岩层中错误识别砂岩层。该问题在Transformer（i）和BiLSTM（g）中更为严重，它们生成破碎且地质不连续的预测结果。这一可视化凸显了我们模型在整合地质背景知识方面的架构优越性。

To validate interpretation faithfulness, we conducted perturbation experiments as shown in Fig. 2. Panels (b)-(e) show original predictions, while (f)-(k) display results after adding Gaussian noise. DRSN-GAF, Transformer, and ReFormer exhibit poor faithfulness, producing fragmented artifacts under perturbation due to overfitting to noise. Our model maintains structural coherence and geological consistency with ground truth (a), demonstrating superior stability through geologically-informed architecture.

为验证解释忠实度，我们进行了扰动实验，如图2所示。图b-e为原始预测，图f-k为加噪后结果。DRSN-GAF、Transformer、ReFormer表现出较差的忠实度，扰动下产生破碎伪影，表明对噪声过拟合。我们的模型保持结构连贯性和地质一致性，与真实地层(a)相符，体现了地质信息引导架构的卓越稳定性。