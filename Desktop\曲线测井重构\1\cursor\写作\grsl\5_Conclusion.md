<!-- 模板论文字数参考: ~150 words -->


---
**中文翻译:**

# V. CONCLUSION

This work presents GIAT, a novel framework addressing fundamental challenges in deep learning-based lithology identification—lack of domain knowledge constraints in Transformer architectures and shallow application of geological knowledge extraction. Our core innovation transforms data-driven geological priors from CSC filters into explicit attention bias matrices, injected into Transformer's self-attention mechanism to guide toward geologically coherent patterns. Experimental validation demonstrates GIAT's advantages over baseline models. On Kansas dataset, GIAT achieves 94.7% accuracy, surpassing DRSN-GAF by 3.9%. On Daqing Oilfield dataset, GIAT's accuracy exceeds DRSN-GAF by 6.5%. GIAT achieves breakthroughs in interpretation faithfulness, with PCC values improving 46.6% and 62.9% over Transformer. Ablation studies confirm that removing geological bias matrix causes simultaneous degradation in accuracy and faithfulness, demonstrating our approach fundamentally regularizes attention mechanism. While GIAT represents significant progress, limitations exist. Model performance depends on training data quality for CSC filters. The framework requires adaptation for other geological interpretation tasks. Future work will explore diverse geological priors integration and model transferability across geological settings.

---
**中文翻译:**

# V. 结论

本文提出新颖框架GIAT，解决基于深度学习的岩性识别中的根本挑战—Transformer架构缺乏领域知识约束和地质知识提取的浅层应用。我们的核心创新将CSC滤波器的数据驱动地质先验转化为显式注意力偏置矩阵，注入Transformer自注意力机制，引导关注地质连贯模式。实验验证表明GIAT相对于基线模型的优势。在Kansas数据集上，GIAT达到94.7%准确率，超越DRSN-GAF达3.9%。在大庆油田数据集上，GIAT准确率比DRSN-GAF提升6.5%。GIAT在解释忠实度方面实现突破，PCC值相比Transformer分别提升46.6%和62.9%。消融研究证实移除地质偏置矩阵导致准确性和忠实度同时退化，表明我们的方法从根本上正则化注意力机制。尽管GIAT代表重大进步，但局限性存在。模型性能依赖于CSC滤波器训练数据质量。框架需要适应其他地质解释任务。未来工作将探索多样化地质先验整合和模型在地质环境间的可迁移性。