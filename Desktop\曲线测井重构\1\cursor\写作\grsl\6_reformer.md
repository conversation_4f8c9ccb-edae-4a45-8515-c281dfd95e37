REFERENCES

[1] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, "Innovative lithology identification enhancement via the recurrent transformer model with well logging data," Geoenergy Science and Engineering, vol. 240, p. 213015, Sep. 2024.
[2] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, "Quantum-Enhanced Deep Learning-Based Lithology Interpretation From Well Logs," IEEE Trans. Geosci. Remote Sensing, vol. 60, pp. 1–13, 2022.
[3] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, "Application of an Improved U-Net Neural Network on Fracture Segmentation from Outcrop Images," in IGARSS 2022 - 2022 IEEE International Geoscience and Remote Sensing Symposium, 2022, pp. 3512–3515.
[4] <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, "ReFormer: Lithology Identification via the Improved Transformer Model with Well Logging Data," IEEE Geosci. Remote Sensing Lett., pp. 1–1, 2025.
[5] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, "Facies Identification Based on Multikernel Relevance Vector Machine," IEEE Trans. Geosci. Remote Sensing, vol. 58, no. 10, pp. 7269–7282, Oct. 2020.
[6] <PERSON><PERSON>, <PERSON>. <PERSON>, <PERSON>. <PERSON>, and <PERSON>. Feng, "Category-Wise Sequence Correlation Filter for Lithology Identification With Well Logs," IEEE Trans. Geosci. Remote Sensing, vol. 63, pp. 1–14, 2025.
[7] H. Zhang, W. Wu, and X. Song, "Well Logs Reconstruction Based on Deep Learning Technology," IEEE Geosci. Remote Sensing Lett., vol. 21, pp. 1–5, 2024.
[8] S. Guo, N. Yang, C. Guo, D. Zhao, H. Li, and G. Li, "Intelligent Sedimentary Lithofacies Identification With Integrated Well Logging Features," IEEE Geosci. Remote Sensing Lett., vol. 21, pp. 1–5, 2024.
[9] L. Sun, Z. Li, K. Li, H. Liu, G. Liu, and W. Lv, "Cross-Well Lithology Identification Based on Wavelet Transform and Adversarial Learning," Energies, vol. 16, no. 3, p. 1475, Feb. 2023.
[10] V. C. Dodda, L. Kuruguntla, S. Razak, A. Mandpura, S. Chinnadurai, and K. Elumalai, "Seismic Lithology Interpretation using Attention based Convolutional Neural Networks," in 2023 3rd International Conference on Intelligent Communication and Computational Techniques (ICCT), Jaipur, India: IEEE, Jan. 2023, pp. 1–5.
[11] H. Yang, H. Pan, H. Ma, A. A. Konaté, J. Yao, and B. Guo, "Performance of the synergetic wavelet transform and modified K-means clustering in lithology classification using nuclear log," Journal of Petroleum Science and Engineering, vol. 144, pp. 1–9, Aug. 2016.
[12] J. Wang and J. Cao, "A Lithology Identification Approach Using Well Logs Data and Convolutional Long Short-Term Memory Networks," IEEE Geosci. Remote Sensing Lett., vol. 20, pp. 1–5, 2023.
[13] Y. Sun, S. Pang, J. Zhang, and Y. Zhang, "DRSN-GAF: Deep Residual Shrinkage Network (DRSN) for Lithology Classification Through Well Logging Data Transformed by Gram Angle Field," IEEE Geosci. Remote Sensing Lett.
