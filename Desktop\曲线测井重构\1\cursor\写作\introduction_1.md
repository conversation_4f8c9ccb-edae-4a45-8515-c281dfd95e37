引言中的过去工作和related work是有区别的，引言中是通常性怎么做、通用性的做法（不用写的过于具体）。
第二段开头就分类：A类和B类，A类通常怎么做（第一句话、第二句话、第三句话：代表性工作有哪些、第四句话：然而however这类方法的问题，忽视了什么，存在什么问题、第五句话：而这个问题是我们这个任务不可缺少的，为此我们应该去考虑）

第三段：顺承写法：
第一句话：为了解决上述问题，另一类方法B类方法提出了什么什么思路，基于什么去做。
第二句话：这类方法通常怎么怎么去做。
第三句话：代表作有哪些。
第四句话：然而他们又产生什么新的问题，忽视了什么，存在什么问题
第五句话：为此我们应该怎么去考虑这个问题

并列写法：开头直接写另一类方法怎么做，则产生两个问题（第二段、第三段），同步去解决 。如果找不到第二个问题，可以写第二个方法同样产生了这个问题，为此我们应该怎么做。
顺承最终只有一个问题，并列最终指向两个问题（设计两个模块）。分类与方法有关

第四段：第三章方法method的缩写


1.	背景（意义、价值、有什么好处、为什么要做这个任务）
2.	过去工作（2、3两段有两种写法：顺承、并列）（要加入作者思考，分类：特征角度（全局特征、局部特征）、技术角度（非扩散模型和扩散模型的））  
eg：我们可以把过去的目标检测方法简单分为基于transformer和非基于transformer的方法，或者基于transformer和基于CNN的方法
3.	下一类的过去工作
4.	我们的贡献