# Well Logs Reconstruction Based on Deep Learning Technology  

<PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON>, and <PERSON><PERSON>bstract— Well logs play a very important role in formation evaluation, but due to the influence of geological, engineering, and other factors in practical applications, some logs are often distorted or missing. The log curve reconstruction methods based on traditional empirical model and statistical analysis are unconvincing enough, so it is proposed to reconstruct the log curves by deep learning method. Considering the limitation of neural network, long short-term memory (LSTM) neural network is studied, and we utilize the formation lithology index (FLI) of logging domain knowledge (DK) to filter data. As a result, highquality training sample data are acquired, which are used as the basis for deep learning to reconstruct log curves. Meanwhile, the LSTM model with DK constraint layer is constructed and trained. Then, particle swarm optimization (PSO) algorithm is chosen to optimize the LSTM hyperparameters—initial learning rate and dropout probability. Finally, a log curve reconstruction model (PSO-DK-LSTM) combining DK and PSO algorithm is created. We design one experiment to verify prediction capability of PSO-DK-LSTM. The whole data are collected from a work area of XX oilfield. The verification results show that the log reconstruction method based on PSO-DK-LSTM has achieved better results in terms of accuracy and stability, which gives a new idea for log reconstruction.  

Index Terms— Deep learning, particle swarm optimization (PSO) algorithm, PSO-domain knowledge (DK)-long short-term memory (LSTM), well log reconstruction, well logging DK.  

# I. INTRODUCTION  

N THE field of petroleum exploration, it is essential to measure many different types of well logs to reduce the multiplicity of geological interpretation. However, distorted or missing logs are often encountered for a variety of reasons, such as borehole enlargement or tool stuck. Early boreholes may also be missing important logs due to incomplete logging methods, which can make geological interpretation difficult. Without machine learning, the researchers proposed to establish some models based on geological information to predict oil and gas field production [1] and inverse well logs [2]. The problem lies in that some physical models are established under numerous assumptions defined by the researchers themselves. They are not based on the real complex formation conditions. Those models are also mixed with the subjective considerations of the researchers, which may lead to big  

differences between the generated and the real well logs. Moreover, other researchers utilize different internal relations between various logging data to reconstruct well logs through multiple regression analysis method. The complexity of the subsurface geology is hard to express with a deterministic function. Therefore, the reconstruction of well logs based on statistical analysis usually has low accuracy, which cannot meet the needs of accurate interpretation of log data and fine description of reservoirs.  

Recently, deep learning methods have gradually attracted more attention in the fields of well logging. Researchers have presented many efforts to predict gas well fluid accumulation, porosity, and hydrate formation in carbonate reservoirs. It also provides a new means for well log reconstruction [3], [4], [5], [6], [7]. Furthermore, several geoscience applications have employed conventional fully connected neural network techniques for the reconstruction of well logs. This method can reflect the nonlinear relationship between log data, but it ignores the influence of the logs with depth and the context of data. In particular, the problem of sequence data cannot be effectively processed, which violates the idea of geological analysis. Thereby, the accuracy of the logs generated by the method needs to be further improved. Since the deposition of formation is time-sequential, the log curves are the response of formation depositional characteristics and have certain temporal characteristics. A common data-driven deep neural network structure is the LSTM. The major distinction from the fully connected neural network is that each hidden layer unit is not independent. LSTM neural network can extract the feature data of logging sequence from forward direction and backward direction and fully utilizes the dependent information in the front and back sequences to reconstruct logs. Hence, LSTM is an ideal model architecture for generating artificial log data. To date, several scholars have done lots of work [8], [9], [10], [11], [12], [13]. The LSTM-generated curves take into account the variation trend and contextual correlation of well logs along depth, while there are two key problems in reconstructing log curves with LSTM model. First, the combination of LSTM and engineering practice is too direct, with little reference to specific domain knowledge (DK). Subsequently, for data-driven methods, how to obtain appropriate parameters to enhance the training effect and the final prediction accuracy has always been a major point. In the past research work, it is often obtained through previous experience, and the randomness error is large.  

For the first problem, DK can be introduced to break the above bottleneck [14], so the accuracy of log reconstruction is improved by referring to logging DK. For the second problem, this letter optimizes the curve reconstruction model by particle swarm optimization (PSO) algorithm, which can adaptively optimize the initial learning rate and dropout probability in limited iterations. Ultimately, the proposed PSODK-LSTM network is to be applied to reconstruct the well logs. The reconstruction model is established through TensorFlow framework. Meanwhile, it is compared to the LSTM and DK-LSTM methods and validated using the measured data.  

# II. METHODOLOGY  

# A. Well Logging DK  

DK refers to a collection in a specific field, which specifically includes related concepts in this field, a certain relationship between concepts, and constraints on some concepts [15]. The knowledge content involved in logging DK is complex and has many types. It can be divided into factbased knowledge, process-based knowledge, instance-based knowledge, and meta-knowledge.  

1) Factual knowledge refers to basic information, such as parameters, templates, and plates in logging data.   
2) Process knowledge refers to business logic and domain rules in the logging process.   
3) Instance knowledge refers to individual instances of concepts, such as data from a well.   
4) Meta-knowledge is the knowledge that describes the above types.  

Combined with expert experience, factual knowledge is introduced into the log reconstruction model. The formation lithology index (FLI) is used as an optimization strategy for filtering discrete data, so as to obtain high-quality training samples and regard them as the basis for reconstructing log curves. In standard LSTM model, a fully connected simulation layer is constructed to add logging DK constraints. By introducing the most relevant logging DK of statistical analysis, a priori constraint is added to the log reconstruction model. Accordingly, we fully consider the prior knowledge of the logging field to construct the log reconstruction model, which can get higher prediction accuracy. When DK is applied to LSTM, its network structure is displayed in Fig. 1.  

# B. PSO-DK-LSTM Log Reconstruction Model  

PSO-DK-LSTM log reconstruction model mainly includes two parts: DK-LSTM neural network and PSO parameter optimization. Fig. 2 depicts the PSO-DK-LSTM model’s algorithm flow. Apparently, DK-LSTM neural network is an improvement of LSTM network. In addition, PSO algorithm acts on the output process of DK-LSTM neural network to optimize the initial learning rate and dropout probability for modeling [16], [17].  

Compared with standard LSTM neural network, DK-LSTM introduces DK constraint layer by changing network structure. It adds prior information in the logging domain to the reconstruction model. The essence of constraint layer is to compress and then expand data, that is, performs nonlinear transformation. At the same time, according to the correlation analysis results, the most relevant feature data are input into the model again to improve the model’s prediction accuracy. It should be clear that the neurons of knowledge constraint layer only refer to log data from structure and do not participate in the fitting and training of model.  

![](images/e1817fca089d108c58a329af1316bb7c577d7287e51af762e5d7cab1fa06df07.jpg)  
Fig. 1. DK-LSTM structure schematic.  

![](images/522742b4308b29902135bfa0c227a14fbb6ddd68787bbc15b93f33241f7b4a6d.jpg)  
Fig. 2. PSO-DK-LSTM flowchart.  

PSO-DK-LSTM in this letter organically integrates the advantages of three methods. It adaptively optimizes key parameters while retaining the excellent training and prediction performance of LSTM and also refers to specific logging DK for prediction. The inputs are the original logs and the outputs are the missing logs. According to the analysis above, the detailed steps are as follows.  

Step 1: Input sequence data related to well logs, including CAL, RT, DEN, and other logs and FLI. The z-score method is adopted to standardize data and divide the training set and test set (prediction set). Moreover, we will subdivide the training set into DK-LSTM training set and PSO optimization set in this letter.  

Step 2: After data set allocation is completed, the second step is to initialize PSO algorithm and randomly generates a particle population. The particle dimension is set to 2-D—initial learning rate and dropout probability.  

Step 3: Next, in each iteration, use the DK-LSTM training set as the new training set and the PSO optimization set as the new test set to simulate DK-LSTM prediction process. In the meantime, the solution (particle) that minimizes the prediction error is selected after all iterations.  

Step 4: When the end condition is reached, output the optimal solution of the historical population. This solution represents the best initial learning rate and dropout probability. In the end, the log reconstruction process of prediction set is carried out. So, the reconstruction results are obtained.  

# C. Data Preparation  

The experimental data come from one horizontal well in a work area of XX Oilfield, namely, Well A. Well A includes eight main logs: compensated neutron (CNL), density (DEN), resistivity (RT), acoustic (AC), gamma ray (GR), spontaneous potential (SP), photoelectric effect (PE), and caliper (CAL). Before data standardization, the experimental data are screened based on well logging DK. When the FLI is introduced, log data can be distinguished by setting the extraction values of different lithology categories. To ensure data sufficiency, it is crucial to analyze the user-defined extracted values to get a reasonable set of extracted values. Then, we select the labeled data under this set as the final experimental data. Determining the extraction value is based on the lithology category of formation and is within lithology interval (01, 02, 03). Among them, 01 represents mudstone, 02 represents siltstone, and 03 represents fine sandstone. According to the reference change results, the labeling results of samples under all set extraction values are counted. Thereby, discrete data are eliminated and a high-quality dataset is acquired by comprehensively considering formation lithology categories. Table I displays several representative sample data.  

We prepare the well logs generation experiment to validate the performance of PSO-DK-LSTM model. Assuming that a certain log curve of Well A is completely missing, the missing log curve is integrally reconstructed by utilizing the existing log curves. In this way, the ability of PSO-DKLSTM to automatically reconstruct missing logs from relevant information is demonstrated. Subsequently, it is analyzed and compared with LSTM and DK-LSTM models.  

# D. Evaluation Indicators  

Evaluation indicators are used to quantitatively assess the performances of these models, and the evaluation indicators mainly measure the difference between the reconstructed log and real log. Three evaluation indicators are RMSE, MAE, and $R ^ { 2 }$ in this study  

$$
\begin{array} { r l } & { \displaystyle \mathrm { R M S E } = \sqrt { \frac { \sum _ { i = 1 } ^ { n } \big ( X _ { \mathrm { p r e } , i } - X _ { \mathrm { o b s } } \big ) ^ { 2 } } { n } } } \\ & { \displaystyle \mathrm { M A E } = \frac { 1 } { n } \sum _ { i = 1 } ^ { n } \big | X _ { \mathrm { p r e } , i } - X _ { \mathrm { o b s } } \big | } \\ & { \displaystyle R ^ { 2 } = 1 - \frac { \sum _ { i = 1 } ^ { n } \big ( X _ { \mathrm { p r e } , i } - X _ { \mathrm { o b s } } \big ) ^ { 2 } } { \sum _ { i = 1 } ^ { n } \big ( \bar { X } - X _ { \mathrm { o b s } } \big ) ^ { 2 } } . } \end{array}
$$  

The lower the RMSE, the more stable the model. The smaller the MAE, the more accurate the model. The more closely $R ^ { 2 }$ approaches 1, the better the model’s overall performance.  

TABLE I PARTIAL LEARNING SAMPLE DATA OF WELL A   


<html><body><table><tr><td>AC</td><td>GR</td><td>RT</td><td>CNL</td><td>DEN</td><td>PE</td><td>FLI</td></tr><tr><td>114.02</td><td>110.23</td><td>29.50</td><td>35.47</td><td>2.67</td><td>5.14</td><td>01</td></tr><tr><td>115.21</td><td>112.58</td><td>35.67</td><td>36.75</td><td>2.48</td><td>4.51</td><td>01</td></tr><tr><td>63.14</td><td>52.49</td><td>14.98</td><td>25.67</td><td>2.34</td><td>4.02</td><td>02</td></tr><tr><td>69.87</td><td>52.78</td><td>16.64</td><td>24.74</td><td>2.33</td><td>3.94</td><td>02</td></tr><tr><td>72.45</td><td>47.42</td><td>4.38</td><td>20.75</td><td>2.24</td><td>3.91</td><td>03</td></tr><tr><td>73.11</td><td>45.34</td><td>2.30</td><td>18.88</td><td>2.11</td><td>3.82</td><td>03</td></tr></table></body></html>  

# III. EXPERIMENTS AND RESULTS  

In the experiment, the real log data of Well A is known. Two other models, including DK-LSTM and LSTM, are introduced to highlight the experimental results. We reconstructed the POR (porosity) log in the same well and compared the prediction accuracy by three models. It should be noted that the POR log is only reconstructed as the target log to be reconstructed, which is assumed to be lost. First, the original log data are screened, which are CNL, DEN, RT, AC, GR, SP, PE, and CAL. As we know, the well depth of logging section is $1 7 0 0 { - } 2 6 0 0 \ \mathrm { m }$ , the total length is $9 0 0 \mathrm { ~ m ~ }$ , and the sampling interval is $0 . 1 \mathrm { ~ m ~ }$ . The training set consists of $1 7 0 0 { - } 2 4 0 0 { - } \mathrm { m }$ well section, and the test set consists of $2 4 0 0 { - } 2 6 0 0 { - } \mathrm { m }$ well section. There are 550 groups of training data and 60 groups of test data. The three models (LSTM, DK-LSTM, and PSO-DKLSTM) all contain two long and short-term memory layers, with a total of 100 hidden units in each layer. Additionally, in order to prevent overfitting, the initial learning rates of LSTM and DK-LSTM are set to 0.01, and a dropout operation with 0.5 dropout rate is added to two models. Due to the addition of PSO algorithm, the initial learning rate and drop rate of PSO-DK-LSTM are adaptively optimized.  

Before model training, we perform feature extraction work to simplify calculation. Fig. 3 depicts the correlation between the well logging parameters and the real porosity of Well A. Apparently, CNL has the highest correlation with porosity, and the other logging parameters have the correlation with porosity in the descending order of DEN, AC, CAL, RT, PE, SP, and GR. According to the correlation order in Fig. 3, feature elimination selection is carried out. At first, we delete the GR feature parameter with the least correlation, input the remaining seven parameters, and use these data to train every model. Second, delete the SP feature parameter, and input the remaining six groups of feature parameter data to train the models. Subsequently, the data of PE, RT, CAL, AC, and DEN are sequentially deleted until the remaining CNL data are deleted. The RMSE values of LSTM, DK-LSTM, and PSO-DK-LSTM methods are counted; then, the results are displayed in Fig. 4. When CNL, DEN, and AC logging parameter data are used as an input, the RMSE value of PSO-DK-LSTM method is the lowest, revealing that the log reconstruction accuracy is the highest. On the whole, PSO-DK-LSTM method has higher reconstruction accuracy than the other two deep learning methods when different parameters are input.  

Under the condition of inputting CNL, DEN, and AC logs, LSTM, DK-LSTM, and PSO-DK-LSTM methods are applied to reconstruct the POR log, respectively. Fig. 5 shows the comparison between the reconstructed log and real log. As can be seen, all three models have large reconstruction deviations in the range of about $17 \%$ porosity. For this case, because this part of true porosities are extreme values in the whole bottom section, it is normal for the reconstructed values to deviate greatly during the whole POR log reconstruction process, and the reconstructed values of the whole log in PSO-DK-LSTM model are roughly the same as the real values, which achieve the expected goal. Table II records the evaluation indicator data of the prediction results by three models. This also fully confirms this point.  

![](images/667b525a912b4e749c5de64de56c70d8dff5626effdfc80d069299ea03b89a92.jpg)  
Fig. 3. Correlation diagram between eight kinds of logging data and real porosity in Well A.  

![](images/a5898df619c6b15c232347020d13ba301909bbb7f2bf7da8838e36805fadaefd.jpg)  
Fig. 4. RMSE variation values of correlation feature parameter number.  

# IV. MORE DISCUSSION  

Compared with traditional LSTM model from Fig. 5, the use of DK-LSTM and PSO-DK-LSTM models for well logs reconstruction is particularly useful. They can basically reflect the changing trend of logs in distorted or missing intervals, and the reconstruction effect is better without abrupt intervals. Apparently, DK structure is introduced into two models. It proves that the prior knowledge of well logging domain can effectively enhance the prediction accuracy of reconstruction models.  

![](images/c02948e1681ea6125a2e0666c1f289efb91a8483df80e5bda2b09aaa84073068.jpg)  
Fig. 5. Comparison of POR logs generated by different models. $\mathrm { L S T M _ { P O R } }$ represents the POR log generated by LSTM model; DK-LSTMPOR represents the POR log generated by DK-LSTM model; PSO-DK-LSTMPOR represents the POR log generated by PSO-DK-LSTM model.  

TABLE II COMPARISON OF EVALUATION INDICATORS FOR POR LOG BASED ON DIFFERENT MODELS IN WELL A   


<html><body><table><tr><td>Model</td><td>RMSE</td><td>MAE</td><td>R²</td></tr><tr><td>LSTM</td><td>0.4182</td><td>0.3621</td><td>0.7254</td></tr><tr><td>DK-LSTM</td><td>0.2473</td><td>0.2077</td><td>0.8461</td></tr><tr><td>PSO-DK- LSTM</td><td>0.0304</td><td>0.0401</td><td>0.9822</td></tr></table></body></html>  

Besides, another fact is uncovered that PSO-DK-LSTM performs better than DK-LSTM on the reconstruction result, especially at the abrupt intervals of log data (the POR log near the depth of $2 5 4 2 \substack { - 2 5 6 5 \mathrm { ~ m ~ } }$ in Fig. 5). These abrupt intervals have one thing in common: the well log data to be reconstructed suddenly increase or decrease, but the variation range of DK-LSTM model is slight, so that DK-LSTM model cannot successfully estimate those abrupt changes in target logs and generates logs with large deviation. The reason for this phenomenon is presented as follows: during the model fitting process, the parameters of DK-LSTM cannot be adjusted to the optimal parameters, which leads to the fact that the reconstructed logs cannot achieve the desired effect when there is a large difference between reconstructed values. After introducing PSO algorithm, it could be hardly known that PSO-DK-LSTM model not only consistent with the real values at the abrupt intervals but also has smaller error than other two models on the whole. Taking well A as an example, compared with LSTM and DK-LSTM, its RMSE decreases by $9 3 . 7 3 \%$ and $8 7 . 7 1 \%$ , respectively, MAE decreases by $8 8 . 9 3 \%$ and $8 0 . 6 9 \%$ , respectively, and $R ^ { 2 }$ increases by $3 5 . 4 0 \%$ and $1 6 . 0 9 \%$ , respectively. The fact reveals that the accuracy and stability of the well logs generated by PSO-DK-LSTM are the best.  

Actually, PSO-DK-LSTM can be extended to reservoir parameter prediction, such as inputting multiple logs with high correlation coefficients to predict permeability and TOC values. At the same time, it also gives us an enlightenment: the introduction of DK structure and PSO algorithm in other neural networks can not only take into account the importance of well logging DK but also ensure that the model parameters are adjusted to the optimum, which can eliminate the uncertainty of manual parameter adjustment. Moreover, future work will also further study the correction methods of PSO-DK-LSTM model under more complex conditions.  

# V. CONCLUSION  

We used LSTM as the basic regression model to reconstruct the missing well logs in the logging process. Before the training process, in combination with DK, we proposed to utilize the FLI in the logging DK to filter data. As a result, high-quality training sample data are acquired, which are used as the basis for deep learning to reconstruct log curves, and the LSTM network model with DK constraint layer is constructed and trained. Then, PSO method is used to optimize the LSTM hyperparameters—initial learning rate and dropout probability. Accordingly, PSO-DK-LSTM model is proposed to generate and complement the distorted or missing information in the well logs. The fact shows when the model is applied to the well logs generation experiment, the reconstructed values of log curves are in good agreement with the real values. In comparison with LSTM and DK-LSTM, PSO-DK-LSTM provides the best accuracy and stability in terms of reconstructing well logs. PSO-DK-LSTM considers a variety of logging information responses, strengthens the extraction of relevant logging information, and reduces the error of parameter adjustment. It has an important reference significance for the reconstruction of well logs in complex formations.  

# ACKNOWLEDGMENT  

The authors would like to thank Professor Xinjun Zhang for his guidance on PSO algorithm through e-mail and some data support from him.  

# REFERENCES  

[1] C. Liu, S. Wang, and Y. Liu, “A new method for the application of oil and gas production prediction models,” Oil Gas Geol., vol. 30, no. 3, pp. 384–387, Jun. 2009.  

[2] Y. Qian, W. Hu, and Y. Wang, “High-resolution inversion of acoustic logging curves,” J. Jianghan Pet. Inst., vol. 12, no. 3, pp. 33–38, Sep. 2009.   
[3] M. Liao, “The influence of diameter expansion on density curve and acoustic curve corrected by multiple regression method,” Geophys. Geochem. Explor., vol. 38, no. 3, pp. 174–184, Jan. 2014.   
[4] M. M. Salehi, M. Rahmati, M. Karimnezhad, and P. Omidvar, “Estimation of the non records logs from existing logs using artificial neural networks,” Egyptian J. Petroleum, vol. 26, no. 4, pp. 957–968, Dec. 2017.   
[5] B. Alizadeh, S. Najjari, and A. Kadkhodaie-Ilkhchi, “Artificial neural network modeling and cluster analysis for organic facies and burial history estimation using well log data: A case study of the south pars gas field, Persian gulf, Iran,” Comput. Geosci., vol. 45, pp. 261–269, Aug. 2012.   
[6] Y. Z. Ma, E. Gomez, and B. Luneau, “Integration of seismic and well-log data using statistical and neural network methods,” Lead. Edge, vol. 36, no. 4, pp. 324–329, Apr. 2017.   
[7] Y. Zhang, Y. Qu, and Z. Chen, “Application of neural network technology in well logs reconstruction,” J. Jianghan Pet. Inst., vol. 33, no. 3, pp. 89–94, Mar. 2011.   
[8] S. Yuan, X. Jiao, Y. Luo, W. Sang, and S. Wang, “Double-scale supervised inversion with a data-driven forward model for lowfrequency impedance recovery ell logs reconstruction technology based on genetic neural network algorithm,” Geophysics, vol. 87, no. 2, pp. 1–6, Mar. 2022.   
[9] W. Zhu, X. Li, C. Liu, F. Xue, and Y. Han, “An STFT-LSTM system for P-wave identification,” IEEE Geosci. Remote Sens. Lett., vol. 17, no. 3, pp. 519–523, Mar. 2020.   
[10] S. Hochreiter and J. Schmidhuber, “Long short-term memory,” Neural Comput., vol. 9, no. 8, pp. 1735–1780, Nov. 1997.   
[11] R. Huang et al., “Well performance prediction based on long shortterm memory (LSTM) neural network,” J. Petroleum Sci. Eng., vol. 208, Jan. 2022, Art. no. 109686.   
[12] S. Kouadri, C. B. Pande, B. Panneerselvam, K. N. Moharir, and A. Elbeltagi, “Prediction of irrigation groundwater quality parameters using ANN, LSTM, and MLR models,” Environ. Sci. Pollut. Res., vol. 29, no. 14, pp. 21067–21091, Nov. 2021.   
[13] S. Nitish, E. Geoffrey, K. Alex, S. Ilya, and S. Ruslan, “Dropout: A simple way to prevent neural networks from overfitting,” J. Mach. Learn. Res., vol. 15, no. 1, pp. 1929–1958, Jan. 2014.   
[14] Y. Chen, H. Chang, J. Meng, and D. Zhang, “Ensemble neural networks (ENN): A gradient-free stochastic method,” Neural Netw., vol. 110, pp. 170–185, Feb. 2019.   
[15] J. Wu. (2019). Research on Emotional Cause Discovery Combined With Domain Knowledge. Harbin Inst. Technol, Harbin, China. Accessed: Jan. 6, 2021, doi: 10.27061/d.cnki.ghgdu.2019.006455.   
[16] R. Ma, T. Yang, E. Breaz, Z. Li, P. Briois, and F. Gao, “Datadriven proton exchange membrane fuel cell degradation predication through deep learning method,” Appl. Energy, vol. 231, pp. 102–115, Dec. 2018.   
[17] S. Lalwani, H. Sharma, S. C. Satapathy, K. Deep, and J. C. Bansal, “A survey on parallel particle swarm optimization algorithms,” Arabian J. Sci. Eng., vol. 44, no. 4, pp. 2899–2923, Apr. 2019.  