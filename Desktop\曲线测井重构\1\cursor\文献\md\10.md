# Seismic Lithology Interpretation using Attention based Convolutional Neural Networks

Vineela Chandra Dodda Department of ECE SRM University AP Andhra pradesh,India <EMAIL>

Lakshmi Kuruguntla Department of ECE SRM University AP Andhra pradesh,India <EMAIL>

Shaik <PERSON>zak Department of ECE SRM University AP Andhra pradesh, India <EMAIL>

Anup Mandpura Department of Electrical engineering Delhi Technological University New Delhi, India <EMAIL>

Sunil Chinnadurai Department of ECE SRM University AP Andhra pradesh, India <EMAIL>

Karthikeyan Elumalai Department of ECE SRM University AP Andhra pradesh, India <EMAIL>

Abstract- Seismic interpretation is essential to obtain information about the geological layers from seismic data. Manual interpretation, however, necessitates additional pre- processing stages and requires more time and effort. In recent years, Deep Learning (DL) has been applied in the geophysical domain to solve various problems such as denoising, inversion, fault estimation, horizon estimation, etc. In this paper, we propose an Attention- based Deep Convolutional Neural Network (ACNN) for seismic lithology prediction. We used Continuous Wavelet Transform (CWT) to obtain the time- frequency spectrum of seismic data which is further used to train the network. The attention module is used to scale the features from the convolutional layers thus prioritizing the prominent features in the data. We validated the results on blind wells and observed that the proposed method had shown improved accuracy when compared to the existing basic CNN.

Index Terms- Deep learning, Lithology prediction, seismic data, Interpretation

# I. INTRODUCTION

Seismic Exploration is a cost effective method used to identify the hydrocarbons and minerals in the earth layers'. The seismic exploration process includes the four basic steps: acquisition, processing, inversion and interpretation. The acquisition step is used to get the raw seismic data from the earth layers, processing step is utilized to attenuate the noise present in the raw data. Whereas inversion step is used to retrieve the physical properties of the earth layers from the seismic reflection data and finally the last step is the interpretation which is used to transform the data in seismic sections into geological information. In [1], authors defined that the seismic interpretation acts as an interface between the exact processed seismic data and inexact geological data. The seismic interpretation process converts the velocity and time of subsurface reflecting layers into depth form which can translate the seismic data into geological images [2]. There are three different types of interpretations; structural interpretation, stratigraphic interpretation and lithological interpretation. Structural interpretation is used to get the structural maps of sub- surface layers with respect to observed arrival times of reflected data. Stratigraphic interpretation gives the pattern of reflections in sub- surface layer. Whereas lithological interpretation is used to identify the potential hydrocarbon- bearing zones in each layer of the earth's sub- surface. Accurate and detailed information of the structural, stratigraphic and lithology interpretation depends on the seismic attributes [3]. There are different types of seismic attributes which includes: instantaneous amplitude, frequency, phase and bandwidth. There are various parameters in Interpretation such as Volume of shale, Porosity, permeability, hydrocarbon saturation, effective porosity, and fluid content which are the important parameters that should be considered for any type of interpretation.

From literature, we infer that methods for seismic lithology prediction can be broadly classified into two; conventional and data- driven methods. In conventional methods, interpretaion is based on physical relation between the seismic attributes and the geological targets [4]- [8]. Whereas in data- driven methods, the statistical relationship between seismic attributes and geological factors is derived using machine learning techniques [9] [10]. Recently, with the rapid development of GPU's and huge availability of data, the data- driven methods gained popularity in various areas such as computer- vision, natural language processing, image and speech recognition etc. Machine learning (ML) techniques can learn nonlinear correlations implicitly from a large number of labels and are usually not constrained as traditional methods, which are based on signal processing theories or physics- based equations. In reservoir characterization, ML techniques such as Support Vector Machine (SVM), Random forests and Deep Learning (DL) has been used to predict permeability, porosity, saturation and volume of sand, shale etc etc [10]- [13]. Here, the

TABLEI SUMMARY OF LITERATURE WORK FOR SEISMIC LITHOLOGY PREDICTION  

<table><tr><td>Ref.No</td><td>problem</td><td>Network/Algorithm</td></tr><tr><td>[10]-[13]</td><td>Seismic liquefaction potential, Prediction of porosity and water saturation, Seismic event classification</td><td>SVM</td></tr><tr><td>[16]</td><td>Salt body classification</td><td>CNN</td></tr><tr><td>[17]</td><td>High resolution 3D porous media reconstruction</td><td>GAN</td></tr><tr><td>[18]</td><td>Seismic facies prediction</td><td>DCAE</td></tr><tr><td>[19]</td><td>Sand shale classification</td><td>CWT-CNN</td></tr></table>

problem of prediction can be either regression, classification or clustering. Artificial Neural networks (ANN) have been used to predict reservoir properties however ANN have issues like over fitting, parameter selection and vanishing gradient problem. On contrary, DL has advantages such as usage of pre- trained models, novel activation functions, GPU functionality etc over ANN. There are many architectures in DL; feed forward neural networks, CNN, Generative Adversarial Network (GAN) and Recurrent neural Network (RNN) [14], [15]. In [16], CNN is used to extract attributes and perform salt body classification from seismic data.

Further in [17], GANs are used to reconstruct high resolution 3D porous media at different scales. In [18], Deep Convolutional Auto encoder (DCAE), an unsupervised approach is used to predict the seismic facies from the prestack seismic data. Whereas in [19], CWT- CNN was used to predict seismic lithology. Furthermore, various network architectures are compared and found that Continuous Wavelet Transform (CWT)- CNN had better performance compared to the other architectures. A brief summary of the literature for seismic lithology prediction is given in TABLE I. In all the aforementioned methods, accuracy and time to train the network are the important factors to consider while interpreting about the seismic lithology.

Therefore, to improve the accuracy and reduce the time for training, we used attention based CNN for seismic lithology prediction in specific sand and shale classification. The wavelet transformed data is used to train the network thus making use of full frequency spectrum of the data. Inspired by the human nature i.e., we add an attention mechanism called squeeze and excitation block to the CNN architecture which further increase the attention and overall performance of seismic lithology prediction. Moreover, the added attention mechanism can model the global information and can handle long- range dependencies [20]. We compared the proposed method with the existing methods and observed an increase in the accuracy of prediction.

In section II, we give the proposed methodology which describes about the network architecture followed by the results in III. Finally, the conclusion is mentioned in the section IV.

# II. METHODOLOGY

# A. Overview of Network Architecture

Deep Learning (DL) techniques are based on learning and recognizing the relationships in the data, similar to operations inside the human brain. From literature, we observe that the commonly used neural network architectures in seismic data denoising and reconstruction are either based on Fully Connected Networks (FCN) or convolutional neural networks.

CNN, on the other hand, is employed in a wide range of real world applications. CNN can recognize the patterns in the data, which is important for classification, object identification, and natural language processing. The CNN architecture is built by stacking three main layers: Convolution, Pooling and Fully- connected layer. CNNs operate by utilizing layers with filters positioned along each dimension gathering particular information about the visual field. In the training process of CNN, the filter is shifted over the input volume's width and height during the forward pass to generate an activation map that contains the responses of that filter at each spatial point. The stacking of conv- pool layers is used to identify complex features from previous layers. The pooling layers are used to get downsampled data and reduce the computational complexity, while preserving the input features. The output is now flattened using fully- connected layer and back propagation is performed using chosen loss function on each training cycle for the specified number of epochs.

# B. Proposed Network Architecture

In our work, we propose to use Attention based CNN for seismic lithology prediction. The ACNN consists of two series of input, convolution, pooling, fully connected, attention and output layers. The input layer is fed with CWT spectrum data which are 2D matrices, where one sample is shown in Fig. 2. The output from feature maps of convolution layers are fed as an input to the fully connected layer which aids in classification. After each series of convolution layer, attention module is used to scale the feature maps. In general, the channels of the network are given equal priority while obtaining the feature maps. This mechanism is altered with the addition of attention module which weighs the channels adaptively. The input to the attention module is convolutional layer and number of channels in that particular layer. Then average pooling is used to squeeze each channel to obtain single value. To introduce non- linearity, we use two fully connected layers with an activation function. Finally, the output of attention module is obtained by multiplying the output of excitation layers with the input of the attention module which is given as input to the next convolution layer. In addition, the advantage of attention module is to avoid the vanishing gradient problem and to provide better explainability about the model. In order to preserve the features, the final convolution layer is not followed by

![](images/660e09f7e5951fe8917f5192645dd956099d7f4fb761416b9f9bf08784194555.jpg)  
Fig. 1. Proposed ACNN architecture

![](images/f830eada0b127b04595da2ecdf2df050aff47d1563f557b8d10d9e0f93735ad3.jpg)  
Fig. 2. Sample of 2D spectrum map obtained from Continuous Wavelet Transform (CWT)

activation and pooling layers. We used softmax to calculate the probabilities of the output.

# III. NUMERICAL RESULTS

In this section, we demonstrate the various numerical results obtained using proposed ACNN method on blind wells. The dataset (well and post- stack seismic data) is from the eastern slope of the Chuanxi Depression in the Sichuan Basin, SW China.

![](images/c16de4707735d67c815563c73369bffb703443f44365bc8de84754e1ec6c48ee.jpg)  
Fig. 3. Results of ACNN on blind test well JS7

![](images/f17b2a91b4461b31f0d28b4349f460297ac1660a7ea1ec8e991cdc99e8742923.jpg)  
Fig. 4. post-stack seismic data

![](images/b7dbc0458ddd325bc7fc6ad0fe7cb082b4a97f51a5db70252d0916a9955dee00.jpg)  
Fig. 5. Sand probability and lithology

![](images/87c419c261cf5ef382196b6bbae82e4ed000041e482ee70982408382997ec52d.jpg)  
Fig. 6. Sand probability of well JS7

![](images/f6553b252e6b718b6a33a4a041863db3d24709867df7c9005d095458cfb258ef.jpg)  
Fig. 7. Analysis plots of Epochs Vs Accuracy, Loss, Learning rate

![](images/671d5fb2dec207585b01dbc95e5fc44812869f220d50575b5a5c327c2c710796.jpg)  
Fig. 8. Plots of True Positive and False positive rates

![](images/774e0c604fac0f2fd77dda45c64b450769c96438e15162aadf54071666ce61ae.jpg)  
Fig. 9. F1 scores of training, validation and test data

This river- delta region is made up of tightly packed sands. A total of 3720 samples from 13 wells were used for modelling. The complete data set is separated into three subsets: training, validation, and blind testing. The model is trained using the validation and training data sets. The blind testing data set i.e., well JS7, is used for the final model evaluation. We used  $40\%$  of data as validation and remaining as training data after checking model performance with various train- test ratios. The ratio of sand samples is very less compared to the ratio of shale. Hence, the minority sand category is over sampled to two times to balance the training and validation set. Fig. 4 shows the post- stack seismic data for which CWT is computed and fed as input to train the network. Figs. 3,5,6 shows the predicted sand probabilities with the proposed ACNN for well JS7. The model is built using Tensorflow deep learning library using python 3.8 on server with configuration Intel® Xeon® Silver 4216 CPU @2.10 GHz (2 processors) with 256 GB RAM, 64- bit operating system. The ACNN network is trained with batch size of 40 and with adaptive learning rate i.e., when the validation error stops decreasing, the learning rate also decreases by four. The epochs is set as 1000, however if learning rate does not decrease two times, training is stopped. We used Binary cross entropy as the cost function and Adam as the optimization algorithm during the training of network. We tuned various hyper parameters and selected the optimal parameters to train the network. aZXSDZ

The performance of the model is evaluated based on confusion matrix, which is widely used metric for classification. There are four values in confusion matrix; TN, FN, FP and TP. From these values, we calculate, accuracy, recall, precision and F1 score which are defined as follows.

$$
\begin{array}{r}A c c u r a c y = T P + T N / (P + N)\\ r e c a l l = T P / T P + F N\\ p r e c i s i o n = T P / (T P + F P)\\ F1 = 2.T P / (2.T P + F N + F P) \end{array} \tag{1}
$$

Figs. 7, 8 shows the analysis for number of epochs versus learning rate, loss and accuracy. We manually tuned the hyper parameters in the proposed network whereas in future research work, we would explore on automatic tuning of hyper parameters. In this paper, we used F1 score to validate the results as shown in Fig. 9. The higher the f1 score the better the classification model. The accuracy of the proposed ACNN model is  $89.67\%$  whereas for the basic CNN, the accuracy is  $85.55\%$ .

# IV. CONCLUSION

In this paper, we proposed an attention based CNN for seismic lithology prediction. In our proposed method, we perform classification of sand and shale from the post- stack seismic data. The attention module is used to better retrieve the features and improve the accuracy of prediction. The continuous wavelet transform is computed for the input seismic data and the obtained time- frequency maps are used to train the network. This aids in considering the full frequency features from the data. Moreover, the affect of various hyper parameters in the training process of convolutional neural network is analyzed. The efficacy of the proposed deep learning method is observed using various metrics such as accuracy, precision, recall and F1 score. We observed that the proposed method has achieved higher accuracy compared to the existing CNN method.

# ACKNOWLEDGEMENT

Authors would like to acknowledge the financial support from Science and Engineering Research Board, Department of Science and Technology, India through Core Research Grant, Ref: CRG/2019/001234. The authors would like to thank Prof. Yangkang Chen at the University of Texas at Austin, for providing research data.

[1] L. R. Denham, "Seismic interpretation," Proceedings of the IEEE, vol. 72, no. 10, pp. 1255- 1265, 1984. [2] N. Ahmad, S. Khan, and A. Al- Shuhail, "Seismic data interpretation and petrophysical analysis of kabuwala area tola (01) well, central indus basin, pakistan," Applied Sciences, vol. 11, no. 7, p. 2911, 2021. [3] M. Anees, "Seismic attribute analysis for reservoir characterization," in 10th Biennial International Conference and Exposition, 2013. [4] D. A. Cooke and W. A. Schneider, "Generalized linear inversion of reflection seismic data," Geophysics, vol. 48, no. 6, pp. 665- 676, 1983. [5] B. Russell and D. Hampson, "Comparison of poststack seismic inversion methods," in SEG Technical Program Expanded Abstracts 1991. Society of Exploration Geophysicists, 1991, pp. 876- 878. [6] C. Bunks, F. M. Saleck, S. Zaleski, and G. Chavent, "Multiscale seismic waveform inversion," Geophysics, vol. 69, no. 5, pp. 1457- 1473, 1995. [7] M. Bosch, T. Mukerji, and E. F. Gonzalez, "Seismic inversion for reservoir properties combining statistical rock physics and geostatistics: A review," Geophysics, vol. 75, no. 5, pp. 75A165- 75A176, 2010. [8] Q. Zeng, Y. Guo, R. Jiang, J. Ba, H. Ma, and J. Liu, "Fluid sensitivity of rock physics parameters in reservoirs: Quantitative analysis," Journal of seismic exploration, vol. 26, no. 2, pp. 125- 140, 2017. [9] P. S. Schultz, S. Ronen, M. Hattori, and C. Corbett, "Seismic- guided estimation of log properties (part 1: A data- driven interpretation methodology)," The Leading Edge, vol. 13, no. 5, pp. 305- 310, 1994. [10] D. P. Hampson, J. S. Schuelke, and J. A. Quirein, "Use of multiattribute transforms to predict log properties from seismic data," Geophysics, vol. 66, no. 1, pp. 220- 236, 2001. [11] M. Pal, "Support vector machines- based modelling of seismic liquefaction potential," International Journal for Numerical and Analytical Methods in Geomechanics, vol. 30, no. 10, pp. 983- 996, 2006. [12] S. Na'imi, S. Shadizadeh, M. Riahi, and M. Mirzakhanian, "Estimation of reservoir porosity and water saturation based on seismic attributes using support vector regression approach," Journal of Applied Geophysics, vol. 107, pp. 93- 101, 2014. [13] A. Reynen and P. Audet, "Supervised machine learning on a network scale: Application to seismic event classification and detection," Geophysical Journal International, vol. 210, no. 3, pp. 1394- 1409, 2017. [14] Y. LeCun, Y. Bengio et al., "Convolutional networks for images, speech, and time series," The handbook of brain theory and neural networks, vol. 3361, no. 10, p. 1995, 1995. [15] A. Krizhevsky, I. Sutskever, and G. E. Hinton, "Imagenet classification with deep convolutional neural networks," Communications of the ACM, vol. 60, no. 6, pp. 84- 90, 2017. [16] A. Waldeland and A. Solberg, "Salt classification using deep learning," in 79th eage conference and exhibition 2017, vol. 2017, no. 1. European Association of Geoscientists & Engineers, 2017, pp. 1- 5. [17] L. Mosser, O. Dubrule, and M. J. Blunt, "Reconstruction of three- dimensional porous media using generative adversarial neural networks," Physical Review E, vol. 96, no. 4, p. 043309, 2017. [18] F. Qian, M. Yin, X.- Y. Liu, Y.- J. Wang, C. Lu, and G.- M. Hu, "Unsupervised seismic facies analysis via deep convolutional autoencoders," Geophysics, vol. 83, no. 3, pp. A39- A43, 2018. [19] G. Zhang, Z. Wang, and Y. Chen, "Deep learning for seismic lithology prediction," Geophysical Journal International, vol. 215, no. 2, pp. 1368- 1387, 2018. [20] J. Hu, L. Shen, and G. Sun, "Squeeze- and- excitation networks," in Proceedings of the IEEE conference on computer vision and pattern recognition, 2018, pp. 7132- 7141.