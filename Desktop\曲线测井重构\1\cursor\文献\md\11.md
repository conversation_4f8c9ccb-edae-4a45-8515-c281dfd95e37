Article

# Cross-Well Lithology Identification Based on Wavelet Transform and Adversarial Learning

Longxiang Sun  $^{1,2}$ , <PERSON><PERSON><PERSON> Li  $^{2}$ , <PERSON><PERSON>  $^{3,*}$ , Haining Liu  $^{4}$ , <PERSON><PERSON> Liu  $^{4}$  and Wenjun Lv  $^{3}$

$^{1}$  AHI- IAI AJ Joint Laboratory, Anhui University, Hefei 230601, China   $^{2}$  Institute of Artificial Intelligence, Hefei Comprehensive National Science Center, Hefei 230088, China   $^{3}$  Institute of Advanced Technology, University of Science and Technology of China, Hefei 230031, China   $^{4}$  Geophysical Research Institute, SINOPEC Shengli Oilfield Company, Dongying 257022, China  * Correspondence: <EMAIL>

Citation: Sun, L.; <PERSON>, <PERSON>.; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON>, <PERSON>; <PERSON><PERSON>, W. Cross- Well Lithology Identification Based on Wavelet Transform and Adversarial Learning. Energies 2023, 16, 1475. https://doi.org/10.3390/en16031475

Academic Editor: <PERSON><PERSON>

Received: 13 December 2022  Revised: 20 January 2023  Accepted: 23 January 2023  Published: 2 February 2023

Copyright: © 2023 by the authors. Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (https://creativecommons.org/licenses/by/4.0/).

Abstract: For geological analysis tasks such as reservoir characterization and petroleum exploration, lithology identification is a crucial and foundational task. The logging lithology identification tasks at this stage generally build a lithology identification model, assuming that the logging data share an independent and identical distribution. This assumption, however, does not hold among various wells due to the variations in depositional conditions, logging apparatus, etc. In addition, the current lithology identification model does not fully integrate the geological knowledge, meaning that the model is not geologically reliable and easy to interpret. Therefore, we propose a cross- domain lithology identification method that incorporates geological information and domain adaptation. This method consists of designing a named UAFN structure to better extract the semantic (depth) features of logging curves, introducing geological information via wavelet transform to improve the model's interpretability, and using dynamic adversarial domain adaptation to solve the data- drift issue cross- wells. The experimental results show that, by combining the geological information in wavelet coefficients with semantic information, more lithological features can be extracted in the logging curve. Moreover, the model performance is further improved by dynamic domain adaptation and wavelet transform. The addition of wavelet transform improved the model performance by an average of 6.25%, indicating the value of the stratigraphic information contained in the wavelet coefficients for lithology prediction.

Keywords: lithology identification; cross- domain; wavelet transform; adversarial learning; semantic segmentation

# 1. Introduction

For geological analysis tasks, such as reservoir characterization and petroleum exploration, lithology identification is a crucial and foundational task [1]. There are two general approaches to identify lithology: direct and indirect approaches [2]. The drilling cores and cuttings are the major elements of the direct approach, which collects the core and cuttings while drilling from the formation, and uses those samples to determine the lithology. However, coring is expensive and time- consuming, and the interpretation of core samples requires extensive expertises. The main methods for indirect identification are logging, electromagnetic gravity, remote sensing, and seismic methods [3]. Electromagnetic gravity, remote sensing and seismic methods cover a large volume of rocks, but have the drawbacks of a weak vertical resolution. The seismic method could lead to a deeper subsurface, but has to be tied to boreholes logging data for the depth reference. Logging has a very high vertical resolution, only prospecting the borehole rocks. Hence, these indirect methods are complementary. In this work, we focus on the identification of lithology by logging curves coupled with core data.

With the increase in logging data and development of computer science, lithology interpretation techniques have been studied intensively in recent years. At present, there are three primary techniques for determining lithology from logging curves [4]:

- The first is the use of traditional lithology identification methods, such as cross-plotting with logging curves and petrophysical characteristics [5]. This technique is based on a variety of lithology-sensitive logging data, organized in 2D or 3D space. For instance, the natural gamma ray varies from high to low values if the lithology shifts from shale to sand, and tuffs often exhibit substantially lower resistivity values. Additionally, a key piece of evidence when determining the lithology is that the physical characteristics of the tri-porosity logging vary for various lithologies [6]. Radwan et al. used petrophysical fingerprints to differentiate fluids, mapped synthetic and raw log combinations to crossplot, and correlated them with previous oil and gas wells tests [7]. Abudeif et al. estimated the hydrocarbons in the target zone, using the crossplot method to calculate porosity and variable values [8]. In addition to traditional crossplots, Cao et al. proposed an augmented-dimensional visualization and analysis approach using crossplots with multi-well and multi-dimensional heterogeneous data. By optimizing the overlapping and covered points in the crossplots, the display and interaction performance of the augmented-dimensional crossplots under the conditions of enormous amounts of data from numerous wells was significantly enhanced [9]. Experts can assess lithology more readily by representing these characteristics in a crossplot. As different experts do not have the same level of empirical knowledge, the interpreted results are inconsistent when analyzed by different experts [10]. In consequence, the results of the identification process are heavily influenced by the expertise of the experts.

- The second one identifies lithology using machine learning (ML), such as random forests (RF) and support vector machines (SVM). Three widely used classifiers for lithology identification, one-to-one SVM, one-to-many SVM, and random forest, were examined by Sun et al. [11]. Tian et al. confirmed the viability of the extreme learning machine (ELM) for lithofacies' identification [12]. Zych et al. evaluated the growing information offered by logging curves on rock physics (e.g., porosity, density, resistivity) and rock chemistry (mineral composition) [13]. To automate the customary workflow of well-log processing and classification, Singh et al. analyzes several unsupervised and supervised machine learning algorithms used on several well-log combinations (WLCs), including an optimization phase, to provide the best results [14]. Salehi et al. developed an intelligent model based on least-squares SVM and coupled simulated annealing algorithms to predict the lithology of an oil field in Iran [15]. Zou et al. proposed a framework for the application of a gradient-boosted decision tree algorithm based on the lithology identification of mineral deposits [16]. For the identification of petrophysical data in the southern Provence Basin, Saporetti et al. developed a Differential Evolution (DE) technique, in addition to an artificial neural network (ANN). This computational approach can aid in the categorization of petrophysical data, enhance reservoir characterization techniques, and idealize production development [17]. Ren et al. obtained the vertical sedimentary patterns of different lithologies using probabilistic statistical methods and combined them with the lithological probabilities calculated by artificial neural networks to obtain a combined lithological probability [18]. To determine the best model for lithology classification, five different classical machine learning methods, which include naive Bayes, SVM and RF, were evaluated by Xie et al., and the results showed that the integrated methods showed optimal performance under supervised learning. The Random Forest and Gradient Tree Boosting classifier also performed well [19]. The inherent relationship between various types of logging curves and the distribution features throughout the depth of the logging curves are two examples of the distinctive information of the logging curves that typical ML algorithms do not fully explore. The amount and quality

of samples have a large influence on the accuracy of machine learning in lithology identification at this phase, which is highly dependent on labeled samples.

- The third one uses a deep learning (i.e., Convolutional Neural Network) approach to identify lithology. Convolutional neural networks (CNN) are used to extract the depth characteristics of the logging curve, since the lithology and the form of the curve are somewhat associated. The conversion of one-dimensional curves from logs to two-dimensional pictures as CNN inputs was accomplished by Zhu et al. using wavelet transform [20]. Imamverdiyev et al. proposed a 1D-CNN model and tested its efficacy in lithology detection using six distinct logging curves as input [21]. Considering the occurrence of a sizable quantity of unlabeled data in the field of petroleum exploration, Li et al. proposed a lithology identification approach, employing the Laplace support vector machine for semi-supervised learning [22]. Zeng et al. designed a bidirectional lithology identification network by combining the properties of the logging data at depth, the correlation of various logging curves, and the real depth accumulation effect [23]. Li et al. proposes a CNN-based method for multi-type mixed lithology identification, and incorporates neural network model compression techniques to assure model inference efficiency. Individual lithology identification for the proposed model, which was trained on a multi-type mixed-rock lithology dataset, takes just 11 milliseconds, making it feasible for use in industry by adapting the method for embedded hardware or Android platforms [24]. The performance of the ML model is constrained by the imbalance in the lithology samples and the complementing information that is not recovered from the logging curves. As a result, Li et al. proposed an enhanced deep forest (DF) model, along with adaptive synthetic sampling (ADASYN), to accomplish autonomous lithology identification. To balance the lithology samples, ADASYN is utilized to oversample a few lithologies. The method investigates the supplementary data suggested in the multi-grain scanned logging curves and experimentally confirms that the model can successfully identify the lithology of imbalanced logs [25]. In recent years, long short-term memory networks (LSTM) have shown great advantages in processing sequential data. As logging data frequently have a sequential structure, Lin et al. proposed an LSTM-based lithology identification network and tested it on eight other wells in the same research region. The findings indicated that the trained LSTM had a decent generalization capacity [26]. Liu et al. tried to enhance the generalization capacity of deep learning models by utilizing superposition and entangled quantum systems in quantum computing. In addition to considerably lowering the number of parameters, the proposed QEDL model also provides a better generalization ability without compromising predictive potential [27]. Using EfficientNet as the basic network, Gong et al. improved the neural network, considerably increasing the identification accuracy and generalization ability. The field of lithology identification has satisfactory outcomes, which offers fresh perspectives and novel approaches to lithology identification issues [28]. Hu et al. proposes using a stacked capsule autoencoder network (SCAE-Net) to better identify the lithology of complicated carbonates. To express the internal relationships of objects from their component parts to their whole, sCAE-Net employs multiple capsule autoencoders. It is also able to mine sample subspaces where sample representations are similar, offering a different approach to the lithology identification of complex carbonates [29]. However, lithology identification tasks still suffer from the much-argued lack of interpretability in deep learning. Since the model lacks interpretability, it is challenging to use it in a realistic production setting, and it fails to fundamentally address the issue at hand.

Numerous geologically qualified professionals must be involved in traditional logging lithology identification modelling, the results of which are highly subjective due to the high amount of human participation. Therefore, results might differ substantially from one expert to another. Geographical features differ from region to region, and even from well to well within the same region. As a result, it could be challenging to directly use

the same logging lithology identification approach for several wells. In other words, predicting the lithology of unknown wells using logging data from known wells can significantly cut down on the time and work required for lithology identification. Therefore, geologists are already concentrating on strategies to increase the precision of lithology identification in cross- wells. Liu et al. proposed a cross- well lithology identification method incorporating maximum mean difference, joint distribution domain adaptation, and manifold regularization to improve the accuracy of the old model when applied to new wells [30]. Chang et al. incorporated active learning (AL) with domain adaptation in lithology identification, and the approach was successful in minimizing data distribution discrepancies by employing a limited number of target labels [31]. A cross- domain lithology identification model, proposed by Zhou et al., was based on sequential logging data and mined the trend of logging curve depth changes using a temporal convolutional network [32]. Xu et al. investigated the problem of large differences in logging data and distribution of new wells, applying an adversarial autoencoder to extract domain- invariant features. [33]. Fan et al. created a model to recognizing the lithology of rocks, built on two lightweight CNN (SqueezeNet and MobileNets) [34]. Chang et al. proposed a two- stream multilayer neural network to solve the data drift problem. The training process of this network is performed by maximum mean discrepancy optimization [35]. Zhang et al. proposed a method for transferring the logs from one well to another without affecting their physical significance [36]. Wu et al. presented the robust unilateral alignment approach for lithology identification, which employs anticipated maximum mean differences to reduce the cross- well data- drift problem [37]. A meta- learning approach was utilized by Xu et al. to build a rock type identification method that overcame the data transfer issue and enhanced the model's generalizability [38]. The question of how to improve cross- well lithology identification while adding geological information to the deep learning method to promote model interpretability remains open for the present logging lithology identification problem.

In this paper, we proposed a cross- well lithology identification method and discuss the unsupervised domain adaptation in lithology identification. The proposed method uses dynamic adversarial adaption to solve the problem of cross- well data- distribution differences and introduces stratigraphic features to benefit the overall network when analyzing the geological properties of lithologies. Specifically, this paper proposes a cross- well lithology identification network based on wavelet transform and adversarial learning, and introduces semantic feature extraction, attentional feature fusion, and dynamic adversarial domain adaptation for the lithology identification task. The main contributions of this paper are as follows:

1. The logging data are processed using the semantic segmentation approach, and the traditional semantic segmentation model is improved via attentional feature fusion; 
2. The wavelet transform is used to process the logging curves, which introduces stratigraphic feature limitations and improves the model's interpretability; 
3. A dynamic adversarial training strategy is used to eliminate the data distribution discrepancy.

The remaining sections of the paper are structured as follows: Section 2 introduces the relevant knowledge for the proposed method; Section 3 describes the proposed method; Section 4 evaluates the proposed method's efficacy using comparative and ablation experiments; Section 5 concludes the paper.

# 2. Preliminary

This section presents some preliminaries about Dynamic Domain Adaptation and Wavelet Transform, which are included in our method.

# 2.1. Problem Description

In unsupervised domain adaptation (UDA), there is a source domain  $D_{s} = \{(\mathbf{x}_{i}^{s},y_{i}^{s})\}_{j = 1}^{n_{s}}$  with  $n_t$  labeled samples and a target domain  $D_{t} = \{\mathbf{x}_{j}^{t}\}_{j = 1}^{n_{t}}$  with  $n_t$  unlabeled samples,

both with the same label space. The goal of UDA is to predict the unknown target domain samples based on known source samples. In the logging lithology identification task, the logging sample at a certain depth is denoted as  $x_{i} = \{x_{i1};x_{i2};\ldots ;x_{id}\}$ , where  $d$  is the number of different types of logging curves, including acoustic log, spontaneous potential log, etc. The lithology corresponding to sample  $x_{i}$  is denoted as  $y_{i} = k,k\in 1,2,3,\ldots ,K$  where  $K$  is the number of lithology categories.

# 2.2. Dynamic Domain Adaptation

Adversarial training typically includes a label classifier, a feature extractor, and a domain discriminator. To discriminate between the source and target domains, the domain discriminator is trained. The feature extractor attempts to perplex the domain discriminator to guarantee that domain invariant features are well- learned. Label classifiers and domain classifiers are combined, using the feature extractor as a shared component of the network in a common adversarial training process for domain adaptation. An intermediate feature representation that is relevant to the label classifier but identical to the domain classifier is created via adversarial learning. Once more, it is possible to think of the intermediate feature as including information on class distinctions as well as more detailed domain- invariant information. Domain adaptation is based on identifying and rationally utilizing the similarities between the source and target domains. At present, the primary way of describing the similarities between the two is by estimating the various probability distributions of the distinct domain.

The primary concept of dynamic adversarial adaptation (DAA) [39], which is based on the well- established Generative Adversarial Network (GAN), is to dynamically modify the weight of marginal and conditional distributions in the process of distribution adaptation. The primary components of DAA are the label classifier, global domain discriminator, local domain discriminator and dynamic adversarial factor.

# 2.2.1. Label Classifier  $G_{y}$

Labeled domain data were used to train the label classifier. After the distribution of the two domains was minimized, the supervised information from that domain can affect the classification of the unlabeled domain. The loss of the label classifier can be expressed as:

$$
L_{y} = L_{CE}(G_{y}(G_{f}(\mathbf{x}_{i})),y_{i}) \tag{1}
$$

where  $L_{CE}$  denotes the cross- entropy loss,  $\mathbf{x}_i\in D_s,y_i$  is the label of  $\mathbf{x}_i$

# 2.2.2. Global Discriminator  $G_{d}$

The marginal distributions of the two domains can be aligned via  $G_{d}$ .  $G_{d}$  is built on the framework of a general domain adversarial neural network. The purpose of  $G_{d}$  is to determine from which domain the sample features derived, and to engage in adversarial learning with  $G_{f}$ . As a result,  $G_{f}$  extracts more common characteristics across domains. The loss of global domain discriminator can be expressed as:

$$
L_{g} = L_{BCE}(G_{d}(G_{f}(\mathbf{x}_{i})),d_{i}) \tag{2}
$$

where  $L_{BCE}$  denotes the binary cross- entropy loss,  $\mathbf{x}_i\in D_s\cup D_t,d_i$  denotes the domain label of  $\mathbf{x}_i$

# 2.2.3. Local Discriminator  $G_{d}^{c}$

$G_{d}^{c}$  is responsible for aligning the conditional distributions of the two domains. The local domain discriminator, which is distinct from the global domain discriminator, can align multi- node structures in both distributions, leading to fine- grained domain adaptation.

The local domain discriminator is subdivided into  $C$  class- wise domain discriminators  $G_{d}^{c},c = 1,2,\ldots ,C$ . where  $C$  is the number of classes. Each class- wise domain discriminator only distinguishes from which domain the samples in a class derived. The target domain is

divided based on the pseudo- labels provided by the label classifier. The loss of the local domain discriminator can be expressed as:

$$
L_{l} = \sum_{c = 1}^{C}L_{BCE}\big(G_{d}^{c}\big(\hat{y_{i}^{c}} G_{d}^{c}\big(\mathbf{x}_{i}\big),d_{i}\big) \tag{3}
$$

where  $\hat{y_i^c}$  is the probability that  $\mathbf{x}_i$  is predicted to be class  $\bar{C}$

# 2.2.4. Dynamic Adversarial Factor  $\omega$

The marginal and the conditional distribution do not necessarily have the same influence on the results in most tasks. Equivalent weighting for both distributions may result in worse outcomes. The essential feature of DDA is balancing the contributions of the two distributions. As a result,  $\omega$  is utilized in DAA to evaluate the relative importance of the two distributions in the task. To be more specific,  $\mathcal{A}$  - distance [40] in DAA is used to calculate the weight of two distributions. The  $\omega$  can be calculated as:

$$
\omega = \frac{A_g}{A_g + \frac{1}{C}A_l} \tag{4}
$$

where  $A_{g}$  and  $A_{l}$  are the  $\mathcal{A}$  - distance of the global and local domain discriminator. The ultimate learning objectives of the DAA can be expressed as:

$$
L(\theta_{f},\theta_{y},\theta_{d},\theta_{d}^{c}|_{c = 1}^{C}) = L_{y} - \lambda ((1 - \omega)L_{g} + \omega L_{l}) \tag{5}
$$

# 2.3. Wavelet Transform

In addition to using a "time- frequency" window that changes with frequency, wavelet transforms, as a transform analysis technique, contains the Fourier transform localization principle at its core. It solves the drawback of the Fourier transform's weak time domain resolution and has improved localization properties in both the time and frequency domains, making it especially effectively fitted to processing time- varying signals.

The wavelet transform coefficient in the logging task depends on the scale factor and the displacement factor. The similarity between the Wavelet function and logging curves could be determined by referring to the wavelet coefficient size. The wavelet coefficients can be viewed as the studied logging curve's covariance with the wavelet function. The definitions of the wavelet transform are as follows:

$$
\mathcal{W}_{\psi}(a,b) = \frac{1}{\sqrt{|a|}}\int_{-\infty}^{+\infty}f(t)\overline{\psi}\left(\frac{t - b}{a}\right)\mathrm{d}t \tag{6}
$$

where  $\mathcal{W}_{\psi}$  denotes the wavelet coefficient.  $a$  is the scale factor, which is related to the scaling (frequency) of the wavelet function.  $b$  is the time translation factor, indicating the translation of the wavelet function.  $f(t)$  is the signal sequence,  $\psi$  is the wavelet function,  $\overline{\psi}$  is the conjugate function of  $\psi$  . The continuous wavelet transform (CWT) is one in which the wavelet function's variables  $a$  and  $b$  are continuous. In the discrete wavelet transform (DWT),  $a$  is usually discretized by a power exponent.

# 3. Methodology

In this study, we used the typical U- Net structure as the model backbone network to represent the lithology identification issue based on logging data as a one- dimensional (1D) semantic segmentation problem [41]. The semantic segmentation method in logging lithology identification can simultaneously predict lithology at each depth, as shown in Figure 1. The semantic segmentation method can extract not only the correlation information between various logging curves, but also the properties of the logging curves in the depth domain, in contrast to standard classification methods (e.g., ANN, SVM) [42].

The U- Net decoding structure is more sophisticated than the traditional FCN structure [43]. The U- Net structure can gain more semantic information, since it recursively combines characteristics between layers. In the field of image segmentation, the U- Net has been extensively utilized. In 1D U- Net, the shallow layer's convolution concentrates on the local features of the curve, while the deep layer's convolution concentrates on the features of the curve as a whole. The combination of shallow and deep features, each of which has a distinct relevance, can enhance the network's capacity to extract features across the curve depth domain.

![](images/06de2bb9f4db47a456604c30273e3884dc927c564a275e10532eee5eb2d0eaa0.jpg)  
Figure 1. Intensive lithology prediction. Each logging curve in the semantic segmentation task is input as a channel to achieve intensive lithology prediction for each depth point. AC: acoustic log, SP: spontaneous potential log, GR: gamma ray log.

The structural information on the depth of the logging curve from geological analysis plays a role in determining the geological structure of the rock. The distribution pattern inferred between various lithologies in a location is determined by the sedimentary features of that area. The geological circumstances (such as water depth and flow velocity) present during the deposition phase affect the composition of the sediments. As a result, changes in sediment sources and circumstances may alter the type of sedimentation. In sedimentary rocks, this alteration manifests as a change in the composition of the rocks and in the size of the grains, which, in turn, affects the variety of geologic lithologies. Deep learning is becoming the standard for geophysical logging tasks, but directly applying deep learning models to logging tasks does not always produce satisfactory results. This phenomenon has two primary causes. First, there is the issue of inconsistencies in the data across several areas. The second is that physical geographical information is not included in the current models.

One issue we wish to address is how to include additional geological information in our model. Geological researchers have recently adopted wavelet transform as an efficient tool in the analysis and processing of logging data due to its multi- scale analytical properties [44- 46]. The logging curve and its wavelet- transformed time- frequency diagram are shown in Figure 2, where it is clear that the wavelet transform is more sensitive to the abrupt changes in the logging curve, and can recognize its singularity with accuracy. In geological analysis, scientists have applied logging data to a variety of stratigraphic divisions using wavelet transform. This involves both the identification of singularity through the extraction of the time- frequency characteristics from the logging curves and the analysis of the geological sedimentary cycles included in the logging curves using wavelet transforms. To accomplish single- well sequence division, it is necessary to establish a correlation with the sequence boundary at each level by examining the periodic oscillation

traits displayed by wavelet coefficient curves at multiple scales. The logging curve is selected for wavelet transform to obtain wavelet coefficients. Moreover, the stratigraphic division is accomplished by analyzing the wavelet coefficient curves for periodic oscillatory or modulus maxima. Figure 3 illustrates the stratigraphic division procedure utilizing the wavelet transform.

![](images/ec19c0e0005a4801da717dc724ac12e92968874dd31cd02162b80a4de8a7a393.jpg)  
Figure 2. (a) SP logging curve (b) Time-frequency diagram after wavelet transform. It is clear that the wavelet transform is more sensitive to the logging curve's abrupt changes and can recognize its singularity with accuracy.

![](images/52683cd5245f2857c34d62460dc20a427bfae7e403bfc4cd8ec94346ffd53fc0.jpg)  
Figure 3. The process of stratigraphic division using wavelet transform. The stratigraphic division is accomplished by analyzing the wavelet coefficient curves for periodic oscillatory or modulus maxima.

We thus think that the feature associated with the stratigraphic sequence can be found in the wavelet coefficient curve that results from processing the logging curve using a wavelet transform. Additionally, this stratigraphic characteristic can facilitate the more effective identification of lithologies. In this paper, we analyze the potential spontaneous log (SP) using wavelet transform and combine the stratigraphic sequence information represented by the wavelet coefficients with the features extracted by the U- Net module. Furthermore, the distribution features of the logging curve in the frequency domain cannot be considered, since the U- Net network can only extract the features of the logging curve in the depth domain. However, the features of the logging curve may be expanded via the wavelet transform to the depth- frequency domain; therefore, combining the two features further increases their robustness.

The traditional U- Net structure uses universal concatenation to achieve feature fusion. However, this simple kind of feature fusion is not the optimal option. Feature fusion is the process of merging the advantages of characteristics obtained from different sources to

enhance the model performance by exploiting the complementarity between the features. Better feature fusion is made possible by attentional feature fusion (AFF), which also improves the representation of the fusion features. The majority of typical cases, such as short and long connections, are accessible to AFF. Given two feature maps  $\mathbf{X}, \mathbf{Y} \in \mathbb{R}^{C \times H \times W}$  and based on the multi- scale channel attention module  $M$ , AFF can be expressed as:

$$
\mathbf{Z} = \mathbf{M}(\mathbf{X}\oplus \mathbf{Y})\otimes \mathbf{X} + (1 - \mathbf{M}(\mathbf{X}\oplus \mathbf{Y}))\otimes \mathbf{Y} \tag{7}
$$

where  $\mathbf{Z} \in \mathbb{R}^{C \times H \times W}$  is the fused feature,  $\oplus$  denotes the initial feature integration,  $\otimes$  denotes the element- wise multiplication. Therefore, we utilize the AFF to replace the feature fusion in the whole model to execute the better fusion of various features and provide a better feature representation.

The reality that the geological properties vary considerably from area to region, and even from well to well within the same region, adds another significant challenge to the cross- well lithology identification task. This makes it challenging to directly apply classification models that perform well on a single well to other wells, and results in limited generalization of those models. As a result, it is typically ineffective to directly classify data in the target domain using a network that has been trained on data from the source domain. In cross- domain image classification and object detection, dynamic adversarial adaptive (DAA) strategies have shown more impressive results because of their capacity for the dynamic and quantitative evaluation of marginal and conditional distributions. To lessen the shift in distribution between the features of two wells, we thus attempt to incorporate an adversarial learning strategy into the cross- well lithology identification task.

Specifically, our proposed overall network consists of a distribution alignment module and a feature extraction module. We employ a two- stream structure in the feature extraction module, with the upstream structure (UAFN) utilizing the widely used U- Net framework and AFF to enhance the skip connection in U- Net. The downstream structure uses wavelet transform to process the SP curve to obtain the stratigraphic features represented by wavelet coefficients. Then, the stratigraphic features from downstream structure and the semantic features from UAFN are fused with AFF, and the fused features are the output features of the whole feature extraction module. Finally, the distribution alignment module uses the DAA to align the output features from the feature extraction module. Figure 4 illustrates the overall structure of the network. We add a regularization term to prevent overfitting from decreasing network performance.

The loss of the label classifier in the proposed method can be expressed as:

$$
L_{y} = L_{CE}\big(G_{y}\big(Z\big(U_{AF}(\mathbf{x}_{i}),W_{t}\big(\mathbf{x}_{i}^{sp}\big)\big)\big),y_{i}\big) \tag{8}
$$

where  $L_{CE}$  is the cross- entropy loss,  $y_{i}$  is the label of  $\mathbf{x}_{i}$ ,  $U_{AF}(\cdot)$  denotes the UAFN,  $W_{t}(\cdot)$  denotes the wavelet transform,  $\mathbf{x}_{i} \in D_{s}$ .

The loss of the global and local domain discriminator can be updated as:

$$
L_{g} = L_{BCE}\big(G_{d}\big(Z\big(U_{AF}(\mathbf{x}_{i}),W_{t}\big(\mathbf{x}_{i}^{sp}\big)\big)\big),d_{i}\big) \tag{9}
$$

$$
L_{l} = \sum_{c = 1}^{C}L_{BCE}\big(G_{d}^{\hat{c}}\big(\hat{y_{i}^{c}} Z\big(U_{AF}(\mathbf{x}_{i}),W_{t}\big(\mathbf{x}_{i}^{sp}\big)\big)\big),d_{i}\big) \tag{10}
$$

where  $L_{BCE}$  is the binary cross- entropy loss,  $\hat{y_{i}^{c}}$  is the probability that  $\mathbf{x}_{i}$  is predicted to be class  $c$ ,  $\mathbf{x}_{i} \in D_{s} \cup D_{t}$ .

![](images/b9a37f41c09c5749077ebda1291925e24a794c518df4b54ca1cc91bad2cc2404.jpg)  
Figure 4. The Proposed Method of Cross-well Lithology Identification.

The ultimate learning objectives of the overall network can be expressed as:

$$
L(\theta_{U_{AF}},\theta_{y},\theta_{d},\theta_{d}^{c}|_{C = 1}^{C}) = L_{y} - \lambda ((1 - \omega)L_{\mathrm{g}} + \omega L_{I}) + \beta ||W||_{2} \tag{11}
$$

The network involves three hyperparameters  $(\lambda , \omega , \beta)$ ,  $\beta$  is the regularization term factor and  $\omega$ , continually updated via the network.

# 4. Results

The proposed method is tested on two datasets, including four logging lithology identification tasks, to compare the proposed method with other classification methods. The contribution of each component is also verified by ablation experiments.

# 4.1. Experiment Preparation

We evaluate the advantages of the proposed method using two datasets. The two datasets were collected from several wells, with each well being selected at random from within its region to ensure data diversity. Based on the assumption that the source and target wells have the same type of logging curves and share the same label space, the training wells were selected. Based on the sensitivity of the lithology, we choose logging curves, and logging curves with high lithology are more likely to be chosen as inputs. During the training process, the deep learning model automatically evaluates the importance of the selected curves.

The experimental data come from the Jiyang Depression area, a typical Mesozoic terrestrial fault basin in eastern China and the earliest discovered area in the Bohai Bay Basin. The area has large resources and rich hydrocarbon reservoir types. Exploration has proven that the shale, oil shale, calcareous shale, and dark mudstone rocks in the Es4 and Es3 Member of Shahejie Formation are the main gathering places for rich hydrocarbon resources. In this paper, mudstone and siltstone and coarse sandstone are the lithology type being predicted. Table 1 shows the specific data contained in each dataset. We used Macro- F1 as a metric of classification performance due to the significant imbalance between the number of samples for each kind of lithology. Macro- F1 is calculated as follows:

$$
\mathrm{Macro - F1} = \frac{2TP}{2TP + FP + FN} \tag{12}
$$

where  $TP, FP,$  and  $FN$  denote true positive, false positive, and false negative, respectively. To avoid the mistakes brought on by multiple measurement ranges, the data may be normalized because the values of several logging curves have distinct ranges and orders. We employed Min- Max Normalization, which is denoted by the following formula:

$$
x^{\prime} = \frac{x - x_{min}}{x_{max} - x_{min}} \tag{13}
$$

Multiple logging curves are chosen as inputs to the UAFN module, with each logging curve representing a channel and the logging curve used as one- dimensional depth point data. The input data consisted of the source domain and target domain, with the source domain having the relevant lithology labels and the target domain lacking those labels. Each batch of the logging curve data contained 512 depth point samples. The batches were separated according to depth. The data were batch- input the the UAFN module. The UAFN module comes with an encoder and a decoder. The encoder consists of four convolutional blocks, with each containing two 1D convolutional layers and a 1D maximum pooling layer, the convolutional kernel size 9, the stride size 1, and the padding size 4. Batch normalization (BN) and linear rectification function (ReLU) occurred after each convolutional layer. Each transposed convolution block consists of a 1D transposed convolution with convolution kernel size 8, stride size 2 and padding size 3, and two 1D convolutions with the convolution kernel size 9, stride size 1 and padding size 4. The AFF was carried out throughout the channel dimensions by feature mapping of encoders and decoders of the same scale. The logging curve was utilized as the input of the wavelet transform to acquire the wavelet coefficients corresponding to each scale, and the UAFN structure was used to extract source domain features and target domain features for each training cycle. The final product of the whole feature extraction module is the fusion of stratigraphic features and semantic features. Subsequently, the distribution alignment module used DAA to conduct the distribution alignment, with the output features of the feature extraction module as its input. Finally, the above process was repeated until the model training was completed.

Following the training, the lithology corresponding to each depth point was predicted using the trained feature extraction module and label classifier. We employed a hidden layer neural network with 64 neurons in the hidden layer as the classifier. A double hidden layer neural network of 64 and 128 hidden layer neurons, respectively, served as the global domain discriminator. The structure of the global domain discriminator and the local domain discriminator is the same, as is the subdomain discriminator in the local domain discriminator, and all activation functions use ReLU. In the experiments, our network was trained using the ASGD optimizer with a learning rate of 0.015. The value of the hyperparameter  $\omega$  was self- calculated by the network. The regularization factor  $\beta = 0.001$  and balanced hyperparameter  $\lambda = 1$

Table 1. Datasets Description.  

<table><tr><td colspan="2">Dataset</td><td colspan="3">1</td><td colspan="4">2</td></tr><tr><td>LOGS</td><td colspan="4">CAL; SP; AC; GR; COND; RLML; RNML; R25; R4</td><td colspan="4">AC; CAL; COND; RLML; RNML; R04; R25; R4; SP</td></tr><tr><td rowspan="3"># Samples</td><td>LITH</td><td>Mudstone</td><td>Silystone</td><td>Coarse sandstone</td><td>LITH</td><td>Mudstone</td><td>Siltstone</td><td>Coarse sandstone</td></tr><tr><td>Well A</td><td>8700</td><td>1392</td><td>640</td><td>Well C</td><td>8760</td><td>1369</td><td>778</td></tr><tr><td>Well B</td><td>4108</td><td>521</td><td>532</td><td>Well D</td><td>4450</td><td>461</td><td>632</td></tr></table>

1 LOGS: R04:  $0.4\mathrm{m}$  bottom gradient resistivity, CAL: caliper log, AC: acoustic log, SP: spontaneous potential log, COND: Induction conductivity, RNML: micro-potential resistivity, RLML: micro-gradient resistivity, R25:  $2.5\mathrm{m}$  bottom gradient resistivity, R4:  $4\mathrm{m}$  bottom gradient resistivity, GR: gamma ray log.

# 4.2. Experimental Analysis

We conducted experiments on the dataset in Table 1 and compared our strategy to popular classification methods. The data from four wells in Well A and B were homogeneous, as well as the Well C and D. Transfer learning between homogeneous datasets is

possible where labels are present in the source domain but absent in the target domain. Additionally, using the full well section as input in the trials, our proposed method was evaluated on four cross- well lithology tasks  $(A \rightarrow B, B \rightarrow A, C \rightarrow D, D \rightarrow C)$ . We did not split the test and validation sets, since this is an unsupervised domain adaptive issue. Rather, we directly tested the outcomes on the whole target well section.

We set the hyperparameters of each methodology in accordance with the respective hyperparameter setup protocol and utilized KNN methods as baseline classifiers. Additionally, we performed five experiments and recorded the Macro- F1 values each time. Finally, we took the average Macro- F1 value of each method as the final experimental result, as shown in Figure 5. In comparison to existing typical lithology classification methods, our proposed method has a number of benefits, with average Macro- F1 values on datasets 1 and 2 of  $76.3\%$  and  $71.85\%$ , respectively. The results show that the segmentation model is able to extract more effective features compared to other models in the case of an increase in logging curve. More lithological characteristics that are present in the logging curves can be extracted by combining the geological information found in the wavelet coefficients with the semantic information.

![](images/8771f8e7058ba5932bc73426394eb862f618785236573b93171f9a2cd63a8e0e.jpg)  
Figure 8. Comparative study results of different methods.

The trained feature extraction module- label classifier was used to make the prediction, and the confusion matrix between the outputs and the actual values was computed. In Figure 6 we show the well B's confusion matrix. The model effectively recognizes mudstone. The class imbalance affected the prediction scores. The method would offer a better performance in a more balanced dataset.

Figure 7 shows some of the real log curves from well B used in our experiments, as well as the manually determined lithology of the corresponding depth. Additionally, the experimental results of our method and other methods are visualized. The three colors in Figure 7 represent lithologies mudstone, silstone, and coarse sandstone, respectively, and the far right of the figure displays the outcomes predicted by our method. The experimental findings of our suggested technique are smoother and more in line with the

actual lithological distribution when compared to those of other methods. According to the findings, our method has a significant advantage over other methods.

![](images/aab844af8fd866aacc3d5d53fc6cf822aa3b3dd35edf106bf1e20351b5ad576d.jpg)  
Figure 6. Normalized confusion matrix of well B.

![](images/c371d7b8809f6635d60d6b5c1844e2314715d9271be6e6eb698a20cafbe28fed.jpg)  
Figure 7. Visualization of experimental data and different methods of lithology identification in well B, where GT stands for "ground truth", which is a manually produced lithology label.

To verify the validity of each component in our proposed method, we decompose the overall structure, as shown in Table 2. The experimental results for the four lithology identification tasks mentioned above are shown in Figure 8. The findings demonstrate that the DAA and wavelet transform contribute more to the enhancement of model performance. The average  $6.25\%$  improvement in model performance with the inclusion of wavelet transform demonstrates the effectiveness of the geological characteristics incorporated in the wavelet coefficients for lithology prediction. The DAA may successfully align the feature information found in various well- to- well logging curves, as seen by the  $6.5\%$

average gain in model performance that followed its addition. In addition to replacing skip connection, the AFF fuses two features in the model. It retains more useful feature information than traditional feature stitching, which results in a small improvement in the performance of the model as a whole. Last but not least, while the performance of the cross- well classification task from large to small data on  $(A \rightarrow B, C \rightarrow D)$  was only improved by  $2.7\%$ , we found an average increase of  $10.3\%$  in V4 compared to V3 in migration experiments from small to large data on  $(B \rightarrow A, D \rightarrow C)$ . This finding suggests that wavelet transform is able to extract more useful features with fewer data in the source domain. Consequently, wavelet transform introduction is crucial for the cross- well lithology identification task with limited source domain data.

![](images/5024fa12606b168b554c7e4652ebac0e21254dde531a8fd268e4a032deec3693.jpg)  
Figure 8. Ablation of experimental results.

# 5. Conclusions

In this paper, we investigated the unsupervised cross- well domain adaptive challenge in lithology identification, proposed the application of dynamic adversarial adaptive methods to the distribution discrepancy problem, and introduced stratigraphic characteristics to enhance the lithology analysis of the entire network. The importance of each structure was validated by ablation experiments, which proves that eliminating any one of the structures from our method results in a decline in performance.

The results show that the segmentation model can extract more useful features than other models when there are more logging curves. Moreover, more lithological characteristics present in the logging curves can be extracted by combining the geological information found in the wavelet coefficients with the semantic information. Furthermore, the performance of the model is improved more by DAA and wavelet transform. The addition of the wavelet transform increased model performance by  $6.25\%$  on average, demonstrating the value of the stratigraphic information included in wavelet coefficients for lithology prediction. Moreover, we discovered that applying wavelet transform on small datasets to large datasets can increase performance more than applying wavelet transform on large datasets to small data sets. Since there are fewer data in the source domain, the wavelet transform can extract more meaningful characteristics. Consequently, wavelet transform introduction is essential for cross- well lithology identification tasks with limited source domain data.

However, the proposed method needs to address the problem of the unbalanced distribution of labeled samples in well logging data. The proposed approach is expected to perform better if there is a better way to deal with imbalanced samples, which will be our next development direction.

Author Contributions: Conceptualization, L.S.; methodology, L.S.; software, L.S.; validation, K.L. and L.S.; formal analysis, K.L.; investigation, L.S.; resources, K.L. and Z.L.; data curation, H.L. and G.L.; writing—original draft preparation, L.S.; writing—review and editing, K.L.; visualization, L.S.; supervision, Z.L. and W.L.; project administration, Z.L.; funding acquisition, Z.L. and W.L. All authors have read and agreed to the published version of the manuscript.

Funding: The work is supported in part by National Nature Science Foundation of China under grant number 62203420, 62103125, 62273319, and in part by Shengli Oilfield of SINOPEC under grant number YKJ2201.

Data Availability Statement: Not applicable.

Acknowledgments: We are grateful to all staff involved in this project, and also wish to thank the journal editors and the reviewers, whose constructive comments improved the quality of this paper greatly.

Conflicts of Interest: The authors declare that they have no conflict of interest.

# Abbreviations

The following abbreviations are used in this manuscript:

ML Machine learning AL Active learning DAA Dynamic domain adaptation GAN Generative adversarial network SVM Support vector machines WT Wavelet transform UDA Unsupervised domain adaptation FCN Full convolution network RF Random forests SP Spontaneous potential log CWT Continuous wavelet transform DWT Discrete wavelet transform ANN Artificial neural network TP True positive FP False positive FN False negative BN Batch normalization ReLU Linear rectification function GT Ground truth KNN K- Nearest Neighbor

# References

1. Chang, J.; Kang, Y.; Zheng, W.X.; Cao, Y.; Li, Z.; Lv, W.; Wang, X.M. Active Domain Adaptation With Application to Intelligent Logging Lithology Identification. IEEE Trans. Cybern. 2022, 52, 8073-8087. [CrossRef]2. Saporetti, C.M.; da Fonseca, L.G.; Pereira, E. A lithology identification approach based on machine learning with evolutionary parameter tuning. IEEE Geosci. Remote Sens. Lett. 2019, 16, 1819-1823. [CrossRef]3. Fu, G.M.; Yan, J.Y.; Zhang, K.; Hu, H.; Luo, F. Current status and progress of lithology identification technology. Prog. Geophys. 2017, 32, 26-40.4. Pan, S.; Wang, Z.; Zhang, Y.; Cal, W. Lithology identification based on LSTM neural networks completing log and hybrid optimized XGBoost. J. China Univ. Pet. Nat. Sci. 2022, 46, 62-71.5. Li, Y.; Lian, R.; Xue, Z.; Dai, C. Application status and prospect of big data and artificial intelligence in oil and gas field development. J. China Univ. Pet. Nat. Sci. 2020, 44, 1-11.6. Zhao, L.F.; Li, A.Y.; Wang, D.L.; Zhang, M.P.; Zhou, X.M. Calibration of magnetotelluric sounding based on resistivity logging curves. Geophys. Geochem. Explor. 2022, 46, 732-742.7. Radwan, A.E.; Abudeif, A.; Attia, M. Investigative petrophysical fingerprint technique using conventional and synthetic logs in siliciclastic reservoirs: A case study, Gulf of Suez basin, Egypt. J. Afr. Earth Sci. 2020, 167, 103868. [CrossRef]8. Abudeif, A.; Attia, M.; Al-Khashab, H.; Radwan, A. Hydrocarbon type detection using the synthetic logs: A case study, Baba member, Gulf of Suez, Egypt. J. Afr. Earth Sci. 2018, 144, 176-182. [CrossRef]9. Cao, M.; Gao, Z.; Yuan, Y.; Yan, Z.; Zhang, Y. A Visualization and Analysis Method by Multi-Dimensional Crossplots from Multi-Well Heterogeneous Data. Emergies 2022, 15, 2575. [CrossRef]10. Petroleum, U. Application of Crossplot Technique to the Determination of Lithology Composition and Fracture Identification of Igneous Rock. Well Logging Technol. 1999, 23, 53-56.11. Sun, J.; Li, Q.; Chen, M.; Ren, L.; Huang, G.; Li, C.; Zhang, Z. Optimization of models for a rapid identification of lithology while drilling-A win-win strategy based on machine learning. J. Pet. Sci. Eng. 2019, 176, 321-341. [CrossRef]12. Tian, Y.J.; Pan, H.X.; Liu, X.C.; Cheng, G.J. Lithofacies recognition based on extreme learning machine. Appl. Mech. Mater. 2013, 241-244, 1762-1767. [CrossRef]13. Zych, M.; Stachura, G.; Hanus, R.; Szabó, N.P. Application of Artificial Neural Networks in Identification of Geological Formations on the Basis of Well Logging Data—A Comparison of Computational Environments' Efficiency. In Methods and Techniques of Signal Processing in Physical Measurements; Springer Cham, Switzerland, 2018; pp. 416-422.14. Singh, H.; Seol, Y.; Myshakin, E.M. Automated well-log processing and lithology classification by identifying optimal features through unsupervised and supervised machine-learning algorithms. SPE J. 2020, 25, 2778-2800. [CrossRef]15. Salehi, S.M.; Honarvar, B. Automatic identification of formation lithology from well log data: A machine learning approach. J. Pet. Sci. Res. 2014, 3, 73-82. [CrossRef]16. Zou, Y.; Chen, Y.; Deng, H. Gradient boosting decision tree for lithology identification with well logs: A case study of zhaoxian gold deposit, shandong peninsula, China. Nat. Resour. Res. 2021, 30, 3197-3217. [CrossRef]17. Saporetti, C.M.; Golatt, L.; Pereira, E. Neural network boosted with differential evolution for lithology identification based on well logs information. Earth Sci. Inform. 2021, 14, 133-140. [CrossRef]18. Ren, X.; Hou, J.; Song, S.; Liu, Y.; Chen, D.; Wang, X.; Dou, L. Lithology identification using well logs: A method by integrating artificial neural networks and sedimentary patterns. J. Pet. Sci. Eng. 2019, 182, 106336. [CrossRef]19. Xie, Y.; Zhu, C.; Zhou, W.; Li, Z.; Liu, X.; Tu, M. Evaluation of machine learning methods for formation lithology identification: A comparison of tuning processes and model performances. J. Pet. Sci. Eng. 2018, 160, 182-193. [CrossRef]20. Zhu, L.; Li, H.; Yang, Z.; Li, C.; Ao, Y. Intelligent logging lithological interpretation with convolution neural networks. Petrophys. SPWLA J. Form. Eval. Reserv. Descr. 2019, 59, 799-810. [CrossRef]21. Imamverdiyev, Y.; Sukhostat, L. Lithological facies classification using deep convolutional neural network. J. Pet. Sci. Eng. 2019, 174, 216-228. [CrossRef]22. Li, Z.; Kang, Y.; Feng, D.; Wang, X.M.; Lv, W.; Chang, J.; Zheng, W.X. Semi-supervised learning for lithology identification using Laplacian support vector machine. J. Pet. Sci. Eng. 2020, 195, 107510. [CrossRef]23. Zeng, L.; Ren, W.; Shan, L. Attention-based bidirectional gated recurrent unit neural networks for well logs prediction and lithology identification. Neurocomputing 2020, 414, 153-171. [CrossRef]24. Li, D.; Zhao, J.; Liu, Z. A Novel Method of Multitype Hybrid Rock Lithology Classification Based on Convolutional Neural Networks. Sensors 2022, 22, 1574. [CrossRef]25. Li, S.; Liu, J.; Zhou, K. An Improved Deep Forest Model Combining Adaptive Synthetic Sampling for Automatic Lithology Identification. In Proceedings of the 2021 China Automation Congress (CAC), Beijing, China, 22-24 October 2021; pp. 1215-1220.26. Lin, J.; Li, H.; Liu, N.; Gao, J.; Li, Z. Automatic lithology identification by applying LSTM to logging data: A case study in X tight rock reservoirs. IEEE Geosci. Remote Sens. Lett. 2020, 18, 1361-1365. [CrossRef]27. Liu, N.; Huang, T.; Gao, J.; Xu, Z.; Wang, D.; Li, F. Quantum-enhanced deep learning-based lithology interpretation from well logs. IEEE Trans. Geosci. Remote Sens. 2021, 60, 4503213. [CrossRef]28. Gong, R.; Zhou, J. Research on Application of Deep Learning in Lithology Recognition of Oil and Gas Reservoir. In Proceedings of the 2021 IEEE International Conference on Power, Intelligent Computing and Systems (ICPICS), Shenyang, China, 29-31 July 2021; pp. 110-115.

29. Hu, X.; Cheng, L.; Zhou, H.; Zhang, C.; Zhang, X.; Li, D. Research on Lithology Identification Method Based on Stacked Capsule Auto-Encoder Network. In Proceedings of the 2021 IEEE 2nd International Conference on Big Data, Artificial Intelligence and Internet of Things Engineering (ICBAIE), Nanchang, China, 26–28 March 2021; pp. 381–386.30. Liu, H.; Wu, Y.; Cao, Y.; Lv, W.; Han, H.; Li, Z.; Chang, J. Well logging based lithology identification model establishment under data drift: A transfer learning method. Sensors 2020, 20, 3643. [CrossRef]31. Chang, J.; Kang, Y.; Li, Z.; Zheng, W.X.; Lv, W.; Feng, D.Y. Cross-domain lithology identification using active learning and source reweighting. IEEE Geosci. Remote Sens. Lett. 2020, 19, 8004805. [CrossRef]32. Zhou, K.; Li, S.; Liu, J.; Zhou, X.; Geng, Z. Sequential data-driven cross-domain lithology identification under logging data distribution discrepancy. Meas. Sci. Technol. 2021, 32, 125122. [CrossRef]33. Xu, T.; Zhang, W.; Li, J.; Liu, H.; Kang, Y.; Lv, W. Domain generalization using contrastive domain discrepancy optimization for interpretation-while-drilling. J. Nat. Gas Sci. Eng. 2022, 105, 104685. [CrossRef]34. Fan, G.; Chen, F.; Chen, D.; Dong, Y. Recognizing multiple types of rocks quickly and accurately based on lightweight CNNs model. IEEE Access 2020, 8, 55269–55278. [CrossRef]35. Chang, J.; Li, J.; Kang, Y.; Lv, W.; Xu, T.; Li, Z.; Xing Zheng, W.; Han, H.; Liu, H. Unsupervised domain adaptation using maximum mean discrepancy optimization for lithology identification. Geophysics 2021, 86, ID19-ID30. [CrossRef]36. Zhang, W.; Wang, J.; Li, K.; Liu, H.; Kang, Y.; Wu, Y.; Lv, W. Unilateral Alignment: An interpretable machine learning method for geophysical logs calibration. Artif. Intell. Geosci. 2021, 2, 192–201. [CrossRef]37. Wu, Y.; Yang, Y.; Lv, W.; Chang, J.; Li, Z.; Feng, D.; Xu, T.; Li, J. Robust unilateral alignment for subsurface lithofacies classification. IEEE Trans. Geosci. Remote Sens. 2021, 60, 4501913. [CrossRef]38. Xu, T.; Zhang, W.; Liu, H.; Kang, Y.; Lv, W. Subsurface Lithofacies Identification with Meta Learning. In Proceedings of the 2022 IEEE International Conference on Artificial Intelligence and Computer Applications (ICAICA), Dalian, China, 24–26 June 2022; pp. 440–444.39. Yu, C.; Wang, J.; Chen, Y.; Huang, M. Transfer learning with dynamic adversarial adaptation network. In Proceedings of the 2019 IEEE International Conference on Data Mining (ICDM), Beijing, China, 8–11 November 2019; pp. 778–786.40. Ben-David, S.; Blitzer, J.; Crammer, K.; Pereira, F. Analysis of representations for domain adaptation. Adv. Neural Inf. Process. Syst. 2006, 19, 1–8.41. Ronneberger, O.; Fischer, P.; Brox, T. U-net: Convolutional networks for biomedical image segmentation. In Medical Image Computing and Computer-Assisted Intervention—MICCAI 2015; Springer: Cham, Switzerland, 2015; pp. 234–241.42. Chang, J.; Li, J.; Kang, Y.; Lv, W.; Feng, D.; Xu, T. SegLog: Geophysical Logging; Segmentation Network for Lithofacies Identification. IEEE Trans. Ind. Inform. 2021, 18, 6089–6099. [CrossRef]43. Long, J.; Shelhamer, E.; Darrell, T. Fully convolutional networks for semantic segmentation. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition, Boston, MA, USA, 7–12 June 2015; pp. 3431–3440.44. Zhang, J.; Song, A. Application of Wavelet Analysis in Sequence Stratigraphic Division of Glutenite Sediments. In Proceedings of the 2010 International Conference on Challenges in Environmental Science and Computer Engineering, Wuhan, China, 6–7 March 2010; Volume 2, pp. 204–207.45. Pan, Y.; Meng, X.; Ge, M.; Liu, S. Automatic Stratification Strategy of Well Logging Curves Based on Wavelet Transform. In Proceedings of the 2017 International Conference on Smart Grid and Electrical Automation (ICSGEA), Changsha, China, 27–28 May 2017; pp. 219–222.46. Chen, S.; Liu, P.; Tang, D.; Tao, S.; Zhang, T. Identification of thin-layer coal texture using geophysical logging data: Investigation by Wavelet Transform and Linear Discrimination Analysis. Int. J. Coal Geol. 2021, 239, 103727. [CrossRef]

Disclaimer/Publisher's Note: The statements, opinions and data contained in all publications are solely those of the individual author(s) and contributor(s) and not of MDPI and/or the editor(s). MDPI and/or the editor(s) disclaim responsibility for any injury to people or property resulting from any ideas, methods, instructions or products referred to in the content.