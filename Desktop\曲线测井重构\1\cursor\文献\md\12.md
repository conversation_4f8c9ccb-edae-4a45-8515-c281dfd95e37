# Innovative lithology identification enhancement via the recurrent transformer model with well logging data

Youzhuang Sun a,b, <PERSON><PERSON> a,b,\*, <PERSON><PERSON> a,b

a China University of Petroleum (East China), College of Computer Science, Qingdao, Shandong, China  b China University of Petroleum (East China), Qingdao College of Software, Qingdao, Shandong, China

# ARTICLEINFO

# ABSTRACT

Keywords:  Lithology prediction  Logging parameters  Recurrent transformer  Machine learning

In the field of oil and gas exploration and development, accurately predicting lithology is crucial for strategic decision- making. Identifying subsurface rock types helps determine the distribution, quantity, and recoverability of oil and gas reserves, guiding exploratory efforts and increasing the likelihood of successful resource extraction. Our research focuses on enhancing lithology prediction accuracy through the development of the Recurrent Transformer model. This innovative model combines the Transformer architecture with recurrent elements, optimizing the processing of well logging data, inherently comprising time- series information. The recurrent structure effectively learns local contextual information, improving responsiveness to geological variations impacting lithology. Additionally, we integrated the Recurrent Scale- wise Attention (RSA) mechanism into our model, uniquely suited to well logging data. RSA captures and interprets multi- scale information by implementing attention mechanisms at varying scales, enhancing the model's understanding of sequence data. The recursive element within RSA further strengthens the model's ability to process contextual nuances. Key well log curves, including Density (DEN), Acoustic (AC), Gamma- ray (GR), and Compensated Neutron Log (CNL), serve as primary data sources for extracting geological features. These features are input into the Recurrent Transformer, establishing correlations between lithological characteristics and well logging parameters. Comparative analysis with leading- edge models using an experimental dataset revealed the Recurrent Transformer's high prediction accuracy and remarkable generalization capabilities across diverse lithological prediction tasks and geological conditions. This model represents a significant advancement in machine learning for well logging lithology prediction, providing geologists and engineers with a precise and efficient tool, enhancing resource exploration and development quality and reliability. The Recurrent Transformer model showcases the potential of integrating advanced machine learning in geosciences, opening new horizons in oil and gas resource exploration and utilization.

# 1. Introduction

Prediction of lithology (Corrino and Hovda, 2018) is crucial for determining the location and characteristics of oil and gas reservoirs. Different lithologic types possess distinct pore structures and permeability, directly influencing the accumulation of hydrocarbons in geological formations. Accurate lithology prediction (Zhang et al., 2018a) enables exploration personnel to identify optimal drilling locations and well network layouts, thereby avoiding ineffective drilling and resource wastage while enhancing the cost- effectiveness of exploration. Lithology prediction aids in identifying potential oil and gas reservoirs, as different lithologic types are typically associated with specific types of hydrocarbon reservoirs such as sandstone, shale, and carbonate. Accurate prediction of lithology assists exploration personnel in identifying potential oil and gas reservoirs, guiding subsequent exploration and development efforts, and improving the efficiency and success rate of resource exploration. Additionally, lithology prediction is crucial for the production management and optimization of oil and gas exploration and development. Understanding the distribution and characteristics of lithologic layers can guide well completion, reservoir development, and production adjustments, optimizing production techniques, increasing oil and gas recovery rates, and maximizing resource utilization. Machine learning models (Dev and Eden, 2018) possess the capability to discern complex patterns and features from vast amounts of logging data,

thereby improving the objectivity and speed of predictions and efficiently handling large- scale data. The integration of machine learning (Zhang et al., 2018b) into logging lithology prediction introduces a new technological paradigm, providing geologists with more comprehensive, accurate prediction tools and significantly contributing to the advancement of oil and gas exploration and development.

Lithology prediction has increasingly captured the attention of Earth sciences researchers, attracting extensive scrutiny and detailed investigations from many experts and scholars. The field's complexity and the diversity of lithological formations make it an area rife with challenges. In a notable 2018 study, Yunxin Xie (Xie et al., 2018) highlighted the critical importance of determining underground formation lithologies for petroleum exploration. This research assessed five different machine learning methods- Najve Baves, Support Vector Machine Artificial Neural Network, Random Forest, and Gradient Tree Boosting- on well log data from the Daniudui and Hangjinqi gas fields. The findings underscored the superiority of ensemble methods, especially Gradient Tree Boosting and Random Forest, which displayed lower prediction errors and robust performance, proving their effectiveness in classifying lithologies across various geological settings. In 2019, Jian Sun (Sun et al., 2019) underscored the critical role of lithology identification in petroleum exploration, particularly utilizing logging while drilling (LWD) equipment to achieve real- time and precise predictions. By analyzing data from conventional wireline logging (CWL) and LWD at Yan'an Gas Field, the study compared three machine learning algorithms: One- Versus- Rest Support Vector Machines (OVR SVMs), One- Versus- One Support Vector Machines (OVO SVMs), and Random Forest (RF). The RF classifier was highlighted as the optimal choice, demonstrating more than  $90\%$  accuracy, reduced training durations, and lower predictive errors, making it a preferred classifier for lithology identification during drilling operations. In 2020, Thiago Santi Bressan (Bressan et al., 2020) applied various machine learning techniques to classify lithologies using well log data from International Ocean Discovery Program offshore wells. Employing models based on IODP Expedition templates, the study achieved high accuracy rates, with Template1 excelling using MLP, and Templates 2 and 3 reaching over  $80.00\%$  accuracy with Random Forest, showcasing the potential of machine learning for quick and accurate geological data interpretation. Chunbi Jiang's 2021 research (Jiang et al., 2021) introduced a machine- learning framework aimed at enhancing lithology identification for reservoir characterization. This study improved upon previous methodologies by integrating stratigraphic sequence information as a new feature and testing both recurrent and convolutional neural networks on a publicly accessible dataset from the North Sea. The inclusion of geological constraints significantly boosted model performance, particularly highlighting the effectiveness of recurrent neural network approaches. In 2022, Thinesh Kumar (Kumar et al., 2022) tackled the challenges faced in coal exploration within India, especially in banded coal seams characterized by high carbon content and impurities. Utilizing supervised machine learning methods like Support Vector Machine, Decision Tree, Random Forest, Multi- Layer Perceptron, and Extreme Gradient Boosting, his study analyzed geophysical logs from the Talcher coalfield. The results demonstrated these models' effectiveness in classifying carbonaceous and non- coal lithofacies, providing a promising approach for addressing specific issues in Indian coal seams. Aditya Mishra's 2022 research (Mishra et al., 2022) critically evaluated the application of machine learning in automating well log creation to ensure reliable lithology prediction and improved reservoir characterization. His methodology included using a correlation tool for parametric correlations, applying machine learning algorithms to discover significant relationships between feature and target variables, and implementing unsupervised clustering classification and dimensionality reduction. The approach, tested on core holes from the Southern Newark Basin in the USA, surpassed existing methods, achieving an R- squared of 0.723 and a root mean squared error of 0.186 on the testing set. Lastly, Junlong Zhang's 2022 study (Zhang et al., 2022) benchmarked various machine learning models for lithology identification using well- logging data from the Baikouquan formation in the Junggar Basin, China. This research pitted linear and nonlinear classifiers against ensemble methods, identifying XGBoost as the most effective, with an overall accuracy of 0.882 and an AUC of 0.947. XGBoost's performance was notably exceptional in distinguishing potential reservoir lithologies, endorsing it as a powerful tool for efficient and precise lithology identification in petroleum exploration.

While these studies underscore significant progress in lithology prediction through well logging, it is crucial to recognize the specific limitations of each model. For instance, although Random Forest excels in classifying and regressing tasks with tabular data, its effectiveness in managing sequential well logging data is somewhat constrained. Similarly, OVO SVMs, while robust in static classification tasks, may falter in processing sequential well logging data. Recurrent Neural Networks, designed for sequence data handling, often struggle with long- term dependencies due to gradient issues. XGBoost, despite its prowess with tabular data, might not seamlessly adapt to the sequential nuances and temporal dependencies typical of well logging data. To overcome these challenges, we propose the Recurrent Transformer model for lithology prediction in well logging. This model incorporates two significant innovations:1. We introduce a novel Transformer architecture with a recurrent structure. This design is specifically adapted to handle well logging time- series data. The recurrent structure enables the model to capture temporal variations more effectively, enhancing its ability to interpret dynamic changes in well logging data. This is crucial for accurately predicting lithology, as understanding the temporal sequence is key in identifying different rock formations.2. We replace the traditional multi- head self- attention mechanism in the Transformer with a novel RSA mechanism, tailor- made for well logging data. The RSA mechanism applies attention at different scales, allowing the model to capture multi- scale information within input sequences. This approach means the model can simultaneously focus on both local and global contextual information within the data. This dual focus is particularly beneficial for well logging lithology predictions, where understanding both the broader geological context and detailed local variations is essential. Furthermore, the RSA's recursive processing enhances the model's ability to leverage contextual information across different time steps, improving its sensitivity to sequence features and temporal dependencies. In conclusion, the Recurrent Transformer model represents a significant step forward in machine learning for lithology prediction in well logging. By addressing the limitations of existing models and introducing specialized structures and mechanisms, this model offers enhanced accuracy and a deeper understanding of complex geological data. This advancement holds great promise for improving lithology identification, ultimately aiding in more effective exploration and extraction of oil and gas resources.

# 2. Methodology

# 2.1. Recurrent Transformer

Our proposed Recurrent Transformer model, grounded in the concept of Recurrent Structure, is an innovative approach tailored to enhance lithology prediction in well logging. This model integrates recurrent units, which are essential components in neural networks designed to handle sequential data. These units, prevalent in models (Shewalkar et al., 2019) like Recurrent Neural Networks (RNNs), Long Short- Term Memory networks (LSTMs), and Gated Recurrent Units (GRUs), are adept at capturing temporal dependencies in sequence data. For instance, RNNs, being the simplest form of recurrent units, generate output at each time step based on a linear combination of the current input and the hidden state from the previous time step. LSTMs, an advanced version, are equipped with three gate mechanisms (input, forget, and output gates) to control information flow, thereby efficiently addressing the vanishing gradient problem and capturing long- term

dependencies more effectively.

The Recurrent Structure Transformer presents notable advantages over traditional Transformer models, particularly in two areas:1. Well logging data inherently comprises time- series information. The Recurrent Structure Transformer is specially designed to capture these temporal variations more adeptly. Its structure enables a deeper understanding of dynamic changes in well logging data, thereby significantly improving lithology prediction accuracy.2. Lithology identification is often influenced by surrounding geological formations. A model incorporating a recurrent structure can learn local contextual information more effectively, increasing its sensitivity to the impact of geological changes on lithology.

As shown in Fig. 1, the Recurrent Transformer model comprises three recurrent units along with a refine module. Each recurrent unit is made up of an encoder, a Recurrent Transformer block (RTB), and a decoder. The encoder is responsible for extracting key features from the input sequence, particularly high- level abstract features crucial for resolving lithology prediction challenges. By processing the input sequence, the encoder maps it into a more compact, higher- level representation, thereby facilitating more effective processing in the recurrent units. While the Transformer structure, known for its attention mechanisms, is adept at capturing global dependencies in input sequences, the addition of the recurrent structure allows for capturing local temporal dependencies as well. This combination enables the model to grasp relationships at both global and local levels. The decoder in each unit is tasked with generating the target sequence, considering temporal dependencies that are crucial for time- series data like well logging. The inclusion of a "Refine module" (Ong et al., 2023) post- decoding further optimizes the model's output, ensuring it aligns closely with lithology prediction requirements. In neural network models, introducing a "Refine module" after the decoding process represents a significant advancement aimed at further improving the quality of the model's outputs and ensuring their close alignment with the specific requirements of lithology prediction tasks. This optimization module plays a crucial role in enhancing the quality of the model's outputs by finely adjusting the model's predictions to better meet the needs of geological applications. Typically, neural network models generate initial predictions during the decoding stage based on the features learned from input data. While the decoding stage is crucial for transforming abstract representations into concrete predictions, subsequent optimization steps are equally critical. They refine these predictions to enhance their accuracy, robustness, and applicability.

Referencing Fig. 2, each recurrent unit in our model comprises three components: an encoder, termed as  $f^{Enc}$ , a Recurrent Transformer block, labeled as  $f^{RTB}$ , and a decoder, known as  $f^{Dec}$ . We describe each recurrent unit as  $f_{i}^{RU}$ , where  $i^{th}$  represents the specific recurrent unit in question, and its functionality during the  $t^{th}$  iteration is outlined below:

![](images/6fd2eed0f52847ec060d73695811db0c40e50aac82f78a6b4c27fcc3a918b24e.jpg)  
Fig. 2. A Recurrent unit structure diagram.

$$
f_{i}^{RU}\left(\overline{y}_{i - 1}^{(t)},h_{i}^{(t)},c_{i}^{(t)}\right) = f^{Dec}\left(f_{i}^{RTB}\left(h_{i}^{(t)},c_{i}^{(t)}\right)\oplus f_{i}^{Enc}\left(\overline{y}_{i - 1}^{(t)}\right)\right) \tag{1}
$$

where  $\oplus$  denotes the element- wise addition. The Recurrent Transformer block  $f^{RTB}$  in the three RU extracts features of different scale sizes from the original logging data by adjusting the stride of the convolutional layer in encoder  $f^{Enc}$ . Then, decoder  $f^{Dec}$  in each RU uses transposed convolutional layers to recover the original log data size. In the transposed convolutional layer, a reverse process is employed through convolutional operations to increase the size of the output. This involves the amplification of kernels (also known as filters) through padding the blank areas of the input feature maps. The essence of transposed convolution lies in the reverse operation of the convolution process. Unlike a regular convolution operation, the kernels here perform an expansion operation on the input. By convolving the input, more features can be generated in the output, and through weight sharing, it ensures that the learned patterns remain consistent with the original input. A data consistency layer is added at the end of each decoder network, as shown below:

$$
\overline{y}_{i}^{(t)} = \text{Decoder}\left(f_{i}^{RU}\left(\overline{y}_{i - 1}^{(t)},h_{i}^{(t)},c_{i}^{(t)}\right),x\right) \tag{2}
$$

in this process, the Decoder layer is responsible for utilizing the outputs from the Attention layer and other contextual information to generate lithology predictions. Each Decoder layer consists of a fully connected feedforward network, facilitating non- linear transformations based on the self- attention layer. At this stage, the model needs to take into account the previous lithology prediction values and other factors that may influence lithology content.

![](images/137a10f0c94c53b81af2f8da5e25d3b55cceaed4290b1fc9cb989566af7702e9.jpg)  
Fig. 1. Recurrent Transformer structure diagram.

$$
\begin{array}{rl} & {\overline{y}_1^{(t)} = RU_1\left(\overline{y}^{(t)},h_1^{(t)},c_1^{(t)},x\right)}\\ & {\overline{y}_2^{(t)} = RU_2\left(\overline{y}_1^{(t)},h_2^{(t)},c_2^{(t)},x\right)}\\ & {\overline{y}_3^{(t)} = RU_3\left(\overline{y}_2^{(t)},h_3^{(t)},c_3^{(t)},x\right)}\\ & {\overline{y}^{(t + 1)} = Decoder\left(RM\left(\overline{y}_1^{(t)}\otimes \overline{y}_2^{(t)}\otimes \overline{y}_3^{(t)}\right),x\right)} \end{array} \tag{3}
$$

where  $\otimes$  denotes the channel- wise concatenation. In deep learning, a feature map is the output of an intermediate layer in a convolutional neural network, where each channel represents the network's response to different features of the input. The purpose of channel- wise concatenation is to merge feature information from different parts to enrich the model's expressive capability regarding input data. Specifically, for two feature maps with the same height and width but different numbers of channels, they can be concatenated using channel- wise concatenation. The resulting feature map retains the same height and width, while the number of channels is the sum of the channels from the two feature maps. Here, "RM" refers to Recurrent Memory (Bulatov et al., 2022). The RM fuses the outputs of recurrent units to generate  $\overline{y}^{(t + 1)}$ , which is the output of the current iteration and also serves as the input for the next iteration. Recurrent Memory (RM) plays a significant role in deep learning, particularly when dealing with sequential data. In models such as Recurrent Neural Networks (RNNs) and Long Short- Term Memory networks (LSTMs), Recurrent Memory is utilized to capture long- term dependencies within sequence data, thereby enhancing the model's expressive power and performance. In the context of well logging data processing, the introduction of Recurrent Memory offers new insights and methods for better understanding underground rock formations. The generation process of Recurrent Memory involves merging the outputs of recurrent units. Recurrent units typically refer to neural network units within models like RNNs or LSTMs, responsible for processing sequence data and producing corresponding representations. By integrating the outputs of these recurrent units, Recurrent Memory is generated. This process can be seen as continuously updating and refining the model's understanding and representation of the data by blending the current output with previous memories at each iteration step. The generated Recurrent Memory serves not only as the output of the current iteration but also as the input for the next iteration. This recursive structure enables the model to process and understand the data across multiple time steps, thereby better capturing long- term dependencies and temporal information within the data. This recursive nature provides an effective mechanism for the model to maintain the continuity and consistency of information when dealing with sequential data.

# 2.2. Recurrent Transformer block

A detailed view of the Recurrent Transformer block (RTB) is provided in Fig. 3, showcasing the implementation of Recurrent Pyramid Transformer Layers (RPTL). The core of this design is the Recurrent Scale- wise Attention (RSA) mechanism. The Recurrent Scale- wise

Attention (RSA) mechanism, as an attention mechanism applied in deep learning models, has shown tremendous potential in handling well logging data. Well logging data plays a crucial role in oil and gas exploration and development, recording various physical characteristics and properties of underground rock formations, providing valuable geological information to exploration personnel. However, due to the complexity and diversity of underground rock formations, relying solely on traditional data processing methods often fails to capture subtle differences in lithology and important features. The introduction of the RSA mechanism offers a new approach and method to address this challenge. The core advantage of the RSA mechanism lies in its recursive structure, which allows the model to iteratively refine attention weights over multiple steps. In other words, RSA can not only compute the importance of each part of the data, at once but also optimize these weights through multiple iterations, enabling the model to focus more accurately on key information at different scales. This recursive refinement process enables the model to gradually distill important features and structural information from the data, thereby enhancing its understanding of the hierarchical structure and detailed characteristics of underground rock formations. The operation of the RSA mechanism can be divided into the following steps: First, the RSA mechanism transforms well logging data into input representations that the model can process, typically converting curve data representing various physical properties of underground rock formations into vector form using embedding techniques. Second, in each recursive step, the RSA model calculates attention weights to determine the importance of each well logging curve in the input sequence, enabling it to focus on key information at different scales. Third, based on the calculated attention weights, the RSA model aggregates information from the input sequence to obtain a comprehensive representation that integrates the importance of different well logging curves at different scales. Finally, the representation after information aggregation is fed back into the model, together with the original input, to compute the attention weights for the next step, enabling the model to continuously optimize its understanding of underground rock formations. In summary, the RSA mechanism enables the model to better understand the complex information in well logging data through recursive iterations, thereby enhancing its ability to identify and understand underground rock formations. In the field of well logging, the RSA mechanism provides an effective method for dealing with complex geological structures and formation characteristics, contributing to the optimization of oil and gas exploration and development processes and improving resource utilization efficiency.

RSA offers several advantages over the traditional multi- head self- attention mechanism used in conventional Transformer models:1. RSA's ability to apply attention at different scales enables the model to concurrently focus on local and global contextual information. This feature is particularly beneficial for well logging lithology predictions.2. In lithology prediction, complex relationships often exist between well logging data at different time steps. RSA, through its recursive processing, aids the model in capturing and leveraging this contextual information, thus heightening its sensitivity to sequence features.

The RPTL within our model employs a local attention mechanism with a shifted window scheme, proving to be more efficient and superior across various tasks. Given that well logging data is typically collected

![](images/3cbc634cafde3db0835bb63984b481ad9c95abd387a37033fb2eae96a74fa715.jpg)  
Fig. 3. Recurrent Scale-wise Attention structure diagram.

with respect to depth or time, neighboring points often exhibit temporal variations and trends. The local attention mechanism is specially designed to capture these variations, leveraging associations between adjacent logging points to enhance prediction and analysis. This approach is crucial for interpreting well logging data, which often contains local structures where closer correlations exist between adjacent points. Regarding the input hidden state  $h^{(t)} \in R^{H \times W \times C}$ , our Recurrent Partitioned Transformer Layer (RPTL) initially utilizes  $M^{*}M$  non- overlapping local windows to divide  $h^{(t)}$  into segments, resulting in a feature size of  $\frac{H^{\times}W^{\times}}{M^{*}} \times M^{2} \times C$ . Here,  $M$  represents the dimension of each local window, and  $\frac{H^{\times}W^{\times}}{M^{2}}$  indicates the total count of these windows. Following this, our newly introduced Recurrent Self- Attention (RSA) mechanism independently computes self- attention for each window. Within this context, let's denote  $I \in R^{M^{2} \times C}$  as a feature of a local window. In RPTL, the query (Q), key  $(K)$ , and value  $(V)$  matrices are structured as follows:

$$
Q = IP_{Q},K = IP_{K},V = IP_{V} \tag{7}
$$

where  $P_{Q}, P_{K},$  and  $P_{V}$  are the corresponding projection matrices in attention scale heads. In the realm of attention mechanisms within deep learning, a crucial step involves the application of a linear transformation to the input via projection matrices. These matrices are instrumental in mapping the input into various representation spaces, each focusing on distinct aspects of the input data. This process is particularly evident in multi- head attention, where multiple attention heads operate in parallel, each equipped with its unique set of projection matrices. The use of different heads allows the model to concurrently learn and attend to varying relationships within distinct representation spaces. Following the processing by these heads, their outputs are aggregated—typically through concatenation—and subsequently integrated using another projection matrix.

As shown in Fig. 4, we compute the outputs of RSA as follows:

$$
\begin{array}{r}A t t e n t i o n\big(Q,K,V,c^{(t)}\big) = S o f t M a x\big(c^{(t + 1)}\big)V \end{array} \tag{8}
$$

In the context of time series data or tasks, recursive updates play a vital role. These updates ensure the retention and consideration of information from previous time steps, thereby enabling the model to construct more precise predictions or representations for the current time steps. The recursive nature of these models is essential for acknowledging and incorporating temporal dependencies within the data. Deep feature correlation refers to the interrelationships among different features as discerned by the model. The concept of recursive updates implies that the model can dynamically modify the correlation between these features across various time steps, adapting to the evolving nature of the time series data. At each time step  $t$ , the deep feature correlation  $c^{(t)}$  is updated recursively as follows:

$$
c^{(t + 1)} = \lambda \left(\frac{QK^T}{\sqrt{d}} +B\right) + (1 - \lambda)c^{(t)} \tag{9}
$$

where  $\lambda$  is the embedding dimension. The term "embedding dimension (Guo and Dai, 2022)" refers to the dimensions assigned to each input or feature in a model. Typically, data is transformed into a lower- dimensional space to facilitate better learning by the model, enabling it to more effectively capture and represent the inherent complex structures within the input data. Thus, the embedding dimension represents the size of each input or feature representation in this compressed, lower- dimensional space. In deep learning models, data is often presented in high- dimensional forms, where each feature may contain a large amount of information. However, such high- dimensional representations can lead to excessive model complexity, making it difficult to learn and generalize effectively. Therefore, by transforming the data into a lower- dimensional space, the complexity of the model can be effectively reduced, making it easier to capture important features within the data. A key component in these frameworks is the inclusion of a learnable relative position bias  $(B)$ . This mechanism, through the learning process, develops biases for relative positions within the data, enabling the model to account for relationships between different positions in a sequence. Such relative position biases are crucial for capturing the relative importance or relevance between various positions in the sequence, thus enhancing the model's understanding of local structures within the data. These biases are not static but are learned and refined during the training process.  $\lambda$  represents a learnable factor (Wang et al., 2017) that modulates the influence of deep feature correlations emanating from adjacent states. This weighting factor is critical in balancing the contribution of each feature within the model's overall architecture. As a learnable weight factor, it adjusts the impact of the correlation between deep features from neighboring states on the current state. Being learnable implies that the model can adaptively determine how to balance the influence of neighboring states on the current state as it undergoes training. This adaptability is crucial for the model to flexibly respond to the specific requirements of different tasks or datasets. Deep feature correlation, within this context, describes the interplay between learned deep features in the model, highlighting how different features mutually influence each other. By adjusting relative position biases and learnable weight factors, the model can dynamically tailor the deep feature correlation to better suit various scenarios and challenges. The workflow of the Recurrent Pyramid Transformer Layer (RPTL) can be summarized as a process that systematically integrates these elements—embedding dimensions, recursive updates, relative position biases, and learnable weight factors—into a cohesive framework. This integration facilitates the model's ability to process and interpret complex sequential data effectively, making it particularly suited for tasks involving time series analysis, such as well logging lithology prediction.

![](images/ea70b6b00c527f26c88ccaa8b4ca645fe92230af7b7b622979c8c5fdecedd4cc.jpg)  
Fig. 4. Diagram of the transition function in RPTL.

$$
\begin{array}{l}{I = RSA(LN(I)) + I}\\ {}\\ {I = MLP(LN(I)) + I} \end{array} \tag{10}
$$

In the context of advanced neural network architectures, such as the Recurrent Transformer model, two key components are Layer Normalization (LN) (Xu et al., 2019) and Multi- Layer Perceptron (MLP) (Kruse et al., 2022). Layer Normalization is an essential technique during the training phase of neural networks, where it normalizes the inputs of each layer. This normalization is crucial for accelerating the convergence of the model and enhancing its generalization across different datasets and scenarios. Specifically, Layer Normalization computes the mean and standard deviation of each layer's inputs and normalizes these inputs accordingly. This process ensures consistency in the input distributions across different layers during training, thereby improving the stability and efficiency of the model training. By standardizing the inputs of each layer, Layer Normalization addresses issues like gradient vanishing and explosion in deep neural networks, thus speeding up the convergence of the model. Moreover, by ensuring consistency in the distribution of inputs across layers, Layer Normalization also aids in improving the model's generalization, enabling it to perform well across various datasets and scenarios. A Multi- Layer Perceptron (MLP) is a

fundamental neural network structure composed of multiple layers, with each layer containing a network of interconnected neurons. These layers are fully connected, meaning each neuron in one layer is connected to every neuron in the subsequent layer. Neurons within these layers are connected by weights and activated through activation functions, allowing for nonlinear transformations between layers. The ability of MLP to perform nonlinear transformations enables the network to learn and represent more complex patterns and relationships within the data. MLP serves as a versatile neural network architecture widely employed across various machine learning tasks. It possesses scalability and adaptability, suitable for addressing multiple types of problems including classification, regression, clustering, and more. Typically, each layer of MLP comprises multiple neurons, where each neuron performs some linear transformations and activation functions before passing the results to the next layer. Through the stacking of multiple layers, MLP gradually learns and extracts abstract features from the data, combining them into higher- level representations for final task resolution.

As illustrated in Fig. 5, the schematic of a Recurrent Transformer block showcases the intricate process of sequential data handling. In this diagram,  $h(t)$  symbolizes the input feature map or hidden state at a particular time step  $t$ . The input features initially pass through a block embedding layer. This layer is a technique employed in Transformers where feature maps from well logging data are segmented into blocks. Each block is then mapped into a high- dimensional space, preparing it for processing by the Transformer. Following this, the input undergoes layer normalization, ensuring that the inputs between features are normalized. This step is pivotal in stabilizing the training process of deep neural networks, as it ensures uniformity in the data processed across different layers of the model. After normalization, the input is subjected to the Recurrent Scale- wise Attention (RSA) mechanism. This mechanism considers the previous sequence  $c(t)$  and is designed to map the original input into an abstract representation, thereby facilitating a more refined understanding and processing by the model. Subsequently, the transformed input is processed through the Multilayer Perceptron. In the context of the Transformer, the MLP block typically consists of two linear layers, separated by a nonlinear activation function. This configuration allows the MLP to perform complex transformations and learn from the data effectively. The process concludes with Patch Unembedding, essentially the reverse of the block embedding step. This step involves mapping the high- dimensional features back into the original feature map space. The resulting encoded output  $y(t)$  is then combined with other features at the current time step, leading to the generation of  $h(t + 1)$ . This design ensures that the model not only considers the original feature information but also integrates information from the prediction at the previous time step. Such integration is crucial for the model to effectively capture dynamic relationships within the sequential data, enabling more accurate predictions and representations in time- series tasks like well logging lithology prediction.

# 3. Experimental data analysis and preprocessing

In our study, we focus on the well logging data from the Tarim Oilfield, located within the expansive Tarim Basin, which spans geographical coordinates from  $82^{\circ}$  to  $90^{\circ}\mathrm{E}$  and  $38^{\circ}$  to  $44^{\circ}\mathrm{N}$ . This region is renowned as one of China's key areas for oil and gas exploration and development, boasting significant reserves of these resources. The well logging data harnessed in our research includes various parameters: Photoelectric Index (PE), Gamma- Ray (GR), Acoustic Time Difference (AC), Compensated Neutron Log (CNL), and Density (DEN). These parameters were gathered using specialized logging tools—sensors installed in oil or geological drilling wells—designed to measure geological and geophysical properties along the borehole during or after drilling. This data is crucial for obtaining a comprehensive understanding of geophysical and geochemical characteristics of the subsurface formations. Our study employs this well logging data to distinguish between two primary lithologies: mudstone and sandstone. The initial training labels for lithology interpretation come from core samples obtained through drilling. Core sampling involves extracting rock samples from the subsurface layers through drilling boreholes. These core samples provide highly detailed and accurate geological information, including lithology, lithofacies, grain size, porosity, and more. The accuracy of lithology identification through core sampling stems from the direct acquisition of actual rock samples from the subsurface, as opposed to relying on indirect well logging curve data. These core samples undergo thorough laboratory analysis, including microscopic observation, physical property testing, chemical composition analysis, and more. Through these laboratory analyses, geologists can accurately determine the composition, structure, and properties of the rocks, thereby identifying their specific lithological types. For the two common lithological types, mudstone and sandstone, they exhibit distinct characteristics in core samples: Mudstone is a rock composed of fine- grained particles (less than  $0.0625\mathrm{mm}$ ) and typically appears gray, black, or brown. In core samples, mudstone often displays fine- grained, homogeneous features, possibly containing small fragments or rock debris. Microscopic observations reveal mudstone's fine- grained, dense structure with no apparent grain sorting. Sandstone, on the other hand, consists of sand- sized particles ( $0.0625\mathrm{mm} - 2\mathrm{mm}$ ) and usually appears gray, yellow, or red. In core samples, sandstone typically exhibits larger grain size and well- sorted features, allowing clear observation of pore spaces between sand grains. Physical property tests often show sandstone's higher porosity and permeability, attributed to its larger grain size and greater pore volume. Figs. 6 and 7 in our paper illustrate the well logging parameters and how they correspond to the lithologies of mudstone and sandstone. In well logging lithology prediction, the primary reason for using lithological data obtained from core sampling as ground truth labels is its perception as the most reliable and accurate geological lithology reference. Core sampling involves the direct extraction of samples from subsurface rocks, which undergo detailed laboratory analysis on the surface to determine the specific types and properties of the rocks. Consequently, core sampling is regarded as one of the most trusted methods for lithology identification by geologists. The

![](images/df8121bf872e1ee53439ad329c23d7d16646c07f4e3d0cc0dbdfd2af502d7c4f.jpg)  
Fig. 5. Recurrent Transformer block structure diagram.

![](images/fe03b2030dd77ab68e85e56cab51672c14179452c75838f4350950e9219521a8.jpg)  
Fig. 6. Well logging parameters corresponding to lithology for the first dataset (test set).

![](images/5d931c7f182c956b47142661da9fe4151ba6e541427448393f7202a95e1eb572.jpg)  
Fig. 7. Well logging parameters corresponding to lithology for the second dataset (test set).

advantages of using lithological data obtained from core sampling as ground truth are as follows: Core sampling provides actual samples of geological rocks, and laboratory analysis accurately determines the composition, structure, and properties of the rocks. These laboratory analysis results are often considered the gold standard for geological lithology, possessing high accuracy and reliability. Core samples are obtained through subsurface drilling operations, directly extracting samples from subsurface rocks, thus enhancing their reliability. In contrast, other logging methods (such as well logging curve data) may be influenced by the complexity of the formations and instrument errors, leading to certain uncertainties.

The intricate relationship between well logging parameters (PE, DEN, AC, GR, CNL) and lithology (mudstone and sandstone) is not only physically significant but also crucial for accurate geological interpretations and decision- making in the oil and gas industry. Well logging, a technique used to collect detailed geological and petrophysical data, involves the measurement of various parameters such as the Photoelectric Index (PE), density (DEN), acoustic (AC), gamma ray (GR), and compensated neutron logging (CNL). Each of these parameters reveals specific properties of underground formations that are essential for identifying rock types and their characteristics (Wang et al., 2022). The Photoelectric Index (PE) is particularly useful for distinguishing between different rock types based on their ability to absorb X- rays. This absorption is closely related to the formation's electron density and atomic number. Typically, shale formations, which are a type of mudstone, show higher PE values. This is due to their higher electron density and atomic number which contribute to greater X- ray absorption. Conversely, sandstones, characterized by lower electron densities and atomic numbers, absorb fewer X- rays, resulting in lower PE values. This distinction helps in identifying the presence of these rocks within a drilled section of the earth. Density logging measures the density of a formation, or its mass per unit volume, and provides critical insights into the rock's composition. Mudstones generally exhibit higher densities as they are composed of mineral- rich particles with lower porosity. This characteristic implies that they can bear significant geological weight without large void spaces. Sandstones, however, often consist primarily of quartz and are noted for their higher porosity, which typically results in lower density readings on logs. Acoustic logging evaluates the speed of sound through geological formations and is another critical tool in identifying lithological characteristics. The velocity of sound through a material gives an indication of its acoustic impedance, which in turn depends on the material's density and elastic properties. Mudstones, with their higher densities, allow sound waves to travel more quickly, thus indicating smaller acoustic (AC) values. On the other hand, the larger porosity of sandstone slows down sound propagation, resulting in larger AC values. This difference in sound velocity between mudstones and sandstones is pivotal for interpreting subsurface geology accurately. Gamma Ray logging is used to measure the natural radioactivity of formations, specifically the presence of elements like thorium, potassium, and uranium. Mudstones are usually rich in these radioactive elements and hence show higher responses on gamma ray logs. This high radioactivity is indicative of the clay and organic material typically present in mudstones. In contrast, sandstones contain lower concentrations of these radioactive elements, which results in lower gamma ray responses. These differences are valuable for distinguishing between sandstone and mudstone layers. Finally, Compensated Neutron Logging (CNL) differentiates between lithologies based on their neutron scattering characteristics. Neutrons, when emitted into a formation, interact with the nuclei of the present elements. Sandstones, which are generally composed of lighter elements like hydrogen, carbon, and oxygen, exhibit

a higher degree of neutron scattering. This high scattering is indicative of the presence of fluids and gases within the sandstone's porous structure. Mudstones, composed of heavier elements like silicon, aluminum, and potassium, tend to absorb neutrons, resulting in lower neutron scattering. This absorption pattern helps in identifying denser, less porous materials.

During the data preprocessing stage, the first step is to handle missing values, as some parameters in the oilfield data used in this study contain missing values. Interpolation is employed in this study to address missing values, which refers to estimating missing values based on information from known data. We fill in missing values using the mean of the known data. Specifically, for each missing numerical value, we calculate the mean of the known data and replace the missing value with this mean. This helps maintain data consistency and integrity enabling the model to better utilize available information during the training process. Handling missing values is an important step in data preprocessing, contributing to improved stability and performance of the model. Next, outlier handling is carried out. We detect outliers using the Z- Score method (Hafeez et al., 2022), which measures the degree to which a data point deviates from the mean, calculated as:

$$
Z = \frac{X - \mu}{\sigma} \tag{12}
$$

Where,  $X$  is data point,  $\mu$  is the standard deviation, and we set the data points of  $\sigma > 3$  as the outlier. We use the mean of the known data instead of the value of the outlier. Figs. 8 and 9 show boxplots of the preprocessed data.

The data is then normalized. Data normalization is a common data preprocessing technique that aims to scale data to a similar scale to ensure that different features have equal weight on the model. Data normalization helps to improve the stability and convergence speed of model training. We use Min- Max standardized (Mazziotta and Pareto, 2022) practices to process log data. For each feature, the data is scaled to a specified range, typically [0, 1]. The specific operation formula is as follows:

$$
X_{normalized} = \frac{X - X_{min}}{X_{max} - X_{min}} \tag{13}
$$

where,  $X$  is the original eigenvalue,  $X_{min}$  and  $X_{max}$  are the minimum and maximum values of the feature, respectively. The benefit of data normalization lies in its ability to eliminate scale differences between features, making the model more robust. During the model training process, normalized data tends to converge more easily because the weights of each feature are more uniform, allowing the model to learn the relationships between data more accurately. Additionally, normalization helps prevent certain features from dominating the model training, thereby improving the model's generalization ability.

In this study, we have chosen data from six wells in the study area. For the first dataset, 10000 sets of data were selected. The dataset was split into a training set and a test set using an 7:3 ratio, with 7000 sets of data allocated for training and 3000 sets for testing. The same procedure was followed for the second dataset (The second set of data is completely different from the first), where again 10000 sets of data were chosen and divided into training and test sets using an 7:3 ratio, with 7000 sets for training and 3000 sets for testing.

# 4. Analysis of prediction results

Table 1 delineates the specific parameters utilized across various machine learning models. In the Recurrent Transformer model, both the Encoder Activation and Decoder Activation functions are designated as ReLU (Yarotsky, 2017). The choice of ReLU, a non- linear activation function, is pivotal in enabling the model to discern non- linear patterns in the dataset. ReLU's computational efficiency stems from its simplicity, focusing only on positive values without complex calculations. The learning rate within this model is established at 0.001. This rate, commonly adopted as a starting point, is instrumental in avoiding overly abrupt updates to the model's parameters, thereby facilitating a

![](images/25099be15f31e50107a48177f7f742ea12bc57f6e7bef4eef83303028bd42dbb.jpg)  
Fig. 8. Box plot of the preprocessed data for the first test set.

![](images/0cc9cb641f3bbf501ee0c9322ebc30c9aec4ccfcd9c8004ea9caae6f9a1f6abb.jpg)  
Fig. 9. Box plot of the preprocessed data for the second test set.

more consistent convergence process. The Adam (Kingma and Ba, 2014) optimizer, known for its adaptive learning rate mechanism, is employed. This approach dynamically adjusts the learning rate for individual parameters throughout the training, enhancing the efficacy of updating the model's parameters. A Dropout (Baldi and Sadowski, 2014) setting of 0.3 is integrated into the Recurrent Transformer model. Dropout, as a regularization strategy, randomly deactivates certain neurons during training. This process lessens the model's reliance on specific neurons, which in turn bolsters its ability to generalize. The Transformer model mirrors these specifications to maintain uniformity in variable control.

For the BiGRU model, each layer is equipped with 128 GRU units. This quantity is a critical hyperparameter influencing the model's capacity. A capacity that is too large may fit the training data well but risks overfitting. Conversely, a smaller capacity might underfit. The choice of 128 units presents a balanced approach, moderately managing the trade- off between capacity and overfitting susceptibility. The learning rate here too is set at 0.001, considered a prudent initial value that neither hampers the model's convergence nor excessively prolongs the training phase. The Dropout rate and the activation function, set at 0.3 and ReLU respectively, align with the Transformer model to ensure consistency in experimental conditions. This uniformity in parameter settings extends to GRU, LSTM, and BiGRU models, maintaining control over experimental variables.

In the XGBoost model, the learning rate is aligned with the aforementioned models at 0.001. The base learner count (Mehmood and Asghar, 2021) is fixed at 100, a figure often regarded as balancing computational efficiency, training duration, and model effectiveness. This number also aids in reducing the risk of overfitting. The max depth parameter is limited to 10, preventing the model from becoming overly sensitive to training data and averting overfitting. The Min child weight is set to 5, a median value that mitigates overfitting while maintaining generalization. The RF model, belonging to the same ensemble learning category as XGBoost, adheres to these parameter selections for consistency.

The SVM model employs RBF kernel functions (Jayasumana et al.,

2015). These functions are instrumental in mapping the input space to a higher-dimensional feature space, thus linearizing problems in this expanded space that are non-linear in the original input space. This feature makes SVM particularly adept at handling non-linear issues. The regularization parameter is set at 0.1, balancing the model's fit on training data against the complexity penalty, thereby controlling overfitting. The Gamma parameter of the RBF kernel, set at 1, regulates the width of the kernel function.

Figs. 10 and 11 illustrate the results of the first set of data on lithology prediction. From Fig. 10, it is clear that our proposed model exhibits a lower error rate in the identification task, demonstrating significantly superior performance compared to other models. This outcome is further detailed in Fig. 11 and Table 2, where our model excels in multiple key indicators (Chicco and Jurman, 2023), including True Positive Rate (TPR), True Negative Rate (TNR), Positive Predictive Value (PPV), Negative Predictive Value (NPV), and Accuracy. Firstly, TPR measures the model's ability to correctly identify positive instances, i.e., the proportion of correctly identified positive samples among actual positive samples. Our model performs highest in TPR, indicating its reliability in recognizing true positive instances compared to other models. TNR measures the model's ability to correctly identify negative instances, i.e., the proportion of correctly identified negative samples among actual negative samples. Similarly, our model performs best in TNR, showcasing its outstanding performance in correctly excluding negative instances. PPV represents the probability of actual positive instances when the model predicts positive, while NPV represents the probability of actual negative instances when the model predicts negative. The high levels of these two indicators indicate the model's relatively high accuracy in predicting positive and negative instances. Finally, Accuracy is an overall evaluation metric, measuring the proportion of correctly classified samples across all instances. Our model also achieves the highest level of accuracy, further validating its exceptional performance throughout the identification task.

The Recurrent Transformer model achieves greater accuracy in well logging lithology prediction than the traditional Transformer model due

Table 1 Machine learning model parameters.  

<table><tr><td>Models</td><td>Parameters</td><td>Values</td><td></td></tr><tr><td>Recurrent</td><td>Encoder Activation function</td><td>ReLU</td><td></td></tr><tr><td rowspan="5">Transformer</td><td>Decoder Activation function</td><td>ReLU</td><td></td></tr><tr><td>Learning rate</td><td>0.001</td><td></td></tr><tr><td>Optimizer</td><td>Adam</td><td></td></tr><tr><td>Maximum iterations</td><td>1000</td><td></td></tr><tr><td>Dropout</td><td>0.3</td><td></td></tr><tr><td rowspan="7">Transformer</td><td>Encoder Activation function</td><td>ReLU</td><td></td></tr><tr><td>Decoder Activation function</td><td>ReLU</td><td></td></tr><tr><td>Learning rate</td><td>0.001</td><td></td></tr><tr><td>Optimizer</td><td>Adam</td><td></td></tr><tr><td>Maximum iterations</td><td>1000</td><td></td></tr><tr><td>Dropout</td><td>0.3</td><td></td></tr><tr><td>Activation function</td><td>ReLU</td><td></td></tr><tr><td rowspan="6">BiGRU</td><td>The number of GRU units in each layer</td><td>128</td><td></td></tr><tr><td>Learning rate</td><td>0.001</td><td></td></tr><tr><td>Optimizer</td><td>Adam</td><td></td></tr><tr><td>Maximum iterations</td><td>1000</td><td></td></tr><tr><td>Dropout</td><td>0.3</td><td></td></tr><tr><td>Activation function</td><td>ReLU</td><td></td></tr><tr><td rowspan="6">BILSTM</td><td>The number of LSTM units in each layer</td><td>128</td><td></td></tr><tr><td>Learning rate</td><td>0.001</td><td></td></tr><tr><td>Optimizer</td><td>Adam</td><td></td></tr><tr><td>Maximum iterations</td><td>1000</td><td></td></tr><tr><td>Dropout</td><td>0.3</td><td></td></tr><tr><td>Activation function</td><td>ReLU</td><td></td></tr><tr><td rowspan="6">GRU</td><td>The number of GRU units in each layer</td><td>128</td><td></td></tr><tr><td>Learning rate</td><td>0.001</td><td></td></tr><tr><td>Optimizer</td><td>Adam</td><td></td></tr><tr><td>Maximum iterations</td><td>1000</td><td></td></tr><tr><td>Dropout</td><td>0.3</td><td></td></tr><tr><td>ReLU</td><td></td><td></td></tr><tr><td rowspan="6">LSTM</td><td>The number of LSTM units in each layer</td><td>128</td><td></td></tr><tr><td>Learning rate</td><td>0.001</td><td></td></tr><tr><td>Optimizer</td><td>Adam</td><td></td></tr><tr><td>Maximum iterations</td><td>1000</td><td></td></tr><tr><td>Dropout</td><td>0.3</td><td></td></tr><tr><td>activation function</td><td>ReLU</td><td></td></tr><tr><td rowspan="5">XGBoost</td><td>The number of GRU units in each layer</td><td>128</td><td></td></tr><tr><td>Learning rate</td><td>0.001</td><td></td></tr><tr><td>Number of base learners</td><td>100</td><td></td></tr><tr><td>Max depth</td><td>10</td><td></td></tr><tr><td>Min child</td><td>5</td><td></td></tr><tr><td rowspan="3">RF</td><td>Number of base learners</td><td>100</td><td></td></tr><tr><td>Max depth</td><td>10</td><td></td></tr><tr><td>Min samples leaf</td><td>5</td><td></td></tr><tr><td rowspan="3">SVM</td><td>Kernel function</td><td>RBF function</td><td></td></tr><tr><td>Regularization parameter</td><td>0.1</td><td></td></tr><tr><td>Radial Basis Function Core Width (Gamma)</td><td>1</td><td></td></tr></table>

to its advanced structural innovations. It incorporates a recursive structure along with a Recurrent Scale- wise Attention mechanism. This recursive design is crucial for tracking long- distance dependencies within sequence data, enhancing the model's proficiency in correlating diverse positions. Additionally, the Recurrent Scale- wise Attention mechanism boosts the ability of the model to discern intricate spatiotemporal relationships, thereby elevating lithology prediction accuracy. On the other hand, the traditional Transformer model might not adequately manage such recursive connections within sequences. For well logging lithology prediction, it is vital to accurately model recursive geological structures, and the Recurrent Transformer model, with its specialized recursive architecture, is particularly well- equipped for this task.

The Transformer model demonstrates superior rock type identification accuracy over traditional time series prediction models like BiGRU, BiLSTM, GRU, and LSTM. This model employs self- attention mechanisms that provide a significant advantage in identifying relationships across various points in the input sequence. Geological structures in well logging often exhibit long- distance dependencies that are critical for accurate prediction, and the Transformer's self- attention capability allows for a more adaptable approach to these complex spatiotemporal dynamics. Traditional time series models are generally restricted by their memory architecture or fixed window sizes, which hampers their ability to effectively manage long- distance dependencies within intricate geological formations. The Transformer model, on the other hand, utilizes global sequence information without the constraints of window size, which is essential for achieving precise rock type classification.

Compared to ensemble learning models like XGBoost and RF, time series prediction models exhibit better accuracy in handling well logging data, which is inherently temporal. Time series models are specifically designed to address such data, offering robust mechanisms for modeling trends that reveal essential geological structure insights crucial for lithology prediction. These models are adept at capturing long- range dependencies due to features like gating, which is vital for interpreting temporal sequences effectively. In contrast, ensemble learning models, which rely on aggregating weaker classifiers, often fall short in accurately modeling extended temporal relationships inherent in geological data.

Furthermore, the performance of SVM in lithology identification falls behind that of both ensemble learning models and time series prediction models. SVM, which primarily relies on linear classification, struggles with the complex nonlinear relationships prevalent in well logging data. The intricate nonlinear patterns between lithology and logging measurements present a significant challenge for SVM, which lacks the flexibility to adapt to such complexities. Moreover, the high dimensionality often associated with well logging data exacerbates SVM's vulnerability to the "curse of dimensionality," leading to reduced

![](images/dc3bc6dd240eef5546104ad9db8abbfb0c28e0fc2f51e6bf20de9aa8ff394c94.jpg)  
Fig. 10. True lithology and predicted lithology by the machine learning models in the first set of test data.

![](images/7cbf5e4674b6cf2198c5829948461a25ce8892286fabca3da7da300cd9a8aa15.jpg)  
Fig. 11. Confusion matrix for nine machine learning models (the first dataset).

Table 2 Values of the evaluation indicators of the machine learning models in the first data set (Mudstone is positive example, Sandstone is negative example)  

<table><tr><td>Models</td><td>True Positive Rate, TPR</td><td>True Negative Rate, TNR</td><td>Positive Predictive Value, PPV</td><td>Negative predictive value, NPV</td><td>Accuracy</td></tr><tr><td>Recurrent Transformer</td><td>98.14%</td><td>97.88%</td><td>95.98%</td><td>99.03%</td><td>97.97%</td></tr><tr><td>Transformer (Yang et al., 2023a)</td><td>95.41%</td><td>95.35%</td><td>91.39%</td><td>97.57%</td><td>95.37%</td></tr><tr><td>BiGRU (Sun et al., 2023)</td><td>92.96%</td><td>92.72%</td><td>86.85%</td><td>96.22%</td><td>92.80%</td></tr><tr><td>BiLSTM (Yang et al., 2023b)</td><td>90.71%</td><td>91.86%</td><td>85.22%</td><td>95.03%</td><td>91.47%</td></tr><tr><td>GRU (Sun et al., 2023)</td><td>88.17%</td><td>89.18%</td><td>80.82%</td><td>93.58%</td><td>88.83%</td></tr><tr><td>LSTM (Yang et al., 2023b)</td><td>86.41%</td><td>85.79%</td><td>75.88%</td><td>92.43%</td><td>86.00%</td></tr><tr><td>XGBoost (Zhong et al., 2020)</td><td>82.70%</td><td>83.66%</td><td>72.37%</td><td>90.33%</td><td>83.33%</td></tr><tr><td>RF (Feng et al., 2021)</td><td>82.01%</td><td>82.04%</td><td>70.27%</td><td>89.81%</td><td>82.03%</td></tr><tr><td>SVM (Bolandi et al., 2017)</td><td>78.98%</td><td>80.02%</td><td>67.17%</td><td>88.04%</td><td>79.67%</td></tr></table>

efficiency in training and prediction phases. In contrast, both ensemble learning and time series prediction models are better suited to manage high- dimensional data and effectively discern relationships across various features, resulting in enhanced performance in complex geological settings.

efficiency in training and prediction phases. In contrast, both ensemble learning and time series prediction models are better suited to manage high- dimensional data and effectively discern relationships across various features, resulting in enhanced performance in complex geological settings.Figs. 12 and 13 display the lithology identification results for the second data set. Table 3 presents the evaluation metrics of the machine learning models. Judging from the accuracy, the ranking is as follows: Recurrent Transformer  $\gimel$  Transformer  $\gimel$  BiGRU  $\gimel$  BiLSTM  $\gimel$  GRU  $\gimel$  LSTM  $\gimel$  XGBoost  $\gimel$  RF  $\gimel$  SVM. The Recurrent Transformer's superior performance stems from its innovative design, which marries the strengths of traditional recurrent neural networks (RNNs) with those of the Transformer architecture. RNNs are known for their ability to capture temporal relationships in data sequences, a vital attribute for modeling phenomena that evolve over time, such as the geological layers encountered in well logging. The Transformer brought a revolutionary self- attention mechanism that allows models to weigh the importance of different parts of the input data independently of their sequence in the data stream. This flexibility enables the Transformer to handle dependencies between various positions within the data more effectively than models limited by the sequential processing of inputs. Building on this, the Recurrent Transformer integrates recursive processing, enabling it to handle sequences with complex hierarchical structures or patterns that unfold over various scales, which are common in geological data. In comparison, traditional sequential models like BiGRU (Bidirectional Gated Recurrent Units), BiLSTM (Bidirectional Long Short- Term Memory), GRU (Gated Recurrent Units), and LSTM (Long Short- Term Memory) have shown considerable effectiveness in modeling time- series data. These models are particularly adept at capturing both long- term and short- term dependencies within sequences, an essential feature for analyzing temporal relationships in well logging data. Their bidirectional variants (BiGRU and BiLSTM) enhance this capability by processing data in both forward and reverse directions, thus gaining a fuller context and improving model performance over unidirectional setups. However, these traditional RNNs can suffer from issues such as gradient vanishing or exploding, particularly when dealing with very long sequences. This limitation often hampers their application in scenarios involving extensive data stretches, typical of well logs that can span vast depths. Moreover, ensemble learning models like XGBoost and Random Forest (RF) have been widely successful in various machine learning tasks, especially those involving non- temporal structured data. These models use multiple learning algorithms to obtain better predictive performance than could be obtained from any of the constituent learning algorithms alone. However, their effectiveness diminishes when dealing with time- series data, as their fundamental designs do not naturally accommodate the temporal dependencies crucial for accurate time- dependent predictions like lithology characterization in well logs. The Support Vector Machine (SVM) model, often used for its robust classification capabilities in linear separable scenarios, faces challenges in the well logging context. SVM struggles with modeling the nonlinear relationships and temporal dependencies inherent in well logging data, due to its primary design as a linear classifier. This limitation restricts its effectiveness in complex, multi- faceted geological datasets where the ability to capture and model intricate relationships within the data significantly influences predictive accuracy.

# 5.Conclusion

Our research delves into the potential and efficacy of the Recurrent Transformer model in predicting lithology from well logs. Through a comparative analysis with various models, spanning both traditional machine learning techniques and alternative deep learning frameworks, we observed that the Recurrent Transformer model demonstrates superior performance in this domain. Our findings can be summarized as follows:

1. Integrating a recurrent framework within the Transformer model significantly boosts its effectiveness. Given that well log data inherently comprises time-series elements, the inclusion of a recurrent system within the Transformer model proves more adept at grasping these temporal patterns. This enhances its capacity to accurately interpret changes in well logging data, which in turn, improves the precision of lithology predictions. Additionally, lithology predictions are often affected by neighboring geological structures. A model embedded with a recurrent system is better equipped to assimilate local contextual information, thereby heightening its responsiveness to geological variations impacting lithology.

2. The Recurrent Scale-wise Attention (RSA) mechanism outperforms the conventional multi-head self-attention mechanism found in traditional Transformer models. RSA's ability to apply attention at varying scales allows it to effectively discern multi-scale intricacies within input sequences. This capability enables the model to concurrently concentrate on both local and broader contextual information, a feature exceedingly beneficial for well log lithology predictions. In this context, the intricate interplay between well log data across different time points can be critical. RSA, through its recursive methodology, aids the model in capturing and exploiting this contextual data more efficiently, thus enhancing its sensitivity to sequential attributes.

In the future, we can focus on further optimizing the architecture and parameters of the Recursive Transformer model. By adjusting the model's structure and hyperparameters, we can attempt to enhance its adaptability and generalization across different geological structures and data types. For example, we can explore more complex attention mechanisms or recursive structures to better capture long- term

![](images/7a2b28e88d80b5e2643750498fba052a8f6d763bbe0c31910d62a69f6f8d6372.jpg)  
Fig. 12. True lithology and predicted lithology by the machine learning models in the second set of test data.

![](images/32a03e8dd67a8395d3ef0834ac5d5c6388669d68bbd3bbc13af85973302691f0.jpg)  
Fig. 13. Confusion matrix for nine machine learning models (the second dataset).

Table 3 alues of the  t f th  i  th  t  i  i.  

<table><tr><td>Models</td><td>True Positive Rate, TPR</td><td>True Negative Rate, TNR</td><td>Positive Predictive Value, PPV</td><td>Negative predictive value, NPV</td><td>Accuracy</td></tr><tr><td>Recurrent Transformer</td><td>97.95%</td><td>97.64%</td><td>98.54%</td><td>96.70%</td><td>97.83%</td></tr><tr><td>Transformer (Yang et al., 2023a)</td><td>95.59%</td><td>95.88%</td><td>97.42%</td><td>93.03%</td><td>95.70%</td></tr><tr><td>BiGRU (Sun et al., 2023)</td><td>94.08%</td><td>92.64%</td><td>95.41%</td><td>90.58%</td><td>93.53%</td></tr><tr><td>BiLSTM (Yang et al., 2023b)</td><td>90.04%</td><td>91.07%</td><td>94.25%</td><td>84.90%</td><td>90.43%</td></tr><tr><td>GRU (Sun et al., 2023)</td><td>87.57%</td><td>89.23%</td><td>92.97%</td><td>81.52%</td><td>88.20%</td></tr><tr><td>LSTM (Yang et al., 2023b)</td><td>85.04%</td><td>86.43%</td><td>91.07%</td><td>78.02%</td><td>85.57%</td></tr><tr><td>XGBoost (Zhong et al., 2020)</td><td>82.72%</td><td>83.89%</td><td>89.31%</td><td>74.90%</td><td>83.17%</td></tr><tr><td>RF (Feng et al., 2021)</td><td>81.65%</td><td>82.66%</td><td>88.45%</td><td>73.46%</td><td>82.03%</td></tr><tr><td>SVM (Bolandi et al., 2017)</td><td>78.53%</td><td>80.65%</td><td>86.85%</td><td>69.77%</td><td>79.33%</td></tr></table>

dependencies and spatiotemporal relationships in time- series data. Additionally, we can try different initialization methods and regularization techniques to improve the stability and generalization of the model. We can expand the scope of the research further to explore a wider range of geological problems and data types. Although our study primarily focuses on predicting lithology from well logging data, similar methods and models can also be applied to other geological problems such as seismic interpretation, reservoir prediction, etc. Furthermore, we can consider using different types of well logging data, such as sonic logging, resistivity logging, etc., to obtain a more comprehensive geological understanding. Moreover, we can explore how to integrate domain expertise from the field of geology into the model to improve its understanding and interpretation of geological attributes. Geologists and earth scientists often possess rich domain knowledge and experience, which can help us better interpret and understand geological features in well logging data. Therefore, collaborating with professionals from the geological field can aid in developing models that are more explanatory and interpretable.

# CRediT authorship contribution statement

Youzhuang Sun: Writing - original draft. Shanchen Pang: Writing - review & editing. Yongan Zhang: Methodology.

# Declaration of competing interest

The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.

# Data availability

The data that has been used is confidential.

# References

Baldi, P., Sadowski, P., 2014. The dropout learning algorithm. Artif. Intell. 210, 78- 122. Bolandi, V., Kadkhodaie, A., Farzi, R., 2017. Analyzing organic richness of source rocks from well log data by using SVM and ANN classifiers: a case study from the Kazhdumi formation, the Persian Gulf basin, offshore Iran. J. Petrol. Sci. Eng. 151, 224- 234. Bressan, T.S., de Souza, M.K., Girelli, T.J., et al., 2020. Evaluation of machine learning methods for lithology classification using geophysical data. Comput. Geosci. 139, 104475. Bulatov, A., Kuratov, Y., Burtsev, M., 2022. Recurrent memory transformer. Adv. Neural Inf. Process. Syst. 35, 11079- 11091. Chicco, D., Jurman, G., 2023. A statistical comparison between Matthews correlation coefficient (MCC), prevalence threshold, and Fowlkes- Mallows index. J. Biomed. Inf., 104426. Corina, A.N., Hovda, S., 2018. Automatic lithology prediction from well logging using kernel density estimation. J. Petrol. Sci. Eng. 170, 664- 674. Dev, V.A., Eden, M.R., 2018. Evaluating the boosting approach to machine learning for formation lithology classification[M]. Computer aided chemical engineering 44, 1465- 1470. Elsevier. Feng, R., Grana, D., Balling, N., 2021. Imputation of missing well log data by random forest and its uncertainty analysis. Comput. Geosci. 152, 104763. Guo, J., Dai, Q., 2022. Graph clustering via variational graph embedding. Pattern Recogn. 122, 108334.

Hafeez, B., Li, X., Kabir, M.H., et al., 2022. Measuring bank risk: forward- looking z- score. Int. Rev. Financ. Anal. 80, 102039. Jayasumana, S., Hartley, R., Salzmann, M., et al., 2015. Kernel methods on Riemannian manifolds with Gaussian RBF kernels. IEEE Trans. Pattern Anal. Mach. Intell. 37 (12), 2464- 2477. Jiang, C., Zhang, D., Chen, S., 2021. Lithology identification from well- log curves via neural networks with additional geologic constraint. Geophysics 86 (5), IM85- IM100. Kingma, D.P., Ba, J., 2014. Adam: a method for stochastic optimization. arXiv preprint arXiv:1412.6980 1412. Kruse, R., Mostaghim, S., Borgelt, C., et al., 2022. Multi- layer perceptrons[M]// Computational Intelligence: a Methodological Introduction. Springer International Publishing, Cham, pp. 53- 124. Kumar, T., Seelam, N.K., Rao, G.S., 2022. Lithology prediction from well log data using machine learning techniques: a case study from Talcher coalfield, Eastern India. J. Appl. Geophys. 199, 104605. Mazziotta, M., Pareto, A., 2022. Normalization methods for spatio- temporal analysis of environmental performance: Revisiting the Min- Max method. Environmetrics 33 (5), e2730. Mehmood, Z., Asghar, S., 2021. Customizing SVM as a base learner with AdaBoost ensemble to learn from multi- class problems: a hybrid approach AdaBoost- MSVM. Knowl. Base Syst. 217, 106845. Mishra, A., Sharma, A., Patidar, A.K., 2022. Evaluation and development of a predictive model for geophysical well log data analysis and reservoir characterization: machine learning applications to lithology prediction. Natural Resources Research 31 (6), 3195- 3222. Ong, J.C.H., Lau, S.L.H., Ismadi, M.Z.P., et al., 2023. Feature pyramid network with self- guided attention refinement module for crack segmentation. Struct. Health Monit. 22 (1), 672- 688. Shewalkar, A., Nyavanandi, D., Ludwig, S.A., 2019. Performance evaluation of deep neural networks applied to speech recognition: RNN, LSTM and GRU. J. Artif. Intell. Soft Comput. Res. 9 (4), 235- 245. Sun, J., Li, Q., Chen, M., et al., 2019. Optimization of models for a rapid identification of lithology while drilling- A win- win strategy based on machine learning. J. Petrol. Sci. Eng. 176, 321- 341. Sun, Y., Zhang, J., Yu, Z., et al., 2023. The bidirectional gated recurrent unit network based on the inception module (Inception- BiGRU) predicts the missing data by well logging data. ACS Omega 8 (30), 27710- 27724. Wang, J., Zhu, X., Gong, S., et al., 2017. Attribute recognition by joint recurrent learning of context and correlation[C]. Proceedings of the IEEE International Conference on Computer Vision 531- 540. Wang, Z., Nie, X., Zhang, C., et al., 2022. Lithology classification and porosity estimation of tight gas reservoirs with well logs based on an equivalent multi- component model. Front. Earth Sci. 10, 850023. Xie, Y., Zhu, C., Zhou, W., et al., 2018. Evaluation of machine learning methods for formation lithology identification: a comparison of tuning processes and model performances. J. Petrol. Sci. Eng. 160, 182- 193. Xu, J., Sun, X., Zhang, Z., et al., 2019. Understanding and improving layer normalization. Adv. Neural Inf. Process. Syst. 32. Yang, L., Feinel, S., Wang, S., et al., 2023a. Porosity and permeability prediction using a transformer and periodic long short- term network. Geophysics 88 (1), WA293- WA308. Yang, W., Xia, K., Fan, S., 2023b. Oil logging reservoir recognition based on TCN and SA- BILSTM deep learning method. Eng. Appl. Artif. Intell. 121, 105950. Yarotsky, D., 2017. Error bounds for approximations with deep ReLU networks. Neural Network. 94, 103- 114. Zhang, G., Wang, Z., Chen, Y., 2018a. Deep learning for seismic lithology prediction. Geophys. J. Int. 215 (2), 1368- 1387. Zhang, G., Wang, Z., Chen, Y., 2018b. Deep learning for seismic lithology prediction. Geophys. J. Int. 215 (2), 1368- 1387. Zhang, J., He, Y., Zhang, Y., et al., 2022. Well- logging- based lithology classification using machine learning methods for high quality reservoir identification: a case study of Baikouquan formation in mahu area of Junggar basin, NW China. Energies 15 (10), 3675. Zhong, R., Johnson, Jr R., Chen, Z., 2020. Generating pseudo density log from drilling and logging- while- drilling data using extreme gradient boosting (XGBoost). Int. J. Coal Geol. 220, 103416.