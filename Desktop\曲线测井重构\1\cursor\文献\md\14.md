# Performance of the synergetic wavelet transform and modified K-means clustering in lithology classification using nuclear log

<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>*, <PERSON><PERSON>*, <PERSON>, <PERSON>, Bo Guo

Institute of Geophysics and Geomatics, China University of Geosciences (Wuhan), Lume Road 388, 430074 Wuhan, Hubei, China

# ARTICLEINFO

Article history:  Received 14 August 2015  Received in revised form 27 December 2015  Accepted 28 February 2016  Available online 3 March 2016

Keywords:  CCSD- MH  Well- log  Wavelet transform  K- means clustering  Formation interface  Lithology classification

# ABSTRACT

Accurate lithology identification is fundamentally crucial to reservoir evaluation from geophysical well logs. However, the traditional way of lithological identification is carried out in laboratory, which is not only expensive, but also time consuming in its interpretation. In this study, the synergetic wavelet transform and modified K- means clustering techniques are performed to classify metamorphic rocks from Chinese Continental- Scientific Drilling Main Hole (CCSD- MH). At the beginning, different wavelet functions in different well logs are presented to detect lithologic interfaces. Meanwhile, the Haar wavelet and GR are determined to be the optimum wavelet function and well log, and the range of the optimum scales is about  $8 - 15\mathrm{m}$  in the reference well. After that, a fast and practical K- means clustering algorithm is employed to make a classification of stratigraphy into 5 groups, which are demarcated from the performance of wavelet transform. The results achieved are in accordance with the stratigraphic column and have a higher accuracy compared to the previous studies, indicating that the combination of the wavelet transform and modified K- means clustering can improve the accurate rate for the classification of metamorphic rocks in CCSD- MH.

$\mathfrak{E}$  2016 Elsevier B.V. All rights reserved.

# 1. Introduction

On petroleum exploration and exploitation, the lithological characterization description is routine and essential work in dealing with geophysical and geological data. Well logs, which can reflect the physical properties of the subsurface material, are useful for the analysis of lithological characterization. However, the inherent complexity of well logs which is complicated and diversified makes well log interpretation difficult (Crain, 1986; Dewan, 1983). Traditionally, most previous analysis of well logs involved direct interpretation through visual inspection and comparison between different well logs which depends on the experiences of the workers (Chandrasekhar and Eswara Rao, 2012). Also, the detailed lithological characterization description can be obtained by direct measurement of cores in laboratory, while the core recovery is time consuming and expensive (Chang et al., 2000).

To address this issue, recently many lithology identification methods have been proposed. The most important of these is signal processing technique, such as Fourier analysis (Weedon, 2003), Walsh transform (Lanning and Johnson, 1983; Maiti and Tiwari, 2005), and wavelet transform (Bolton et al., 1995; Guyodo

et al., 2000; Briqueu et al., 2010; Arabjamaloei et al., 2011). Lanning and Johnson (1983) were the first time to apply the Walsh transform, which is a low- pass filter method, to analyze well logs for detecting the rock boundary. They used the depth corresponding to the beginning of the data as the first boundary value, however, the first step may not always be the boundary. After that, Maiti and Tiwari (2005) performed the Walsh transform in analyzing well logs to develop an automated method to detect the lithologic boundaries, and with the specific criteria. The Fourier analysis can only tell whether or not some particular frequency (representative of formation) of interest are present in the signal, and thus they fail to explain which frequencies (formations) occur at what depths (Chandrasekhar and Eswara Rao, 2012). Wavelet transform of well logs can detect the formation interfaces, and providing the thickness and spatial localization of different formations.

Pan et al. (2008) analyzed geophysical well logs though wavelet transform and Fourier transform technique, and obtained the similar formation interfaces. However, their method can only work on the well log signals which are both fluctuation and intensity. Chandrasekhar and Eswara Rao (2012) performed different wavelet functions on different well logs to identify formation interfaces. And they found that Gauss 1 was the most appropriate wavelet for the determination of the space- location of formation boundary. Recently, Teresa et al. (2013) applied wavelet functions analysis in well logs (radioactivity, resistivity and sonic) to identify

![](images/78ac86b0e0eb4b4dafb8de66e93c70c6557b2bf1dc920a46a635ad463c73c93a.jpg)  
Fig. 1. Block diagram for decomposition into three level of a signal.

![](images/ba67cdc5a531c87d7e441d3b22ba5837213fd9e54ee3aa60c647271d8ee506ec.jpg)  
Fig. 2. Simplified geological location of CCSD-MH by a star symbol (Zhang et al., 2006).

lithology. They analyzed that the rocks types can be identified based on the correlations between color patterns represented in wavelet scalograms and electro- facies associations, but without giving the specific criteria.

In the present study, we have proposed the application of the synergetic wavelet transform and modified K- means clustering technique to classify metamorphic rocks using CCSD- MH drilling data. Firstly, we have performed the continue wavelet transform (CWT) which applied different wavelet functions (Haar wavelet, Symlet2 wavelet, Morlet wavelet, Gauss3 wavelet) in different well logs (GR, TH, Rt) from CCSD- MH for formation interfaces detection. Also, discrete wavelet transform (DWT) in GR well log was also displayed, the formation boundary information was included by the detailed coefficients. After that, a fast and practical K- means clustering algorithm was employed to make a classification of stratigraphy into 5 groups, which were demarcated from the performance of wavelet transform. All results were compared and corroborated by core data. Finally, the data of the whole CCSD- MH with depths from 110 to  $5000\mathrm{m}$  were processed, and the accuracy of the results have been significantly improved.

![](images/6517e6261fd87852b82bf27e662006f24c6bda47a6bcea7132658d44a81b353f.jpg)  
Fig. 3. Well logs (GR, TH and Rt) of CCSD-MH, the depths from 640.0 to  $800.0\mathrm{m}$

# 2. The methodology of wavelet transform

# 2.1. wavelet transform

The kernel function of wavelet transform is defined as follow (Pan et al., 2005; Yu, 2013):

![](images/c45e3c5cae73c20a823c86758cb354dc5245b42ac5f73b943cd5b1d0a621b7ef.jpg)  
Fig.4. a) Sttigphic cumn and wale t sgrams f (b) GR (c) TH (d) Rt with Ha wale t rnsfom. Note: 1-utile clogite, 2-phenge clogite, 3-qtz clogite, 4- a article.)

$$
\psi_{a,b}(t) = \frac{1}{\sqrt{a}}\psi \left(\frac{t - b}{a}\right) \tag{1}
$$

Where the function  $\psi (t)$  is called the mother wavelet,  $a$  is the scale factor, that determines the wavelength, and  $b$  represents the shift of the wavelet (Goupillaud et al., 1985).

A wavelet transform which shifts and dilations are continuously varied is called a continuous wavelet transform (CWT) (Goupillaud et al., 1985). The formula is given by

$$
WT_{f}(a,b) = \frac{1}{\sqrt{a}}\int_{-\infty}^{+\infty}f(t)\psi^{*}\left(\frac{t - b}{a}\right)dt \tag{2}
$$

The variation of wavelet transformation is represented by an integer is called a discrete wavelet transform (DWT) (Daubechies, 1988). In the DWT, the scale and shift parameters are discretized as  $a = a_0^i$  and  $b = jb_0$ , and the formula is given by

$$
WT_{f}(i,j) = \frac{1}{\sqrt{a_{0}^{i}}}\int_{-\infty}^{+\infty}f(t)\psi^{*}\left(\frac{t - jb_{0}}{a_{0}^{i}}\right)dt \tag{3}
$$

Where  $f(t)$  is the original signal.

When continuous wavelet transform is performed on well logs, wavelet coefficient scalogram is charted in a color scale where the low energy and high energy conical color shape represent the space- localization of formation boundaries. The edge effects are removed by the method of outward symmetric extension of edge data (Strang and Nguyen, 1995).

# 2.2. Wavelet decomposition

In the DWT, a signal can be decomposed into several lower- resolution components which are the approximation wavelet coefficients (cA) and detailed wavelet coefficients (cD). At the first level, the detailed wavelet coefficients can be decomposed again, which produce the approximation wavelet coefficients and detailed wavelet coefficients at the next level. Fig. 1 shows a schematic representation of decomposition of a signal into three levels. And the  $j$ th level decomposition can be expressed as follow (Misit et al., 2000; Yue et al., 2004):

![](images/36d6998a80b1049cdfb8d3a112be2e4bd37ee65fb9e05c3b9380914658297735.jpg)  
Fig.5. a 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20. article.)

$$
cA_{k}^{0} = \sum_{n}f(n)\phi (n - k) \tag{4}
$$

which is orthogonal to the wavelet function  $\psi$ .  $k$  in Eqs. (4)- (8) is equivalent to phase (b) in Eq. (1). Wavelet decomposition is seem to be that a signal is applied to pass two filters, a low- pass filter that produces the approximation wavelet coefficients and a high- pass filter that produces the detailed wavelet coefficients. The approximation wavelet coefficients passes the two filters iteratively, decomposing into the approximation and the detailed wavelet coefficients again.

In the wavelet decomposition, the well logs were decomposed to approximation coefficients from the low- pass filter and detailed coefficients from the high- pass filter. Thus, the detailed wavelet coefficients, representing the high- frequency components of the signals and including the information of formation boundaries, are used for detecting the stratigraphic interfaces. When the consideration of the decomposition level is small, wavelet coefficients obtained from wavelet transformation in well log is related to thin lithological changes identification, when considering large level of decomposition, wavelet transform detects the thick stratigraphic interfaces, some thin formation may be missed. Therefore, we choose the one- level decomposition based on the appearance of thin formations in CCSD- MH.

(4) (5) (6)

$$
cA_{k}^{j} = \sum_{n}h_{0}(n - 2k)cA_{n}^{j - 1} \tag{6}
$$

Where

$$
h_{ok} = \frac{1}{\sqrt{2}}\int \psi \bigg(\frac{t}{2}\bigg)\bar{\phi} (t - k)dt \tag{7}
$$

$$
h_{1k} = \frac{1}{\sqrt{2}}\int \psi \bigg(\frac{t}{2}\bigg)\bar{\phi} (t - k)dt \tag{8}
$$

Where  $cA_{k}^{j}$  and  $cD_{k}^{j}$  are the approximation and detailed wavelet coefficients at the  $j$ th level decomposition.  $\phi$  is a scale function,

![](images/059ff5f3d148dd106fd93b6369796b87fa1c7d00b28b7d127e17332e2f61a914.jpg)  
Fig. 6. Five -level decomposition of GR well log with Haar wavelet function.

In this study, Haar wavelet, Symlet2 wavelet, Morlet wavelet and Gauss3 wavelet are considered to determine the optimum wavelet function based on the correlation between stratigraphic column and wavelet coefficient scalograms.

# 3. Geology and well logs of the study area

The drill site of the CCSD- MH is located near Maubei village (Fig. 2;  $N34^{\circ}25^{\prime}$ ,  $E118^{\circ}40^{\prime}$ ), about  $17~\mathrm{km}$  southwest of Donghai in the southern segment of the Sulu UHP terrane (Wang et al., 2013). The drilling hole has met more than 50 kinds of metamorphite rocks (Xu, 2004; Zhang et al., 2004). The main category of metamorphic rocks includes orthogneiss, amphibolites, eclogite, paragneiss, ultramafic rocks, and so on. The subcategory of eclogite includes rutile eclogite, phengite eclogite, quartz eclogite and retrograded eclogite. The subcategory of amphibolites includes chlorite amphibolites and plagioclase amphibolites. (Zhang et al., 2000, 2003; Ya et al., 2000; Liu et al., 2004, 2005). Nuclear well log (GR, U, TH, K, DEN and CNL) can play a significant role (Niu et al., 2008) in identifying the metamorphic rocks.

Fig. 3 shows GR, TH and Rt well logs of CCSD- MH from the depth  $640.0 - 800.0\mathrm{m}$  the signal fluctuations exhibit different intensity and variability. The complexity of the signals reflects different rock properties. The corresponding lithological characterization is drawn by core recovery and geologists, seven kinds of metamorphite rocks included, rutile eclogite, phengite eclogite, quartz eclogite, retrograded eclogite, orthogneiss, paragneiss and serpentinite.

# 4. Wavelet analysis and discussion

# 4.1. Wavelet analysis

In this section, CWT was performed on different well logs using the Haar wavelet function. Several well logs, such as GR, TH, Rt, were used to select the optimum one. Due to large fluctuations in the resistive log, generally its analysis and interpretation is based on the logarithm (Archie, 1942).

According to the previous results about the application of wavelet transform to well logs (Pan et al., 2008; Chandrasekhar and Eswara- Rao, 2012; Teresa et al., 2013), Fig. 4 shows the comparison between stratigraphic column and wavelet coefficient scalograms of GR, TH, Rt well logs using Haar wavelet function. The formation interfaces can be observed based on the correlations between

![](images/da87b19dd98d1be3889815b513b8138c67bd6989b76ebe29ada8b698f0ac7c27.jpg)  
Fig. 7. Comparison of (a) stratigraphic column of CCSD-MH with (b) wavelet coefficient scalogram  $(8 - 15\mathrm{m})$  (c) the detailed wavelet coefficient curve in DWT. Note: 1-rutile eclogite, 2-phengite eclogite, 3-quartz eclogite, 4-retrograded eclogite, 5-orthogneiss, 6-paragneiss, 7-serpentinite. (For interpretation of the references to color in this figure, the reader is referred to the web version of this article.).

conical color shapes represented in wavelet scalograms and stratigraphic column, and the tip of conical color shapes point out the interfaces spatial location. According to the observation of the three wavelet coefficient scalograms, we have found that GR well log have shown acceptable resolution in identifying the formation interfaces. However, the wavelet coefficient scalogram of TH well log shows unclear interfaces from the depth  $740.0 - 755.0\mathrm{m}$ . And the wavelet coefficient scalogram of Rt well log can only detect several obvious boundary interfaces, some thin formations are neglected.

Fig. 5 shows the comparison between stratigraphic column and wavelet coefficient scalograms of GR well log using Haar, Symlet2, Morlet, Gauss3 wavelet functions. As shown in Fig. 5(b), the wavelet scalogram of GR well log in Haar wavelet, the blue conical color shape is corresponding to the GR log data that changes from low to high, and the red conical color shape is corresponding to the GR log data that changes from high to low (Fig. 2). However, Fig. 5(c) shows the wavelet scalogram of GR well log using symlet2 wavelet function, which exhibit an inverse behavior, and the identification performance is not as good as that shown in Fig. 5 (b), so do the Fig. 5(d) and (e), the wavelet scalogram of GR well log using morlet and Gauss3 wavelet functions.

Fig. 6 shows five- level decomposition of GR well log with Haar wavelet function, it is not clear for us to detect formation interfaces as the level decomposition increases except one- level wavelet coefficients. The scales corresponds to level one is  $2.5\mathrm{m}$ , which is the largest value to interfaces detection that can be found the most of all.

Fig. 7 shows the stratigraphic column, the scalogram  $(8 - 15\mathrm{m})$  and the detailed wavelet coefficients curve. The red and blue lines occurred in alternating (Fig. 7(b)), in qualitative terms, match with the presence of formation interfaces, allowing the identification.

Table 1 The comparison table between stratigraphic column and interpretation results.  

<table><tr><td>Lithology</td><td>Tops formation (stratigraphic column) (meters)</td><td>Tops forma-tion (Wavelet transform) (meters)</td><td>Formation thick-ness (strati-graphic column) (meters)</td><td>Formation thickness (Wavelet transform) (meters)</td></tr><tr><td>6</td><td>643.6</td><td>643.8</td><td>8.8</td><td>7.9</td></tr><tr><td>7</td><td>652.4</td><td>651.7</td><td>25.6</td><td>26.8</td></tr><tr><td>3</td><td>678.0</td><td>678.5</td><td>1.0</td><td>0.8</td></tr><tr><td>7</td><td>679.0</td><td>679.3</td><td>4.2</td><td>4.0</td></tr><tr><td>1</td><td>683.2</td><td>683.3</td><td>2.0</td><td>1.2</td></tr><tr><td>1</td><td>685.2</td><td>684.5</td><td>2.2</td><td>2.5</td></tr><tr><td>5</td><td>687.4</td><td>687.0</td><td>4.0</td><td>4.3</td></tr><tr><td>2</td><td>691.4</td><td>691.3</td><td>1.6</td><td>2.7</td></tr><tr><td>1</td><td>693.0</td><td>694.0</td><td>11.4</td><td>10.3</td></tr><tr><td>7</td><td>704.4</td><td>704.3</td><td>4.1</td><td>4.6</td></tr><tr><td>4</td><td>708.5</td><td>708.9</td><td>5.0</td><td>4.5</td></tr><tr><td>1</td><td>713.5</td><td>713.4</td><td>6.7</td><td>7.5</td></tr><tr><td>6</td><td>720.2</td><td>720.9</td><td>5.3</td><td>5.7</td></tr><tr><td>1</td><td>725.5</td><td>725.6</td><td>11.5</td><td>10.7</td></tr><tr><td>6</td><td>737.0</td><td>737.3</td><td>8.2</td><td>8.6</td></tr><tr><td>4</td><td>745.2</td><td>745.2</td><td>1.9</td><td>1.6</td></tr><tr><td>6</td><td>747.1</td><td>747.8</td><td>9.7</td><td>9.0</td></tr><tr><td>4</td><td>756.8</td><td>756.8</td><td>2.0</td><td>1.2</td></tr><tr><td>6</td><td>758.8</td><td>758.0</td><td>7.7</td><td>8.1</td></tr><tr><td>4</td><td>766.5</td><td>766.1</td><td>1.0</td><td>1.6</td></tr><tr><td>6</td><td>767.5</td><td>767.7</td><td>1.7</td><td>2.7</td></tr><tr><td>4</td><td>769.2</td><td>770.4</td><td>2.8</td><td>3.0</td></tr><tr><td>6</td><td>772.0</td><td>773.4</td><td>15.5</td><td>15.0</td></tr><tr><td>4</td><td>787.5</td><td>788.4</td><td>2.7</td><td>1.8</td></tr><tr><td>6</td><td>790.2</td><td>790.2</td><td>1.3</td><td>1.3</td></tr><tr><td>4</td><td>791.5</td><td>791.5</td><td>3.8</td><td>2.5</td></tr><tr><td>6</td><td>795.3</td><td>794.0</td><td></td><td></td></tr></table>

Note: 1-rutile eclogite; 2-phengite eclogite; 3-quartz eclogite; 4-retrograded eclogite; 5-orthogneiss; 6-paragneiss; 7-serpentinite.

When the scale is less than  $8\mathrm{m}$  the red and blue lines may not appear, and the scale is more than  $15\mathrm{m}$  the red and blue lines would be thicker, not accuracy for identify the formation interfaces. Thus, the optimum scales range is about  $8 - 15\mathrm{m}$  in identifying CCSD- MH formation interfaces.

As shown in Fig. 7(c), in the DWT, the detailed wavelet coefficients are obtained from GR well log of the one- level decomposition using the Haar wavelet function. The adjacent negative and positive wavelet coefficient values show the upper and lower boundaries of the formation. When the value of GR well log changes from high value to low value, the wavelet coefficient is positive. When the well log changes from low value to high value, the wavelet coefficient is negative. Therefore, the zone between the two corresponding wavelet coefficients is shown to be a specific formation. One red oblique line is drawn from the positive to negative pairs of wavelet coefficients, showing one formation. However, compared with the wavelet coefficient scalogram, the detailed wavelet coefficients curve is less accurate if scale was chosen much larger, some thin formations would be missed.

Based on the analysis above, the wavelet coefficient scalogram of GR well log using Haar wavelet function provides evidence that can detect the formation interfaces, meanwhile, giving the thickness and depth of the formations. Thus, the Haar wavelet and GR is the optimum wavelet type and well log in identifying the CCSD- MH formation interfaces. That is because the Haar wavelet can captures the abrupt change exhibited on GR well log signals.

In general, the comprehensive interpretation between wavelet scalogram (scale from 8- 15) and the detailed wavelet coefficients (GR well log in one- level decomposition using the Haar wavelet) can make identification of formation interfaces in CCSD- MH. Taking the reference well with the depth ranging from 640.0 to  $800.0\mathrm{m}$  as example, the comparison between stratigraphic column and interpretation results using wavelet transform is shown in Table 1. If stratigraphic column and interpretation result of one formation interface space- location are ranged in  $\pm 1.0\mathrm{m}$  it can be regarded as one interface. According to the stratigraphic column, 26 formation interfaces have been found from the depth 640.0-  $800.0\mathrm{m}$  in CCSD- MH, the accuracy is about  $84.6\%$

# 4.2. Lithology identification

In order to identify the lithology of all the formations which are demarcated by the performance of wavelet transform, a fast and practical K- means clustering algorithm (Yang et al., 2015) is employed to make a classification of these formations into 5 groups, which are orthogneiss, amphibolites, eclogite, paragneiss, ultramafic rocks (Zhang et al. 2000, 2003).

Based on the traditional K- means clustering algorithm, Euclidean distance was replaced by Mahalanobis distance, and the initial cluster centers were acquired from the average of characteristic values, in addition, added weight value in each characteristic value of the objective function. After that, a lithology recognition model named modified K- means clustering is established (Yang et al., 2015). The objective function of modified K- means cluster algorithm can be defined as:

$$
F_{i}(X,C) = \min \left(\sum_{k = 1}^{Z}\sum_{i = 1}^{m}\sqrt{w_{1}(x_{i1} - c_{k1})^{2 - 1}(x_{i1} - c_{k1})^{T} + \dots + w_{n}(x_{in} - c_{kn})^{2 - 1}(x_{in} - c_{kn})^{T}}\right) \tag{9}
$$

Where  $w_{j}(j = 1,2,\dots,n)$  is the weight of each characteristic value,

![](images/5006cfc61cf598f317dc43a87719f4e9bcb1fa4ae19b3f82241164fa357327ee.jpg)  
Fig. 8. The camprision of lithology classfication reults with methods of modifed K-meas cluster and self-organizing map neural network with whole well driling dat (For interpretation of the references to color in this figure, the reader is referred to the web version of this article.)

$\boldsymbol{\Sigma}$  is the covariance matrix,  $C_k = \{c_{k1},c_{k2},\dots,c_{kn}\} ,(k = 1,2,\dots,z)$  is the cluster centers.  $X_{k} = [x_{ij}],(i = 1,2,\dots,m;j = 1,2,\dots,n)$  are the indeterminate samples. The detail calculation of the parameters are seen Yang et al. (2015).

Based on the stratigraphic column from the depth 110.0-  $5000.0\mathrm{m}$  in CCSD- MH, 560 formation interfaces are obtained. Compared with the comprehensive interpretation of wavelet transform, 536 formation interfaces are detected, while 486 interfaces are correct, the accuracy is  $86.79\%$

According to the corresponding relationship between the well logs and lithology, 7 well logs are selected, they are AC, DEN, PE, RILD, CNL, GR and K. The rocks distributed from 100 to  $5000\mathrm{m}$  especially in the depth interval, the logs are affected heavily by the temperature and pressure. Therefore, the borehole environment corrections to the logs is necessary. After doing them, some changes have happen to some well logs, AC is smaller, DEN is bigger, and CNL is smaller.

The performance of modified K- means clustering algorithm: the correction logs of 536 formations are input this algorithm, the cluster number is setting 5. Stop running this algorithm when the ending condition  $\epsilon < 0.0001$  .The result is follows as Fig. 8, the highest correct classification rate is achieved for ultramafic rocks  $(94.67\%)$  and the lowest correct classification rate is obtained for amphibolites  $(70.58\%)$  .And the correct classification rate of othogneiss, paragneiss and ecolgite are  $94.51\%$ $77.92\%$  and  $83.75\%$  respectively.

Compared with the results obtained from Konate et al. (2015), who made a classification of formations with self- organizing map neural network in whole CCSD- MH, our results have improved the accuracy in identifying the whole CCSD- MH, as shown in Fig. 7. In his study, a total of 33,326 data points derived from RILD, AC, DEN, Pe, GR, U and CNL were used as an input pattern to a self- organizing map neural network to classify lithology in five categories: orthogneiss, paragneiss, ecolgite, amphibolite and ultramafic rocks. In my study, the classification of paragneiss, othogneiss, amphibolites and ultramafic rocks are more accurate than those achieved by Konate et al. (2015). However, the ecolgite is less accurate, which may be due to the fact that variation of mineral composition results in many types of eclogites, such as rutile eclogite, phengite eclogite, quartz eclogite, retrograded eclogite, and so on. Moreover, the application of the synergetic wavelet transform and modified K- means clustering technique make a classification of metamorphic rocks from CCSD- MH, which performs this algorithm from formation to formation (536 formations), not from point to point (333,26 points)(Konate et al., 2015), not only improving the accuracy, but also spending less computing time.

# 5.Conclusion

In this study, we have presented the application of the synergetic wavelet transform and modified K- means clustering techniques to formation interfaces detection and lithology classification using Chinese Continental Scientific Drilling Main Hole drilling data.

On the one hand, different wavelet functions in different well logs are performed to detect formation interfaces. Meanwhile, the Haar wavelet and GR are determined to be the optimum wavelet function and well log, as well as the optimum scales range from 8 to  $15\mathrm{m}$  . The results obtained are compared and corroborated by stratigraphic column.

On the other hand, a modified K- means clustering algorithm is employed to make a classification of stratigraphics, which are demarcated by the performance of wavelet transform. The results obtained not only improve the accuracy rate for the classification of metamorphic rocks in CCSD- MH, but also spend less computing time.

The proposed method has been succeed in both making a classification of metamorphic rocks and understanding the inhomogeneity of crusty in the whole Chinese Continental Scientific Drilling Main Hole.

# Acknowledgment

The work was supported by the National Natural Science Foundation of China Project Fund no. 41074086. Research on model experiments, forward and inversion of induced polarization in ground- well mode.2011.01- 2013.12.

# References

ReferencesArchie, G.E., 1942. The electrical resistivity log as an aid in determining some reservoir characteristics. Trans. Am. Inst. Mech. Eng. 146, 54- 62. Arabjamaloei, R., Edalatkha, S., Jamshidi, E., et al., 2011. Exact lithologic boundary detection based on wavelet transform analysis and real- time investigation of facies discontinuities using drilling data. Pet. Sci. Technol. 29, 569- 578. Bolton, E.W., Maasch, K.A., Lilly, J.M., 1995. A wavelet analysis of plio- pleistocene climate indicators: a new view of periodicity evolution. Geophys. Res. Lett. 22 (20), 27- 53. Briqueu, L., Zaourar, N., Lauer- Leredde, C., Hamoudi, M., 2010. Wavelet based multiscale analysis of geophysical downhole measurements: application to a clayey silicastic sequence. J. Petrol. Sci. Eng. 71 (3- 4), 112- 120. Chen, E.R., 1986. The Log Analysis Handbook. Quantitative Log Analysis Methods. Vol.1. PennWell Publishing, Tulsa OK. Chang, H., Kopaska- Merkel, D.C., Chen, H.C., Durrans, S., 2000. Lithofacies identification usingmultiple adaptive resonance theory neural networks and group decision expert system. Comput. Geosci. 26, 591- 601. Chandrasekhar, E., Eswara- Rao, V., 2012. Wavelet analysis of geophysical well- log data of Bombay Offshore Basin, India. Math. Geosci. 44 (8), 901- 928. Dewan, J.T., 1983. Essentials of Modern Open- Hole Log Interpretation. Penn Well Publishing, Tulsa, OK. Daubeunies, I., 1988. Orthonormal bases of compactly supported wavelets. Commen. Pure Appl. Math. 41 (7), 909- 996. Goupillaud, R.A., Grossmann, A., Morlet, J., 1985. Cycle- octave and related transform in seismic signal analysis. Geoexploration 23, 85- 102. Guyodo, Y., Gaillot, P., Channell, J.E.T., 2000. Wavelet analysis of relative geomagnetic paleointensity at ODP Site 983. Earth Planet. Sci. Lett. 184, 109- 123. Konate, A.A., Pan, H.P., Fang, S.N., 2015. Capability of self- organizing map neural network in geophysical log data classification: case study from the CCSD- MH. J. Appl. Geophys. 18, 37- 46. Lanning, E.N., Jonson, D.J., 1983. Automatic identification of rock boundaries: an application of the Walsh transform to geophysical well- log analysis. Geophysics 48, 197- 205. Liu, F.L., Xu, Z.Q., Xue, H.M., 2004. Tracing the protolith, UHP metamorphism, and exhumation ages of orthogneiss from the SW Sulu terrane (eastern China): SHRIMP U- Pb dating of mineral inclusion- bearing zircons. Lithos 78 (4), 411- 429. Liu, Y.S., Zhang, Z.M., Lee, C.T., Gao, S., 2005. Decreased high- Ti from low- Nb (Zr) of eclogites from the CCSD: implications for magnetite fractional crystallization in basal chamber. Acta Pet. Sin. 21 (2), 339- 346. Misits, M., Misiti, Y., Oppenheim, G., Poggi, J.M., 2000. Wavelet Toolbox for Use with MATLAB User's Guide. The Math Works Inc., Natick, MA, USA. Maiti, Tiwari, R.K., 2005. Automatic detection of lithologic boundaries using the Walsh transform. A case study from the KTB borehole. Comput. Geosci. 31, 948- 955. Niu, Y.X., Pan, H.P., Wang, W.X., 2008. The Metamorphic Well Logging Technology of Chinese Continent Scientific Drilling Project One Well. Science Press, Beijing, China. Pan, Q., Zhang, L., Meng, J.L., 2005. The Wavelet Analysis Method and its Application. Tsinghua University Press, Beijing, China. Pan, S.Y., Hsieh, B.Z., Lu, M.T., Lin, Z.S., 2008. Identification of stratigraphic formation interfaces using wavelet and Fourier transforms. Comput. Geosci. 34, 77- 92. Strang, G., Nguyen, T., 1995. Wavelets and Filter Banks. Wellesley- Cambridge Press, Prentice Hall, Aberdeen, UK. Teresa, Perez- Munoz, Jorge, Velasco- Hernandez, Eliseo, Hernandez Martinez, 2013. Wavelet transform analysis for lithological characteristics identification in siliciclastic oil fields. J. Appl. Geophys. 98, 298- 308. Weedon, G.P., 2003. Time- Series Analysis and Cyclostratigraphy. Cambridge University Press, Aberdeen, UK. Wang, X.X., Zattin, M., Li, J.J., Song, C.H., et al., 2013. Cenozoic tectonic uplift history of western qinling: evidence from sedimentary and fission- track data. J Earth Sci. 24 (4), 491- 505.

Xu, Z.Q., 2004. The scientific goals and investigation program of the Chinese continent scientific drilling project. Acta Pet. Sin. 20 (2), 1–8.  Ya, K., Yao, Y.P., Katayama, I., Cong, B.L., Wang, Q.C., Maruyama, S., 2000. Large areal extent of ultrahigh- pressure metamorphism in the Sulu ultrahigh- pressure terrane of East China: new implications from coesite and omphacite inclusions in zircon of granitic gneiss. Lithos 52 (1–4), 157–164.  Yue, W.Z., Tao, G., Liu, Z.W., 2004. Identifying reservoir fluids by wavelet transform of well logs. In: Proceedings of the SPE Asia Pacific Oil and Gas Conference and Exhibition, Perth, Australia (SPE 88559).  Yu, F.Q., 2013. Ten Lectures and Practical Wavelet Analysis. Xi'an University of Electronic Science and Technology Press, Xi'an, China.  Yang, H.J., Pan, H.P., Luo, M., 2015. The classification in metamorphic rocks using modified fuzzy cluster analysis from geophysical log data: evidence from

Chinese continental scientific drilling main hole. J. Pet. Explor. Prod. Technol. http://dx.doi.org/10.1007/s13202- 015- 0171- 0  Zhang, Z.M., Xu, Z.Q., Xu, H.F., 2000. Petrology of ultrahigh- pressure eclogite from the ZK703 drillhole in the Donghai, eastern China. Lithos 52 (1), 35–50.  Zhang, Z.M., Xu, Z.Q., Liu, F.L., 2004. The eclogite rocks study of the Chinese continent scientific drilling main hole. Acta Pet. Sin. 20 (1), 27–42.  Zhang, Z.M., Xu, Z.Q., Xu, H.F., 2003. Petrology of the non- mafic UHP metamorphic rocks from a drillhole in the Southern Sulu orogenic belt, eastern- central China. Acta Geol. Sin. 77 (2), 173–186.  Zhang, Z.M., Xiao, Y.L., Hoefs, Jochen, Liou, J.G., Simon, K., 2006. Ultrahigh pressure metamorphic rocks from the Chinese continental scientific drilling project: I. Petrology and geochemistry of the main hole (0–2, 050 m). Contrib. Mineral. Petrol. 152 (4), 421–441.