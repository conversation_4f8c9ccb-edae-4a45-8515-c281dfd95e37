# ReFormer: Lithology Identification via the Improved Transformer Model with Well Logging  

Data  

Youzhuang Sun, <PERSON><PERSON>, <PERSON><PERSON>tract—Predicting lithology with high accuracy is crucial for effective oil and gas extraction, influencing operational strategies significantly. This study introduces the Recurrent Transformer (ReFormer) model, an innovative blend of Transformer frameworks and recurrent dynamics, engineered for superior processing of well logging data. It integrates a unique Recurrent Scale-wise Attention (RSA) feature, designed specifically for well logging applications. This feature efficiently harnesses multi-level data details, enhancing the model's contextual analysis capabilities. It relies on crucial well log curves to link lithological features with logging measurements, and comparative assessments reveal its superior performance and adaptability in varying geological settings. This model marks a substantial leap in applying machine learning to lithology prediction within well logging, providing a robust tool for advancing resource identification and extraction, illustrating the transformative impact of sophisticated machine learning in earth science studies.  

Index Terms—Lithology prediction, Logging parameters, Recurrent Transformer, Machine learning  

# I. INTRODUCTION  

i the oil and gas industry, impacting how resources are allocated, extracted, and recovered. Lithology prediction, a critical aspect of Earth sciences, has been explored by researchers in various studies. [1]. Traditional methods, which rely heavily on manual interpretation, are hindered by their inherent subjectivity and the extensive time they require. The introduction of machine learning offers a revolutionary solution by enhancing objectivity, speed, and efficiency in processing large-scale data. Machine learning models [2] are adept at discerning complex patterns, providing geologists with more comprehensive and accurate tools for prediction. This technological integration signifies a major shift, significantly enhancing strategies for resource exploration and development.  

Pham [3], Chen [4], Lin [5], Wang [6], Zeng [7], and Wang [8] have each explored advanced neural network architectures, including ConvLSTM, ensemble LSTM, LSTM, CNN-LSTM hybrid, and GRU with self-attention mechanism. These studies have provided valuable insights into combining different neural network components to enhance prediction accuracy, manage uncertainty, and improve efficiency in well logging processing, thus emphasizing the importance of focusing on sequence models when dealing with well logging data. Meanwhile, the studies by Wang [6], Zeng [7], and Yang [9] have focused on spatio-temporal analysis and handling of temporal dependencies in well logging data, influencing the factors considered in our method for time series modeling. Wang's spatio-temporal network [6] and Yang's  

RPTransformer [9] demonstrate the importance of capturing sequential relationships in predicting reservoir properties from well logging data. Lin [10] proposed the MWLT model, a Transformer-based model for reconstructing well logs, which showed significant improvement compared to traditional methods, thus prompting our consideration of Transformer architecture in our method, particularly regarding their effectiveness in capturing long-distance dependencies in capturing sequence data from different regions.  

While there have been strides in predicting lithology from well logging data, traditional models encounter difficulties with sequential data types. Techniques like Random Forest and SVMs perform inadequately with this form of data, and RNNs often falter with extended data sequences. XGBoost, while optimal for structured data, falls short when dealing with the sequential nature of well logging data. The newly devised Recurrent Transformer model overcomes these limitations by merging Transformer architecture with a recurrent framework, adept at interpreting dynamic changes. It features the novel Recurrent Scale-wise Attention (RSA) mechanism that adeptly captures information across multiple scales, concentrating on both immediate and extensive contextual details. Through recursive operations, the RSA enhances the model's ability to track temporal changes, positioning the Recurrent Transformer as a refined tool for lithology prediction in well logging, which significantly improves the precision of resource identification and bolsters exploration efforts.  

# II. METHODOLOGY  

# A. Recurrent Transformer  

The advanced Recurrent Transformer model integrates a recurrent configuration to enhance lithology prediction capabilities in well logging applications. This model includes recurrent elements, commonly seen in RNNs, LSTMs, and GRUs[11], to meticulously track temporal variations in sequential data. Unlike typical Transformers, this model excels in processing the chronological data prevalent in well logs, delivering a nuanced comprehension of dynamic shifts, thereby boosting the accuracy of lithology predictions. Its recurrent setup is adept at assimilating local contextual nuances, heightening sensitivity to geological variations that influence lithological outcomes. As depicted in Figure 1, the model is structured with three recurrent modules alongside a refinement unit, combining encoder, Recurrent Transformer block, and decoder configurations to capture both broad and localized temporal influences, refining the output to produce precise lithology forecasts.  

![](images/2992d85856dd79baf0c9a0c799027eb5465ac6a1251f9bd4a6fcd51f9f741752.jpg)  
Fig. 1.  Recurrent Transformer structure diagram  

Referencing Figure 2, each component within our model consists of three essential elements: an encoder designated as $f ^ { E n c }$ , a Recurrent Transformer block termed $f ^ { R T B }$ , and a decoder known as $f ^ { D e c }$ . We detail each component, denoted as fi , where i  indicates the particular unit in focus, performing through the $t ^ { \mathrm { t h } }$ iteration as follows:  

$$
f _ { i } ^ { { R U } } \left( h _ { ( t ) } ^ { i } , c _ { ( t ) } ^ { i } \right) = f ^ { { R e c } } \left( f ^ { { R T B } } \left( h _ { ( t ) } ^ { i } , c _ { ( t ) } ^ { i } \right) \oplus f ^ { { E n c } } \left( h _ { ( t - 1 ) } ^ { i } \right) \right)
$$  

where $\oplus$ signifies element-wise summation. The Recurrent Transformer block f RTB in each unit tailors the extraction of multi-scale features from original well logging data, manipulating the convolutional stride within the encoder f Enc . Subsequently, the decoder $f ^ { D e c }$ in every unit utilizes inverse convolutional layers, known as transposed convolution, to revert the data to its original scale. This type of convolution, distinguished by the expansion of output through strategic padding, contrasts with standard methods by augmenting feature dimensions while preserving original input consistency via shared weights. A layer ensuring data consistency is embedded at the end of the network to maintain alignment with the learned patterns.  

$$
h _ { ( t ) } ^ { i } = D e c o d e r \Big ( f ^ { R U } \left( h _ { ( t - 1 ) } ^ { i } , c _ { ( t ) } ^ { i } , x ^ { t } \right) \Big )
$$  

The Decoder layer, responsible for deriving outputs from the attention-driven interactions between temporal and contextual data, formulates lithology predictions. Incorporating a network of fully connected layers allows for non-linear transformations essential for modeling complex geological patterns. Considering prior lithology predictions and relevant factors is crucial in this phase. The sequence of equations below presents the decoder and recurrent units' operational framework, facilitating data processing at neural network's varied levels. Equations (3), (4), and (5) describe the recurrent units' (RU) formulas, operating sequentially to process data through consecutive time intervals. Equation (6) illustrates the decoder's function, merging the outputs from the recurrent units to generate the final predictive output.  

$$
\begin{array} { c } { { h _ { ( t ) } ^ { 1 } = R U _ { 1 } \big ( h _ { ( t - 1 ) } ^ { 0 } , h _ { ( t ) } ^ { c } , x ^ { t } \big ) } } \\ { { { } } } \\ { { h _ { ( t ) } ^ { 2 } = R U _ { 2 } \big ( h _ { ( t - 1 ) } ^ { 1 } , h _ { ( t ) } ^ { c } , x ^ { t } \big ) } } \\ { { { } } } \\ { { h _ { ( t ) } ^ { 3 } = R U _ { 3 } \big ( h _ { ( t - 1 ) } ^ { 2 } , h _ { ( t ) } ^ { c } , x ^ { t } \big ) } } \end{array}
$$  

$$
h _ { ( t + 1 ) } = D e c o d e r \Big ( R M \big ( h _ { ( t ) } ^ { 1 } \otimes h _ { ( t ) } ^ { 2 } \otimes h _ { ( t ) } ^ { 3 } , x ^ { t } \big ) \Big )
$$  

Here, $\otimes$ denotes the operation of channel-wise concatenation, efficiently combining feature maps of varying channel counts while retaining dimensions. "RM" stands for Recurrent Memory, a key feature integrating outputs from the recurrent units for both current and subsequent iterations, forming a continuous loop of data refinement.  

![](images/4a7b190aa543ca12d36bd57b337f56f38cea7acca99861a52d585e41117838ac.jpg)  
Fig. 2. A Recurrent unit structure diagram  

# B. Recurrent Transformer block  

The Recurrent Transformer block (RTB) utilizes Recurrent Pyramid Transformer Layers (RPTL) along with a central feature, the Recurrent Scale-wise Attention (RSA) mechanism. RSA segments the input sequence and applies distinct attention mechanisms to different parts before combining them for the final output, thereby enhancing the model's ability to interpret contexts at various scales. The adaptability of RSA to various input types is one of its key advantages. Unlike conventional multi-head self-attention, RSA simultaneously addresses both local and global information, which is vital for accurate predictions in well logging lithology. Moreover, RSA's iterative process enhances the model's capability to retain contextual details, increasing its sensitivity to specific lithological sequence characteristics. RPTL improves efficiency through a local attention mechanism that operates with a shifting window approach, providing enhanced performance across multiple tasks. For well logging data, which is often captured as a function of depth or time and shows temporal fluctuations, this local attention mechanism is pivotal. It effectively utilizes the relationships between neighboring data points to aid in more precise predictions and deeper data analysis, essential for decoding well logging datasets that exhibit closely linked local structures. Regarding the input hidden state h( )  RH W C , our Recurrent Partitioned Transformer Layer (RPTL) initially utilizes $M ^ { * } M$ nonoverlapping local windows to divide $\boldsymbol { h } ^ { ( t ) }$ into segments, resulting in a feature size of $\frac { H ^ { \prime } \times W ^ { \prime } } { M ^ { 2 } } { \times } M ^ { 2 } \times C ^ { \prime }$ . Here, $M$ $H ^ { \prime } { \times } W ^ { \prime }$ represents the dimension of each local window, and $\overline { { { \cal M } ^ { 2 } } }$ indicates the total count of these windows. Following this, our newly introduced Recurrent Self-Attention (RSA) mechanism independently computes self-attention for each window. Within this context, let's denote I  RM2C a s a feature of a local window. In RPTL, the query $( \boldsymbol { Q } )$ , key $( K )$ , and value $( V )$ matrices are structured as follows:  

$$
Q = I P _ { \varrho } , K = I P _ { \kappa } , V = I P _ { \nu }
$$  

where $P _ { \mathcal { Q } } , P _ { K }$ , and $P _ { \scriptscriptstyle { V } }$ are the corresponding projection matrices in attention scale heads. In the study of attention mechanisms in deep learning frameworks, a fundamental operation is the application of linear transformations on the input via projection matrices. These matrices play a critical role in transposing the input into various representative spaces, each dedicated to capturing different facets of the input data. This is particularly apparent in the multi-head attention design, where several attention mechanisms operate concurrently, each with its specialized projection matrices. The diverse heads enable the model to simultaneously process and learn varied interrelations within unique representational spaces. After being processed by these heads, the results are typically combined—usually through concatenation—and further refined with an additional projection matrix.  

$$
A t t e n t i o n \Big ( \mathcal { Q } , K , V , c ^ { ( t ) } \Big ) = S o f t M a x \Big ( c ^ { ( t + 1 ) } \Big ) V
$$  

In time series modeling, recursive updates are crucial for generating accurate forecasts by maintaining continuity with previous data points. This ability to update recursively allows the model to continuously adjust to changing temporal patterns. The model uncovers deep feature correlations through interactions among features that dynamically evolve through recursive updates, adapting to the intricate nature of time series data. At each time step $t$ , the deep feature correlation c is updated recursively as follows: $c ^ { ( t ) }$  

$$
c ^ { ( t + l ) } = \lambda \Biggl ( \frac { Q K ^ { T } } { \sqrt { d } } + B \Biggr ) + ( l - \lambda ) c ^ { ( t ) }
$$  

Within deep learning, the 'embedding dimension' $( d )$ describes the scale of compressed input representations that assist the model in grasping complex structures. During the training phase, learnable relative position biases $( B )$ are pivotal for mapping interrelations between sequence positions and local patterns.  represents a learnable coefficient that modulates deep feature correlations dynamically during training to optimize contributions. The dynamic interplay of these features underscores the reciprocal influence of learned attributes. By fine-tuning biases and weight adjustments, the model customizes its approach to deep feature correlation. The Recurrent Pyramid Transformer Layer (RPTL) incorporates these dimensions, updates, biases, and weight adjustments, significantly enhancing its proficiency in applications such as well logging lithology predictions.  

$$
\begin{array} { c } { \dot { \mathbf { \sigma } } ^ { \mathrm { ~ } } } \\ { \dot { I } = R S A ( L N ( I ) ) + I } \\ { \dot { I } = M L P ( L N ( I ) ) + I } \end{array}
$$  

In sophisticated neural network models like the Recurrent Transformer, essential elements include Layer Normalization [12] (LN) and Multi-Layer Perceptron [13] (MLP). LN is vital for normalizing the inputs of each layer during training, enhancing convergence speed and ensuring generalization across varied datasets. It calculates the mean and standard deviation for each input to a layer, promoting uniform training conditions and improving stability. The MLP, composed of fully connected layers, enables nonlinear transformations, empowering the network to decode complex data patterns and relationships.  

In the architectural diagram of the Recurrent Transformer block (as shown in Figure 3), the procedure for managing sequential data is detailed and complex. Represented by the symbol $h ( t )$ , the input feature map at each time step $t$ is subjected to a process called block embedding, which divides the feature maps into segments for enhanced processing within a multi-dimensional framework. To maintain consistency across features, layer normalization is applied. The Recurrent Scale-wise Attention (RSA) mechanism leverages the preceding sequence, denoted as $c ( t )$ , to precisely map and refine the input. Subsequently, the altered input is processed through a Multilayer Perceptron (MLP) that includes two linear layers followed by a nonlinear activation function. The  

Patch Unembedding process then reverses the initial block embedding, and the resulting encoded output, $y ( t )$ , is merged with additional features at that time step to produce the next state, $h ( t { + } I )$ . This sophisticated design facilitates the capture of dynamic interrelationships, significantly improving precision in applications such as well logging lithology prediction.  

![](images/e3b29c93ae06f71d5fe002a209e4b7fd113d0d7aa75d1e41b905fb42e269ba36.jpg)  
Fig. 3.  Recurrent Transformer block structure diagram  

# III. DATA ANALYSIS AND EXPERIMENTAL DESIGN  

Our investigation focuses on well logging data derived from the expansive Tarim Oilfield, located in the vast Tarim Basin ( $8 2 ^ { \circ }$ to $9 0 ^ { \circ } \mathrm { E }$ , $3 8 ^ { \circ }$ to $4 4 ^ { \circ } \mathrm { N }$ ), a pivotal area for oil and gas exploration within China. Employing specialized logging tools, we measured parameters such as Photoelectric Index (PE), Gamma-Ray (GR), Acoustic Time Difference (AC), Compensated Neutron Log (CNL), and Density (DEN) during and post drilling activities. These measurements provide valuable insights into the geophysical and geochemical properties of subsurface structures. Our study aims to differentiate between mudstone and sandstone lithologies by correlating well logging data with lithological indicators obtained from drilling core samples. Figures 4 and 5 illustrate these well logging parameters and their correlations with mudstone and sandstone lithologies.  

![](images/4d43b724396430970b498507595a76a40735f735783169ca8266322554204635.jpg)  
Fig. 4. Well logging parameters corresponding to lithology for the first dataset (test set)  

![](images/d4d540441d5d614b5d9bb508805d467f31b0baa6e3d368ef3d91aab93d91527f.jpg)  
Fig. 5. Well logging parameters corresponding to lithology for the second dataset (test set)  

The initial phase of data processing involves addressing gaps in oilfield data parameters. We employ interpolation techniques to estimate and fill these gaps, utilizing the mean of known values to complete the missing data in our dataset. Additionally, outlier management is performed using the ZScore method [14], which quantifies how much a data point deviates from the average, calculated as:  

$$
Z = { \frac { X - \mu } { \sigma } }
$$  

Where, $X$ is data point, $\mu$ is the standard deviation, and we set the data points of $\sigma > 3$ as the outlier. We use the mean of the known data instead of the value of the outlier. Figures 6 and 7 show boxplots of the preprocessed data.  

![](images/777509a83d0bfdc655688719aafafa2cc9658c37e2200afda23004b88962d539.jpg)  

![](images/9537263fbd21e31037661019d447fe3d40859544670a100ab2da14a741f182e0.jpg)  
Fig. 6. Box plot of the preprocessed data for the first test set  

Data preprocessing includes addressing missing values through interpolation followed by normalization using the Min-Max standardization technique [15], scaling each parameter to a range between [0, 1]. This adjustment ensures that all features are equally weighted, which promotes model stability and accelerates convergence during the training phase.  

$$
X _ { \substack { n o r m a l i z e d } } = \frac { X - X _ { m i n } } { X _ { m a x } - X _ { m i n } }
$$  

Where, $X$ is the original eigenvalue, $X _ { m i n }$ and $X _ { m a x }$ are the minimum and maximum values of the feature, respectively.  

In this research, we selected data from six wells within the study area. For the initial dataset, we chose 7500 data sets We divided this dataset into a training set and a test set in an 8:2 ratio, allocating 6000 sets for training and 1500 for testing. We applied the same division approach to the second database, distinctly different dataset, where another 10,000 sets of data were similarly divided.  

# IV. ANALYSIS OF RESULTS  

Figures 8 and 9 display the superior performance of our Recurrent Transformer model in lithology prediction tasks. In Figure 8, the model demonstrates a reduced error rate compared to other models, highlighting its excellent performance in identification tasks. Detailed further in Figure 9, our model surpasses others in key performance metrics including True Positive Rate (TPR), True Negative Rate (TNR), Positive Predictive Value (PPV), Negative Predictive Value (NPV), and Accuracy. It notably excels in TPR and TNR, indicating reliable identification of positive and negative instances. The model’s high PPV, NPV, and overall accuracy confirm its efficacy across the identification task. The Recurrent Transformer’s recursive structure and Recurrent Scale-wise Attention enhance its ability to capture long-range dependencies and improve correlations between data points in well logging, contrasting with traditional Transformer and time-series models which may falter with recursive relationships and extended dependencies.  

![](images/ef5993aa41d370e1b6abbd664cb87f2c07bf891323decdb14de04eb9c5658a66.jpg)  
Fig. 8. True lithology and predicted lithology by the machine learning models in the first set of test data  

![](images/abcc76918788f202efa8d7d9b11fe231fdb6367001442236e39fe7c4722172eb.jpg)  
Fig. 7. Box plot of the preprocessed data for the second test set   
Fig. 9. Confusion matrix for nine machine learning models (the first dataset) Figures 10 and 11 present lithology identification results for the second dataset, with the accuracy ranking among models as follows: Recurrent Transformer $>$ Transformer $> \mathrm { G R U } >$ $\mathrm { L S T M } > \mathrm { R F } > \mathrm { S V M }$ . The recursive structure and Recurrent Scale-wise Attention of the Recurrent Transformer enhance its ability to capture long-distance dependencies and spatiotemporal relationships in well logging data, thus improving accuracy in complex datasets. Traditional Transformers perform well in managing dependencies through their self-attention mechanisms, exhibiting flexibility in handling different data interactions. Sequential models (GRU,  

LSTM) effectively model temporal dynamics, capturing both long and short-term dependencies, although they might encounter issues with gradient instability in prolonged sequences. Ensemble models (RF) perform best with nontemporal data but may struggle with temporal relationships in time-series tasks such as lithology prediction. SVM, being a linear classifier, has difficulty modeling nonlinear relationships and is less effective in handling complex well logging data, particularly in capturing temporal dependencies within sequences.  

![](images/d4f2a43b2c452960aa0aa8051325344a8ac574cb80212edb531da4cd2ff1d444.jpg)  
Fig. 10. True lithology and predicted lithology by the machine learning models in the second set of test data  

![](images/ed3813c10a85d6be251ee736a03755a0f5f570dcdd6f5b2f8934d3e8da78febd.jpg)  
Fig. 11. Confusion matrix for nine machine learning models (the second dataset)  

# V. CONCLUSION  

Our evaluation of the Recurrent Transformer model’s effectiveness in lithology prediction against a variety of machine learning and deep learning models reveals significant findings. The integration of a recurrent framework within the Transformer markedly enhances its performance, especially in capturing temporal patterns in well log data and refining lithology prediction accuracy. The Recurrent Scale-wise Attention mechanism within the model outperforms traditional attention mechanisms by effectively discerning multi-scale complexities within input sequences and increasing sensitivity to sequential attributes. While the results are promising, potential enhancements to the model’s architecture and parameters are suggested for broader application. Incorporating more profound geological knowledge could further refine the model’s ability to interpret geological features. Collectively, our research introduces a robust approach to lithology prediction in well logs, advancing deep learning applications in the earth sciences and providing valuable insights for future research endeavors.  

# REFERENCES  

[1] V. A. Dev and M. R. Eden, "Evaluating the boosting approach to machine learning for formation lithology classification," in Computer aided chemical engineering, Elsevier, 2018, vol. 44, pp. 1465-1470.   
[2] G. Zhang, Z. Wang, and Y. Chen, "Deep learning for seismic lithology prediction," Geophysical Journal International, vol. 215, no. 2, pp. 1368-1387, 2018.   
[3] N. Pham, X. Wu, and E. Zabihi Naeini, "Missing well log prediction using convolutional long short-term memory network," Geophysics, vol. 85, no. 4, pp. 159-171, 2020.   
[4] Y. Chen and D. Zhang, "Well log generation via ensemble long short‐ term memory (EnLSTM) network," Geophysical Research Letters, vol. 47, no. 23, p. 685-696, 2020.   
[5] J. Lin, H. Li, N. Liu, et al., "Automatic lithology identification by applying LSTM to logging data: A case study in $\mathrm { \Delta } X$ tight rock reservoirs," IEEE Geoscience and Remote Sensing Letters, vol. 18, no. 8, pp. 1361-1365, 2020.   
[6] J. Wang, J. Cao, J. You, et al., "A method for well log data generation based on a spatio-temporal neural network," Journal of Geophysics and Engineering, vol. 18, no. 5, pp. 700-711, 2021.   
[7] L. Zeng, W. Ren, L. Shan, et al., "Well logging prediction and uncertainty analysis based on recurrent neural network with attention mechanism and Bayesian theory," Journal of Petroleum Science and Engineering, vol. 208, p. 1458, 2022.   
[8] J. Wang, J. Cao, J. Fu, et al., "Missing well logs prediction using deep learning integrated neural network with the self-attention mechanism," Energy, vol. 261, p. 125270, 2022.   
[9] L. Yang, S. Fomel, S. Wang, et al., "Porosity and permeability prediction using a transformer and periodic long short-term network," Geophysics, vol. 88, no. 1, pp. 293-308, 2023.   
[10] L. Lin, H. Wei, T. Wu, et al., "Missing well-log reconstruction using a sequence self-attention deep-learning framework," Geophysics, vol. 88, no. 6, pp. 391-410, 2023.   
[11] A. Shewalkar, D. Nyavanandi, and S. A. Ludwig, "Performance evaluation of deep neural networks applied to speech recognition: RNN, LSTM and GRU," Journal of Artificial Intelligence and Soft Computing Research, vol. 9, no. 4, pp. 235-245, 2019.   
[12] Z. Yin, B. Wan, F. Yuan, et al., "A deep normalization and convolutional neural network for image smoke detection," IEEE Access, vol. 5, pp. 18429-18438, 2017.   
[13] J. Gaudart, B. Giusiano, and L. Huiart, "Comparison of the performance of multi-layer perceptron and linear regression for epidemiological data," Computational statistics & data analysis, vol. 44, no. 4, pp. 547-570, 2004.   
[14] A. Martinez-Millana, J. M. Hulst, M. Boon, et al., "Optimisation of children $z$ -score calculation based on new statistical techniques," PloS one, vol. 13, no. 12, p. e0208362, 2018.   
[15] M. Mazziotta and A. Pareto, "Normalization methods for spatio ‐ temporal analysis of environmental performance: Revisiting the Min– Max method," Environmetrics, vol. 33, no. 5, p. e2730, 2022.  