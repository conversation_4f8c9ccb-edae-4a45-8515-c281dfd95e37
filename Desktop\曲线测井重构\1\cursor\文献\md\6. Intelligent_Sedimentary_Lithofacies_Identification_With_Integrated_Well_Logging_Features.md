# Intelligent Sedimentary Lithofacies Identification With Integrated Well Logging Features  

<PERSON><PERSON>, <PERSON><PERSON> , <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>  

Abstract— Lithology identification is the research basis in oil and gas reservoir exploration and is critical for the formation characterization and reservoir development. Traditional lithofacies identification methods rely on the knowledge and experience of geologists and are usually done manually. With the development of deep learning technology and its application in the field of geophysics, lithofacies identification based on deep-learning approach has attracted great attention in recent years. Well logging data have obvious sequence characteristics. Therefore, we propose to use a bidirectional long and short-term memory (BiLSTM) neural network to learn long-term information for more effective lithology facies classification. In addition, we also perform correlation analysis on the input well logging curves and conduct median filter at different scales according to the correlation degree to extract the geological features within data itself and discard the interference of noise. The raw data-based lithofacies identification can reflect the noise resistance of the neural network model to some extent, while the filtered data are more beneficial for the model to extract the geological features correlated with lithofacies and provide accurate classification results. We validate our proposed framework by applying it to a case study from the Council Grove gas reservoir located in Kansas. Furthermore, we compare the effect of input data and network model on the identification results. The experimental results show that the proposed lithofacies identification method has higher classification accuracy.  

Index Terms— Bidirectional long short-term memory (BiLSTM) network, correlation analysis, geological features, lithology identification, sequence data.  

# I. INTRODUCTION  

L IreTsHerOvFoiArCeIvEalS itdieon afincda icohn ias ea fzuatnidoan,mewnhtiacl praertfl fcotsr the depositional transportation and dynamic conditions over time [1]. Traditional lithology analysis usually relies on the experience and knowledge of geologists. Based on the previous research achievements and the regional sedimentary  

Manuscript received 20 December 2022; revised 11 April 2023; accepted 21 December 2023. Date of publication 26 December 2023; date of current version 5 January 2024. This work was supported in part by the Fundamental Research Project of China National Petroleum Corporation (CNPC) Geophysical Key Laboratory under Grant 2022DQ0604-4; and in part by the Strategic Cooperation Technology Projects of China National Petroleum Corporation and China University of Petroleum, Beijing, under Grant ZLZX2020-03. (Corresponding author: Naxia Yang.)  

Shuwen Guo and Chunxiang Guo are with the Research Institute of Exploration and Development, Dagang Oilfield, PetroChina, Tianjin 300280, China (e-mail: guoshwen $@$ petrochina.com.cn; guochxiang $@$ petrochina.com.cn).  

Naxia Yang, Dongfeng Zhao, and Guofa Li are with the State Key Laboratory of Petroleum Resources and Prospecting, CNPC Key Laboratory of Geophysical Exploration, China University of Petroleum, Beijing 102249, China (e-mail: <EMAIL>; zdf_geosciences $@$ 163.com; lgfseismic $@ 1 2 6 . \mathrm { c o m } _ { \cdot }$ ).  

Hao Li is with the Yangtze Delta Region Institute (Huzhou), University of Electronic Science and Technology of China, Huzhou 313001, China (e-mail: lihao $@$ csj.uestc.edu.cn).  

Digital Object Identifier 10.1109/LGRS.2023.3347565 background, the features of core and logging measurements are integrated to delineate the sedimentary lithofacies. This manual identification process involves a large amount of data analysis and comparison. Therefore, it is very tedious and complex that leads to a certain degree of uncertainty and possible artifacts [2], [3].  

With the improvement and development of computer technology, lithofacies identification based on deep learning approach has become a research hotspot [4], [5], which can be divided into two main categories, one is core identification based on image processing [6], [7], [8], and the other is logging recognition based on data analysis [9], [10], [11], [12]. However, most lithofacies identification conducted rely on well logging data because core observation is very expensive and time-consuming. Theoretically, the different logging measurements provide the physical and chemical properties of the subsurface formations, respectively. Meanwhile, similar logging response of different lithology facies results in uncertainties and ambiguities in the classification. Therefore, the optimization, evaluation, and analysis of well log curves are important aspects for lithofacies identification [13]. In addition, various machine learning methods have also shown different application effects. Earlier methods such as support vector machines (SVMs), decision trees, random forests, principal component analysis (PCA), Bayesian optimization, genetic algorithms, and Markov chain Monte Carlo have improved the effectiveness of lithology interpretation and reduced the influence of subjective factors [14], [15], [16], [17]. Lithofacies identification is carried out from the geological features reflected by the data itself. Subsequently, with the development of computing power and the promotion of big data, deep network architectures such as convolutional neural networks (CNNs), recurrent neural networks (RNNs), and auto-encoder networks have been employed for lithology recognition [18], [19]. Then, in order to alleviate the dependence of deep networks on labeled data, semi-supervised, and unsupervised waveform clustering techniques such as self-organized map (SOM), $k$ -means, and other technical tools have been already tried for lithology identification [20], [21], [22], [23], [24]. In addition, the introduction of technical approaches such as data augmentation also alleviates the impact of limited labeled data on the classification effect to certain extent [25]. Research progress has been achieved in lithology identification based on deep learning algorithms.  

Specifically, we propose a framework for lithology identification based on the bidirectional long short-term memory (BiLSTM) neural network, which integrated features of well logging measurements. As one of the variant networks of  

RNNs, the BiLSTM network consists of several units composed of two LSTM that process both forward and reverse input information, simultaneously [26], [27], [28], [29], [30], [31]. It is proven to have significant advantages in the processing of sequence data [32]. In addition to choosing a reasonable network architecture, we also analyze and process the well logging data. The logging curves are median filtered at different scales according to the correlation degree to extract the geological and lithological features reflected by the data. Meanwhile, the filtered data are trained along with the raw data to ensure that the network can establish the correct mapping relationship while with certain degree of noise assistance. We applied the proposed network model to the case study from the Council Grove gas reservoir located in Kansas. Experimental results show that our framework provides more accurate lithofacies identification results with higher resolution and is more effective for delineating thin-layer lithology.  

# II. METHODOLOGY AND DATA  

# A. BiLSTM Architecture  

The traditional RNNs have no memory function, and the information of the current hidden layer comes from the current input and the information of the previous hidden layer. Therefore, it cannot deal with the problem of gradient disappearance or explosion caused by long-term dependence. The network structure of LSTM is more complex than that of RNNs, which introduces the cell state and gating system to maintain and control information. The introduction of the cell state allows the network to handle long-term dependencies. Moreover, the LSTM adds filtering of past states, which can select which states are more influential to the present, rather than simply selecting the most recent states.  

Fig. 1 shows the schematic of the BiLSTM network configurations, which consists of one input layer, two hidden layers, and one output layer. The sequence information flows forward and backward simultaneously. In current time step, the hidden state $\mathbf { h } _ { t }$ is a concatenated matrix from two opposite directions as follows  

$$
\mathbf { h } _ { t } = \left[ \overrightarrow { \mathbf { h } _ { t } } , \overleftarrow { \mathbf { h } _ { t } } \right]
$$  

where $\overrightarrow { \mathbf { h } _ { t } }$ represents the forward hidden state while $\left\{ \mathbf { \widetilde { h } } _ { t } \right.$ indicates the backward hidden state. Such a network configuration allows the BiLSTM model to better capture bi-directional data dependencies and provide more accurate prediction results consequently.  

# B. Data Description and Related Work  

We use the well logging curves from nine wells of the Council Grove gas reservoir located in Kansas to validate the effectiveness of the proposed framework. Feature variables include five wireline log measurements and two geologic constraining variables that are derived from geologic knowledge. The specific relevant information is displayed in Table I.  

On the basis of determining the network architecture, we also perform some corresponding processing of the input data. Fig. 2 displays the well logging curves and lithology interpretation results in the study area. We perform the correlation analysis between different logging measurements and lithofacies  

![](images/44d8d0d9b295ed13f701210641206e7d50afeb2e77fb421fa68f9298a3c3070d.jpg)  
Fig. 1. Architectures of the BiLSTM neural network, which contains one input layer, two hidden layers, and one output layer for processing the forward and backward input information simultaneously.  

$$
r _ { x , y } = { \frac { \sum _ { i = 1 } ^ { n } \left( X _ { i } - { \bar { X } } \right) \left( Y _ { i } - { \bar { Y } } \right) } { \left( { \sqrt { \sum _ { i = 1 } ^ { n } \left( X _ { i } - { \bar { X } } \right) ^ { 2 } } } \right) \left( { \sqrt { \sum _ { i = 1 } ^ { n } \left( Y _ { i } - { \bar { Y } } \right) ^ { 2 } } } \right) } }
$$  

where $r$ is the Pearson correlation coefficient between two series data of $x$ and $y$ with values between $^ { - 1 }$ and 1. $X _ { i }$ and $Y _ { i }$ denote a sequence element while $\bar { X }$ and $\bar { Y }$ represent the mean of the two series, respectively. $n$ represents the number of series. Pearson correlation coefficient is a statistic used to reflect the degree of similarity between two variables. The correlation coefficients of GR, ILD_log10, DeltaPHI, PHIND, and PE with lithology are $- 0 . 3 4 , \ 0 . 3 9 , \ - 0 . 2 3 , \ - 0 . 3 6 .$ , and 0.70, respectively. Meanwhile, we also conduct the correlation analysis of each logging curve. The heat map shown in Fig. 3 illustrates the combined relationship between two discrete variables.  

Where the PE curve has the highest correlation with the lithofacies interpretation results. Therefore, we use the PE curve as the basis, the correlation coefficients of other logging curves with PE as a guide to perform median filtering at different scales according to the correlation coefficient, the curves with high correlation coefficients being filtered at small scales, and curves with low correlation coefficients being filtered at large scales. Consequently, we perform the median filtering with windows of 7, 9, 11, and 13 for PHIND, ILD_log10, GR, and DeltaPHI, respectively. After filtering, the logging curve removes the high-frequency noise and effectively retains the edge information. The correlation coefficients with the PE curve are improved to 0.59, 0.06, 0.40, −0.34, respectively. Fig. 4 shows the raw and the median filtered well logging curves, which is used as input for the BiLSTM network to establish the mapping function between logging measurements and lithofacies.  

# III. EXPERIMENTAL ANALYSIS  

Eight wells in the study area are used as training data, while the remaining one well is used as a blind well to verify the reliability and accuracy of the proposed identification framework. Fig. 5 shows the distribution characteristics of each lithology facies of the training dataset. The unbalanced distribution of the training lithology samples also has certain degree of influence on the classification results. For the stability of the network training process, we normalize the input data to 0–1, respectively. The batch size is determined as  

TABLE I DESCRIPTION OF WELL LOG MEASUREMENTS AND LITHOFACIES   


<html><body><table><tr><td>Lithology Facies</td><td>Description</td><td>Well logging</td><td>Description</td></tr><tr><td>SS</td><td>Nonmarine sandstone</td><td>GR</td><td>Gamma emission</td></tr><tr><td>CSiS</td><td>Nonmarine coarse siltstone</td><td>ILD_log10</td><td>Resistivity</td></tr><tr><td>FSiS</td><td>Normarine fine siltstone</td><td>PE</td><td>Photoeletric effect</td></tr><tr><td>SiSH</td><td>Marine silestone and shale</td><td>DeltaPHI</td><td>Porosity index</td></tr><tr><td>MS</td><td>Mudstone(limestone)</td><td>PNHIND</td><td>Average of neutron and density</td></tr><tr><td>WS</td><td>Wackestone(limestone)</td><td>GR</td><td>After Median Filter</td></tr><tr><td>D</td><td>Dolomite</td><td>ILD_log10</td><td>After Median Filter</td></tr><tr><td>PS</td><td>Packstone-grainstone(limestone)</td><td>DeltaPHI</td><td>After Median Filter</td></tr><tr><td>BS</td><td>Phylloid-algal bafflestone(limestone)</td><td>PNHIND</td><td>After Median Filter</td></tr></table></body></html>  

![](images/3389018c4fb0b15efa9aae3773bfbee2bc03b124c75f91cae3fe7ab463d4b385.jpg)  
Fig. 2. Well log measurements and corresponding lithofacies interpretation result.  

![](images/adbeb0623aef658b74c341ec13f4d933b09fb461e5512d1a45e9e3a1931f60a8.jpg)  
Fig. 3. Correlation analysis of logging measurement data.  

![](images/632c6f5c2db8ab0ba07d496bc39b38e06cdae4facc4bc11c0ee81e6252eddc47.jpg)  
Fig. 4. Input data for the BiLSTM neural network, which includes the raw logging data as well as the median filtered data.  

![](images/43d44d5510cd853bbb59c1760b27e8d10dedbc4591e0c7d4d4f326e0a01977db.jpg)  
Fig. 5. Lithofacies distribution characteristics of the training dataset.  

![](images/5a40fa0d01624242f0cddca94c9ff6b8527faf4cc948ae32e2f3afa1712c4b8d.jpg)  
Fig. 6. Learning curves for different network architectures and input logging measurements of (a) training error and (b) identification accuracy.  

128 while the time step is set to 64. The input data contain nine features. Furthermore, we use multicategory cross-entropy as the loss function to train the network model  

$$
L ( a , y ) = - \frac { 1 } { N } \sum _ { i = 0 } ^ { N - 1 } \sum _ { k = 0 } ^ { K - 1 } a _ { i , k } \ln y _ { i , k }
$$  

where $a$ represents the one-hot code corresponding to the label, $y$ denotes the predicted probability, $K$ represents the total sample categories and it is 9 in this case, $N$ means the number of samples, $a _ { i , k }$ indicates whether the $i$ th sample of input well logging data is a class $k$ lithology facies, 1 if it is, or 0 if it is not. $y _ { i , k }$ refers to the probability that the $i$ th sample is predicted to be the $k$ th label value.  

We choose the Adam optimization algorithm to determine the best network configurations and hyperparameters based on the loss and accuracy performance of the BiLSTM model. Ultimately, the network contains five BiLSTM layers, a fully connected layer, and a softmax probability layer. The results of the neural network output are normalized by the softmax function and output the probability of each lithofacies between 0 and 1. Each discriminate result is obtained over 300 epochs with learning rate as 0.0005 to provide consistent comparisons.  

![](images/6aca2f9d393874765f9af331355f9c781c196f0bef2e6a6f5c3404fb5419b90f.jpg)  
Fig. 7. Comparison of ground truth with predicted lithofacies. (a) Ground truth of the lithofacies. (b) Predicted lithofacies from the BiLSTM neural network with additional median-filtered data as input and (c) only the raw well logging measurements as input. (d) Predicted lithofacies from the LSTM neural network with raw and filtered data as input.  

To illustrate the effectiveness of our proposed lithofacies identification framework, we design two comparison experiments to compare the effects of network structures and input data on lithofacies identification results, respectively. One is the BiLSTM neural network model with only the raw logging data as input and the other is the LSTM structure with the raw and filtered data as input. Fig. 6 shows the loss and accuracy curves for each classification framework, where the BiLSTM configurations with the raw and median-filtered logging measurements as input have lower loss and higher accuracy. Furthermore, we apply the trained model to the validation well. Fig. 7 shows the lithology recognition results with different network architectures and input data. Among them, the accuracy of our proposed framework is $8 4 . 3 8 \%$ , which is higher than the identification framework with the same network architectures and the same inputs, whose accuracy is $78 . 2 3 \%$ and $7 4 . 6 4 \%$ , respectively.  

The recognition results in the red dashed box show that the BiLSTM and LSTM network architectures can identify lithology facies with high accuracy. Meanwhile, the identification results in the black dashed box show that our proposed framework provides recognition results that are closer to the interpretation conclusions. In addition, comparing Fig. 7(c) and (d), although the BiLSTM network structure with only raw data as input has a higher recognition accuracy, the introduction of median-filtered data improves the identification of thin interbedded lithology facies and better distinguishes lithofacies boundaries as shown in the black dashed box. Median filtering filters out high-frequency noise in the raw logging measurements while retains edge information well, which is beneficial to distinguishing different sedimentary lithology facies.  

# IV. DISCUSSION  

The advantages of the RNNs in sequence data processing are demonstrated in this experiment. Moreover, the gating system introduced by the LSTM model effectively solves the gradient problem caused by long-term dependence, which makes the whole training process of the neural network architecture stable and efficient. For the lithofacies classification problem, our proposed model architecture is able to capture the general trend of lithofacies variation, but there are difficulties in the identification of partial sediments, especially the successive occurrence of thin-layer deposits. However, compared to the two comparison schemes, our classification framework has good recognition for class D lithology with small training samples. In addition, the BiLSTM neural network framework with additional median-filtered data as input exists less cases of recognition errors.  

Overall, our proposed framework has higher recognition accuracy. In terms of details, for lithology facies D with small training samples, only our proposed method accurately identifies the class D lithofacies located at $2 9 8 4 \mathrm { ~ m ~ }$ , which reflects the effectiveness of the BiLSTM model. In contrast, for the CSiS lithology facies with a larger number of training samples, all comparative approaches provide relatively accurate prediction results. In addition, comparing the lithofacies recognition frameworks with different network architectures and input data, it is easy to find that the resolution of the classification results and the recognition accuracy of some thin-layered sedimentary lithology still need to be improved.  

Likewise, our experiments have revealed some potential problems. It is clear that even with LSTM network-based lithofacies identification, the median-filtered logging input still improves the identification of thin-layer sediments. However, it still fails to identify the class D lithology facies with small training samples. The impact of the class imbalance of the training samples on the recognition results cannot be ignored. In addition, the generalization application capability of the network needs to be further demonstrated. Furthermore, the subsequent research still needs to improve and refine the algorithm refinement, training sample enhancement, and model migration.  

# V. CONCLUSION  

The BiLSTM neural network architecture is proposed to automatically identify sedimentary lithofacies from well logging measurements. As a demonstrated network configuration with powerful sequence data prediction capability, the BiLSTM model also has great potential for lithofacies classification. In contrast to the direct raw data input approach, we carry out the correlation analysis of the well logging data and perform the median filtering for the logging curves at different scales with respect to the degree of correlation. Median filtering removes high-frequency noise from the well logging curves while retaining valid edge information. The proposed model is trained based on both the raw and the filtered data, which ensures the noise assistance of the model while improves the accuracy of the model classification. Furthermore, we compare the differences in the model and the differences in the input data separately and find that the introduction of the bidirectional system more comprehensively obtains the subsurface information provided by well logging curves compared to the LSTM network model. Therefore, the BiLSTM structure further improves the identification accuracy. Moreover, the input of median filtered data improves the network to identify thin interbedded lithology facies and better distinguish the boundaries of different lithofacies.  

# REFERENCES  

[1] Q. Wang, X. Zhang, B. Tang, Y. Ma, J. Xing, and L. Liu, “Lithology identification technology using BP neural network based on XRF,” Acta Geophysica, vol. 69, no. 6, pp. 2231–2240, Dec. 2021.   
[2] Z. Xu, H. Shi, P. Lin, and T. Liu, “Integrated lithology identification based on images and elemental data from rocks,” J. Petroleum Sci. Eng., vol. 205, Oct. 2021, Art. no. 108853.   
[3] J. Lin, H. Li, N. Liu, J. Gao, and Z. Li, “Automatic lithology identification by applying LSTM to logging data: A case study in X tight rock reservoirs,” IEEE Geosci. Remote Sens. Lett., vol. 18, no. 8, pp. 1361–1365, Aug. 2021.   
[4] C. Jiang, D. Zhang, and S. Chen, “Lithology identification from welllog curves via neural networks with additional geologic constraint,” Geophysics, vol. 86, no. 5, pp. IM85–IM100, Sep. 2021.   
[5] P. Y. Zhang, J. M. Sun, Y. J. Jiang, and J. S. Gao, “Deep learning method for lithology identification from borehole images,” in Proc. EAGE Conf. Exhib., Jun. 2017, pp. 1–5.   
[6] D. T. dos Santos, M. Roisenberg, and M. dos Santos Nascimento, “Deep recurrent neural networks approach to sedimentary facies classification using well logs,” IEEE Geosci. Remote Sens. Lett., vol. 19, pp. 1–5, 2022.   
[7] B. Hall, “Facies classification using machine learning,” Lead. Edge, vol. 35, no. 10, pp. 906–909, Oct. 2016.   
[8] J. Chang, Y. Kang, Z. Li, W. X. Zheng, W. Lv, and D.-Y. Feng, “Cross-domain lithology identification using active learning and source reweighting,” IEEE Geosci. Remote Sens. Lett., vol. 19, pp. 1–5, 2022.   
[9] Z. Xu, H. Shi, P. Lin, and W. Ma, “Intelligent on-site lithology identification based on deep learning of rock images and elemental data,” IEEE Geosci. Remote Sens. Lett., vol. 19, pp. 1–5, 2022.   
[10] Z. Xu, W. Ma, P. Lin, H. Shi, D. Pan, and T. Liu, “Deep learning of rock images for intelligent lithology identification,” Comput. Geosci., vol. 154, Sep. 2021, Art. no. 104799.   
[11] Z. Xu, W. Ma, P. Lin, and Y. Hua, “Deep learning of rock microscopic images for intelligent lithology identification: Neural network comparison and selection,” J. Rock Mech. Geotech. Eng., vol. 14, no. 4, pp. 1140–1152, Aug. 2022.   
[12] F. Alzubaidi, P. Mostaghimi, P. Swietojanski, S. R. Clark, and R. T. Armstrong, “Automated lithology classification from drill core images using convolutional neural networks,” J. Petroleum Sci. Eng., vol. 197, Feb. 2021, Art. no. 107933.   
[13] S. Li, K. Zhou, L. Zhao, Q. Xu, and J. Liu, “An improved lithology identification approach based on representation enhancement by logging feature decomposition, selection and transformation,” J. Petroleum Sci. Eng., vol. 209, Feb. 2022, Art. no. 109842.   
[14] X. Liu, X. Chen, J. Li, X. Zhou, and Y. Chen, “Facies identification based on multikernel relevance vector machine,” IEEE Trans. Geosci. Remote Sens., vol. 58, no. 10, pp. 7269–7282, Oct. 2020.   
[15] K. Zhou, J. Zhang, Y. Ren, Z. Huang, and L. Zhao, “A gradient boosting decision tree algorithm combining synthetic minority oversampling technique for lithology identification,” Geophysics, vol. 85, no. 4, pp. WA147–WA158, Jul. 2020.   
[16] Q. Ren, H. Zhang, D. Zhang, and X. Zhao, “Lithology identification using principal component analysis and particle swarm optimization fuzzy decision tree,” J. Petroleum Sci. Eng., vol. 220, Jan. 2023, Art. no. 111233.   
[17] P. Wang, X. Chen, B. Wang, J. Li, and H. Dai, “An improved method for lithology identification based on a hidden Markov model and random forests,” Geophysics, vol. 85, no. 6, pp. IM27–IM36, Nov. 2020.   
[18] W. Xiong, C. Xu, L. Li, S. Zhan, P. Chen, and Y. Gao, “CNN-based logging lithology identification technique and its application,” in Proc. EAGE Conf. Exhib., vol. 6, 2021, pp. 4423–4427.   
[19] K. Gao and S. Jiao, “Research on lithology identification based on multisensor hybrid domain information fusion and support vector machine,” Earth Sci. Informat., vol. 15, no. 2, pp. 1101–1113, Jun. 2022.   
[20] Z. Hong, J. Yao, K. Li, and G. Hu, “Conjunction of active and semi-supervised learning for wireline logs-based automatic lithology identification,” IEEE Geosci. Remote Sens. Lett., vol. 19, pp. 1–5, 2022.   
[21] Z. Li, Y. Kang, W. Lv, W. X. Zheng, and X.-M. Wang, “Interpretable semisupervised classification method under multiple smoothness assumptions with application to lithology identification,” IEEE Geosci. Remote Sens. Lett., vol. 18, no. 3, pp. 386–390, Mar. 2021.   
[22] W. Xie and K. T. Spikes, “Well-log facies classification using an active semi-supervised algorithm with pairwise constraints,” Geophys. J. Int., vol. 229, no. 1, pp. 56–69, Oct. 2021.   
[23] Q. Ren, H. Zhang, D. Zhang, X. Zhao, L. Yan, and J. Rui, “A novel hybrid method of lithology identification based on k-mean $^ { + + }$ algorithm and fuzzy decision tree,” J. Petroleum Sci. Eng., vol. 208, Jan. 2022, Art. no. 109681.   
[24] J. Chang et al., “Unsupervised domain adaptation using maximum mean discrepancy optimization for lithology identification,” Geophysics, vol. 86, no. 2, pp. ID19–ID30, Mar. 2021.   
[25] C. M. Saporetti, L. G. da Fonseca, and E. Pereira, “A lithology identification approach based on machine learning with evolutionary parameter tuning,” IEEE Geosci. Remote Sens. Lett., vol. 16, no. 12, pp. 1819–1823, Dec. 2019.   
[26] W. Zhu, X. Li, C. Liu, F. Xue, and Y. Han, “An STFT-LSTM system for P-wave identification,” IEEE Geosci. Remote Sens. Lett., vol. 17, no. 3, pp. 519–523, Mar. 2020.   
[27] Y. Wu, S. Pan, J. Chen, G. Song, and Q. Gou, “A surface-wave inversion method based on FHLV loss function in LSTM,” IEEE Geosci. Remote Sens. Lett., vol. 19, pp. 1–5, 2022.   
[28] H. Alzahrani and J. Shragge, “Seismic velocity model building using recurrent neural networks: A frequency-steeping approach,” IEEE Trans. Geosci. Remote Sens., vol. 60, 2022, Art. no. 5921209.   
[29] D. Yoon, Z. Yeeh, and J. Byun, “Seismic data reconstruction using deep bidirectional long short-term memory with skip connections,” IEEE Geosci. Remote Sens. Lett., vol. 18, no. 7, pp. 1298–1302, Jul. 2021.   
[30] X. J. Shi, Z. R. Chen, H. Wang, D. Y. Yeung, W. K. Wong, and W. C. Woo, “Convolutional LSTM network: A machine learning approach for precipitation nowcasting,” in Proc. Adv. Neural Inf. Proces. Syst., 2015, pp. 802–810.   
[31] N. Pham, X. Wu, and E. Zabihi Naeini, “Missing well log prediction using convolutional long short-term memory network,” Geophysics, vol. 85, no. 4, pp. WA159–WA171, Jul. 2020.   
[32] C. Birnie and F. Hansteen, “Bidirectional recurrent neural networks for seismic event detection,” Geophysics, vol. 87, no. 3, pp. KS97–KS111, May 2022.  