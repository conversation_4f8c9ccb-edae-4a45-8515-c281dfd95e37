# A Lithology Identification Approach Using Well Logs Data and Convolutional Long Short-Term Memory Networks

<PERSON>, Graduate Student Member, IEEE, and Jun<PERSON> Cao

Abstract—Lithology identification plays a crucial role in formation characterization and reservoir exploration. When available core samples are limited, well logs data becomes important in lithology identification. Various machine- learning algorithms have been adopted to identify lithology. However, because of the spatial coupling of logging data and the vertical spatial relationship of different depths, the lithology identification of subsurface reservoirs is a challenging task. To solve this challenge, we propose a lithology identification method based on a deep learning (DL) model, which combines a convolutional neural network (CNN) and a long short- term memory (LSTM) network to exert their complementary advantages. In the network mentioned above, the CNN is used to extract the complex features of the logging data, whereas the LSTM is designed to extract the vertical spatial relationship from the output features of the CNN, and finally, the mapping relationships between well logs and lithology types are established. The application results of two cases on field datasets demonstrate the effectiveness of the proposed method compared to the benchmark models. The proposed model is expected to be useful for identifying the lithology of complex strata.

Index Terms—Convolutional neural network (CNN), deep learning (DL), lithology identification, long short- term memory (LSTM) network.

# I. INTRODUCTION

ACURATE lithology identification is a key issue in oil/gas reservoir prediction. Due to its significance in reservoir characterization, research on lithology identification has aroused numerous concerns among researchers [1], [2], [3], [4], [5], [6], [7]. Conventionally, lithology can be decided by examining cuttings or core samples obtained through the drilling process. However, depth mismatch may easily affect this procedure and demand a higher coring cost. Moreover, there are two demerits in the analysis of drilling cuts or core samples [6]. First, core samples are not contained in all of the wells, and second, the entire procedure must be done manually. Researchers tend to perform lithology discrimination by indirectly using well logs to curtail such issues because of the strengths in extraordinary continuity, high resolution, and convenient data collection [8]. However, the sophistication in lithology results in the overlapped distribution of logging data and reinforces multisolution classification. Consequently, in restricted conditions, the improvement of lithology identification accuracy proves to be a worthwhile research topic [9].

Recently, due to the advancement of computer technology and deep learning (DL) [10], DL techniques seek more concern in oil and gas exploration and development [11], [12], [13], [14]. In recent times, the development of DL has promoted new technologies for lithology identification research [5], [6], [7], [8]. Moreover, certain approaches, including convolutional neural network (CNN) [15], [16], [17] and long short- term memory (LSTM) [18], [19], [20] networks, have been proven to have high accuracy in lithology prediction. DL is gradually becoming an important tool in lithology prediction using well logs data [15], [16], [17], [18], [19], [20]. It can mine the complex relationship between logging data and different lithology to improve the accuracy of lithology identification, especially CNN and LSTM. LSTM is a dominant approach for sequence modeling tasks, whereas CNN is ideal for complex feature extraction. CNN can learn local features from data, but the data before and after are independent of each other, and the sequential correlation of information cannot be extracted. LSTM is appropriate for solving the lithology identification task, considering their capability to learn long- term dependencies. The LSTM- based networks enable sequential data processing, thus allowing the model to acquire the information of neighboring formations in lithology prediction at a specific depth. Although LSTM efficiently processes the variable- length sequences and extracts the sequence features from logging data, it cannot focus on the crucial information in the reservoir.

Both lithology class and relevant physical properties in local sediments are subjected to the depositional environment. Owing to the relative stability of the depositional environment in a specific period, sediments in a period exhibit higher similarity with those in another period [20]. Thus, there exists a certain spatial dependency among the adjacent sediments. Rock properties usually show a tendency to change with depths, which is very important for geological studies [21], [22]. Logging data potentially contain various attribute characteristic information of formation rocks; each

observed value for logs should be the weighted sum of the logging responses of adjacent sediments [2], which also have a certain spatial coupling relationship with each other. Therefore, when using logging data for lithology identification, the correlation between logging data and different lithologies should be considered, and the key information of logging data along the depth should be considered. Although the classic CNN and LSTM networks exhibit excellent performance under different conditions in data- driven lithology identification, they do not consider a vertical spatial relationship, and spatial coupling simultaneously. However, no relevant research report combines CNN and LSTM for lithology identification.

Therefore, in this letter, one new architecture is developed by combining CNN and LSTM to leverage their complementary strengths. The test results of two cases on field datasets indicate that, compared with the benchmark models CNN and LSTM, the constructed model can not only make full use of logging the response value of the corresponding depth but also consider the influence of vertical spatial relationship, avoiding the influence of horizontal and vertical heterogeneity of reservoir and therefore improve lithology identification ability.

# II. METHODOLOGY

The DL is recognized as a promising method in the machine learning field, which has also gained wide usage in image analysis, speech recognition, and text comprehension [10]. The most common uses of DL methods are CNN and LSTM, in which CNN has an edge on feature extraction, and LSTM responsibly mines the sequence data. Therefore, to fully exert these methods' merits to accurately identify the lithology, these two are integrated to develop one novel model that brings their respective advantages.

# A. Convolutional Neural Network

CNN is a classic DL method. Its competence in automatic explanation and inversion of seismic or well logs has gained significant attention from geophysicists [15], [16], [17]. CNN is skilled in extracting local data features and spatial abstraction and generalization. CNN mainly includes 1- D CNN (1D CNN) and high- dimensional CNN. 1- D CNN is the recent variant of conventional 2- D CNN [23]. Although they were introduced only a few years ago, recent studies have revealed that 1- D CNN is superior to 2- D CNN in processing 1- D signals [23]. Hence, 1- D CNN is adopted in this research because convolution kernels only scan logs along the depth direction.

# B. LSTM Network

LSTM [24], [25] is a recurrent neural network (RNN) deformation structure that controls the memory information behind the sequence data by introducing memory cells into the hidden layer. Information can be transferred to various cells on the hidden layer with the help of a few controllable gates (forget gate, input gate, and output gate). These gates control the memory by forgetting the degree of past and existing information. In contrast to conventional RNN, LSTM possesses long- term memory functions, which avoids the issue of gradient disappearance. Two LSTM gates have been proposed to control the memory cell state. One is the forget gate that suggests the total "memory" that the cell can save till the last moment. The other is the input gate, which decides the total number of inputs that can be saved at the current moment. An output gate in LSTM is eventually developed to control the amount of information output in the cell state. When LSTM is applied to lithology identification, it can consider the key information of well logs with depth, which is more in line with the geological idea. A bidirectional LSTM network is an improved LSTM with bidirectional feedback in the forward and backward layers.

# C. Integrated Network Architecture

CNN and LSTM belong to mainstream algorithms adopted by DL. From the perspective of reservoir sedimentary continuity, well logs are considered as vertical sequences of data. LSTM can be used as an ideal approach in lithology identification for its ability to capture information from the data sequences and propagate the information of adjacent depths with depth- term dependencies. In numerous DL application scenarios, feature extraction has been considered a major step in producing meaningful information that helps enhance the accuracy of the prediction model. This letter links the CNN to the LSTM network and utilizes serial connections to integrate the two networks for obtaining an integrated model, which can leverage the complementary strength of both networks. First, the complex characteristics of the well logs data are mined using CNN. Next, the features extracted by the CNN are used as input to the LSTM. The critical features of logging data in the depth direction are learned using the LSTM network. These learned features are then transmitted to the fully connected layer directly connected to the LSTM layer. Eventually, the identification result is obtained through the fully connected layer. The frame diagram of the proposed model is shown in Fig. 1.

# D. Workflow of the Proposed Approach

The lithology identification process proposed in this letter mainly includes the following steps: data collection and preprocessing, model design and training, and model performance evaluation. In step 1: data is collected, normalized, and divided into training and test sets. In step 2, we establish the prediction models. Based on the well logs data obtained with lithology, different DL models are constructed in this study. After data normalization and classifying the training set and testing set, the training data can be employed for training the models. The involved hyperparameters in the training process included the epochs, number of hidden layer units, activation function, batch size, and optimizer. In step 3, the constructed models are evaluated to apply to the test dataset. The performance of three DL models is compared.

# E. Evaluation Metrics

In this letter, the model performance is evaluated in terms of the F1- score, which can evaluate the results of

![](images/60191606faad1c22f4d20126726bc67105cc47ab0d79b61f732174383483b92a.jpg)  
Fig. 1. Architecture of the proposed model for lithology identification.

multiclassification. This equation is given as follows:

$\mathrm{F1 - score} = 2\cdot \mathrm{Precision}\cdot \mathrm{Recall} / (\mathrm{Precision} + \mathrm{Recall})$  (1) where precision is the rate of predicted positive cases that are correctly real positives, and recall measures the percentage of actual positive samples that were classified as positive. The F1- score indicates the combination of recall and precision. A higher F1- score indicates a higher robustness of the classification mode. F1- score will be further applied in the subsequent model evaluation of the letter.

# III. CASE APPLICATION

To evaluate the proposed method's performance, two field datasets were collected from two exploration areas, and conducted two case studies. All the models are realized based on Keras, which is a DL library that uses TensorFlow as the backend. The complete workflow has been coded in Python 3.7, and the employed hardware comprises a  $2.5\mathrm{GHz}$  Intel Core2 i7- 4710MQ CPU with 128 GB of memory.

# A. Case One

In this section, the performance of the constructed DL model is verified using the field dataset of two wells in an exploration area. To keep the well information confidential, they are named as wells A1 and A2, respectively. In this case, lithology was predicted from density, deep lateral resistivity, gamma- ray, and sonic. The data from well A2 and A1 are used as training and testing sets, respectively. A1 and A2 wells are located in the clastic reservoir of X formation in an exploration area. The chief lithology is a unique combination of sandstone (label 1) and shale (label 2). The number of data for well A1 and A2 is 4581 and 4465, respectively.

This case sets the hyperparameters as follows: there are two CNN hidden layers with 64 convolution kernels of size four and stride one in each layer and a maxpooling layer. There are two LSTM hidden layers with 20 hidden units in each layer, a time step of five, a batch size of ten, a learning rate of 0.001, and a dropout ratio of 0.1. Besides, the Adam algorithm is chosen as the optimization method. These hyperparameters are chosen to carry out the next experiment. The hyperparameters of the CNN and LSTM models are identical to the hyperparameters of CLSTM. It is interesting to note that such hyperparameters may be tuned to gain superior performance.

For a visual comparison of the considered model results, the original lithology and the results predicted by CLSTM are plotted and compared with the benchmark models CNN and LSTM in Fig. 2, respectively. The CLSTM, LSTM, and CNN represent the predicted lithology by CLSTM, LSTM, and CNN. The CORE column corresponds to the real lithology as a reference. Fig. 2 shows that the lithology in the testing set predicted with the trained CLSTM model can better conform to the real lithology. Although the benchmark models LSTM and CNN achieve certain predicted results, the CLSTM leads to the most accurate prediction. This proves that the designed CLSTM is more reliable in lithology identification from well logs. It can be observed from the identification results of sandstone and shale in Fig. 2 that the CNN and LSTM models provide more stochastic, frequent, and thin sandstone layers with adjacent shale layers along with the well profile. In identifying sandstone and shale with strong lithologic changes, CLSTM can obtain better results than LSTM and CNN. This suggests that CLSTM had better sequence modeling capability than the LSTM and CNN models.

![](images/0abd7dbe63b3b4c8d0fd8e7e7bda92af88797b80a94912867e25fa1869e0507c.jpg)  
Fig. 2. Identification results of three models for well A2.

In terms of F1- score, the performance of the CNN is the worst (0.828); LSTM occupied the second place (0.860), and the performance of the CLSTM is the best (0.873). Specifically, CLSTM increased the F1- score by  $1.49\%$  and  $5.15\%$  compared with LSTM and CNN results, respectively, indicating that better identification results can be achieved by making full use of the local correlations between well logs and the key information of well logs along the depth direction. In other words, for the models with spatial coupling and vertical relationship considered, the CLSTM performs better than the LSTM and CNN models.

# B. Case Two

Field data in the North Sea area can be accessed via GEOLINK and were selected for this case application to further verify the proposed model. According to the literature [6], the lithology of selected wells is divided into seven types:

sandstone (label 1), siltstone and claystone (label 2), limestone and chalk (label 3), argillaceous limestone and marlstone (label 4), cinerite and volcanic tuff (label 5), coal (label 6), and anhydrite (label 7). A total of 14 wells (here, they are named wells B1- B14, respectively) from the study area comprise the dataset, which was divided into training sets (nine wells, B6- B14) and test sets (five wells, B1- B5). Here, lithology was predicted from gamma ray, bulk density, neutron porosity, sonic, resistivity, and group information. The details of the group information can be obtained from the literature [6], which will not go into them here.

Considering that the lithology types in this case are more complex and difficult to identify. Here, in addition to using the CLSTM model mentioned in case one for lithology identification, we also constructed a CBLSTM identification model that combines CNN and bidirectional LSTM. This case sets the hyperparameters as follows: there are seven CNN layers with 64 convolution kernels of size four and stride one in each layer and a maxpooling layer. There are three LSTM or bidirectional LSTM layers, with 64 hidden units in each layer, a time step of ten, a batch size of 100, a learning rate of 0.001, and a dropout ratio of 0.2. The optimizer is the Adam algorithm. The hyperparameters of CNN and LSTM are the same as those of CLSTM. Note that these hyperparameters can also be tuned for better performance.

After training, the models were evaluated on the testing set. The F1- score of predicted results of the CBLSTM, CLSTM, LSTM, and CNN models are shown and compared in Table I. Table I shows that the prediction results of the CBLSTM and CLSTM are more accurate than the LSTM and CNN models in terms of F1- score. The results show that it is difficult to mine the complex mapping relationship between logging data and different lithology only relying on a single LSTM and CNN, so the accuracy of lithology identification needs to be further improved. It is proved that adding CNN to extract multiscale spatial features of well logs is more conducive to subsequent identification, the running time of CLSTM and CNN is significantly less than that of LSTM. Based on the F1- score and runtime analysis of CLSTM, LSTM, and CNN, the proposed model has a higher cost- performance ratio.

To further analyze the performance of the CBLSTM, CLSTM, LSTM, and CNN models, the original lithology of well B1 (contains only five of the seven lithology types) and the partial results predicted by CBLSTM and CLSTM are plotted and compared with CNN and LSTM in Fig. 3, respectively. The CBLSTM, CLSTM, LSTM, and CNN represent the predicted lithology by CBLSTM, CLSTM, LSTM, and CNN. The CORE column corresponds to the real lithology as a reference. Due to the limited space of the letter, only the partial results from well B1 are presented and analyzed here. The results of other wells are similar to this well, and they are not shown and analyzed in detail.

Fig. 3 indicates that the identification result of CLSTM and CBLSTM is better than that of LSTM and CNN. For example, in the range of  $1575 - 1602\mathrm{m}$  the identification results of the benchmark models LSTM and CNN have a large deviation from the real lithology. Nevertheless, the CBLSTM and CLSTM still have a superior ability to identify the target lithology. The results further show that it is difficult to mine complex features hidden in logging data by relying only on a single network structure in complex and multitype lithology identification. The proposed model combines CNN and LSTM to exert their complementary advantages fully. Thus, the proposed model can accurately establish the complex mapping relationship between logging data and different lithologies. Besides, the benchmark models are more unstable than the proposed model, which signifies that the integrated DL model exhibits higher robustness on data under unknown patterns.

TABLEI F1-SCORE OF DIFFERENT MODELS  

<table><tr><td rowspan="2">Well names</td><td colspan="4">Models</td></tr><tr><td>CBLSTM</td><td>CLSTM</td><td>LSTM</td><td>CNN</td></tr><tr><td>B1</td><td>0.944</td><td>0.915</td><td>0.864</td><td>0.767</td></tr><tr><td>B2</td><td>0.887</td><td>0.848</td><td>0.838</td><td>0.842</td></tr><tr><td>B3</td><td>0.873</td><td>0.870</td><td>0.824</td><td>0.853</td></tr><tr><td>B4</td><td>0.863</td><td>0.867</td><td>0.817</td><td>0.837</td></tr><tr><td>B5</td><td>0.814</td><td>0.791</td><td>0.730</td><td>0.772</td></tr></table>

![](images/7751e0a32003c4a96be11b990966d7e0afcf72b2a0827d628cbac4710c165ff1.jpg)  
Fig. 3. Identification results of four models for well B1.

However, in the range of  $1650 - 1700\mathrm{m}$ , the identification error of CBLSTM and CLSTM is larger than that of LSTM, indicating that although CBLSTM and CLSTM combine the advantages of CNN and LSTM, it can learn the complex features between logging data and lithology, the learned nonkey features will also reduce the accuracy of the identification results to a certain extent. Table I and Fig. 3 also show that CNN and LSTM have distinct internal structures and offer different advantages in solving specific problems. However, there is little difference in the overall identification results of lithology from well logs. Comparing the identification results of CBLSTM and CLSTM in Fig. 3, it can be seen that the identification results of CLSTM are not ideal in the range of  $2110 - 2200\mathrm{m}$ , indicating that bidirectional LSTM is more suitable for lithology identification. This may be because LSTM calculates new hidden layer states solely based on the input data at the current time step and the hidden layer states from previous time steps. This nature limits their ability to

effectively utilize the information from the reverse depth of the reservoir. The bidirectional LSTM offers the capability to capture reservoir depth- related information from forward and backward well logs data along the depth direction.

# IV. CONCLUSION

Lithology identification is an important problem in reservoir characterization. In this letter, we propose a novel model to identify the lithology with respect to logging data by combining CNN and LSTM. First, the CNN is used to mine the mapping between well logs and different lithology, and then the LSTM is designed to extract the vertical spatial relationship of different depths. Therefore, the network can correctly resolve reservoir feature relationships within the depth sequence. The results of the two application cases prove that although CNN and LSTM with different internal structures can establish different mapping relationships between logging data and lithology according to different features learned, their lithology identification abilities are basically the same. Compared with the benchmark models CNN and LSTM, the proposed model is the most reliable one as it combines the vertical spatial relationship and spatial coupling in observations by integrating LSTM and CNN's advantages. The model combined with bidirectional LSTM performs better. Further studies will consider adding total variation regularization constraints to the loss function and applying median filtering to the lithologic probability curves to improve the model identification accuracy. Moreover, applications in more and different formations will be required to test the generality of this conclusion and handle unbalanced datasets.

# REFERENCES

[1] C.M. Saporetti, L.G. da Fonseca, and E. PereiraA lithology identification approach based on machine learning with evolutionary parameter tuningIEEE Geosci.Remote Sens.Lett.vol.16,no.12, pp.1819- 1823,Dec.2019. [2] D.V. LindbergE. Rimstad, and H. Omre,Inversion of well logs into facies accounting for spatial dependencies and convolution effects, J.Petroleum Sci.Eng.vol.134pp.237- 246Oct.2015. [3] Y. AoH. LiL. ZhuS. Ali and Z.YangLogging lithology discrimination in the prototype similarity space with random forest, IEEE Geosci. Remote Sens.Lett.vol.16,no.5,pp.687- 691,May 2019. [4] J. Chang et al., Active domain adaptation with application to intelligent logging lithology identification, IEEE Trans. Cybern., vol. 52, no.8, pp.8073- 8087,Aug.2022,doi:10.1109/TCYB.2021.3049609. [5] X. Ren et al., Lithology identicality well logs: A method by integrating artificial neural networks and sedimentary patterns, J.Petroleum Sci.Engvol.182,Nov.2019Art.no.106336, doi: 10.1016/j.petrol.2019.106336. [6] C. Jiang, D. Zhang, and S. Chen, Lithology identification from welllog curves via neural networks with additional geologic constraint, Geophysics,vol.86,no.5,pp.IM85- IM100,Sep.2021.

[7] N. Liu, T. Huang, J. Gao, Z. Xu, D. Wang, and F. Li, "Quantum- enhanced deep learning- based lithology interpretation from well logs," IEEE Trans. Geosci. Remote Sens., vol. 60, 2022, Art. no. 4503213, doi: 10.1109/TGRS.2021.3085340. [8] Y. Xie, C. Zhu, W. Zhou, Z. Li, X. Liu, and M. Tu, "Evaluation of machine learning methods for formation lithology identification: A comparison of tuning processes and model performances," J. Petroleum Sci. Eng., vol. 160, pp. 182- 193, Jan. 2018. [9] R. Feng, "Lithofacies classification based on a hybrid system of artificial neural networks and hidden Markov models," Geophys. J. Int., vol. 221, no. 3, pp. 1484- 1498, Jun. 2020. [10] Y. LeCun, Y. Bengio, and G. Hinton, "Deep learning," Nature, vol. 521, no. 7553, pp. 436- 444, May 2015. [11] Y. Ao, W. Lu, Q. Hou, and B. Jiang, "Sequence- to- sequence borehole formation property prediction via multi- task deep networks with sparse core calibration," J. Petroleum Sci. Eng., vol. 208, Jan. 2022, Art. no. 109637, doi: 10.1016/j.petrol.2021.109637. [12] Y. Chen and D. Zhang, "Well log generation via ensemble long short- term memory (EnLSTM) network," Geophys. Res. Lett., vol. 47, no. 23, 2020, Art. no. e2020GL087685, doi: 10.1029/2020GL087685. [13] Y. Chen and D. Zhang, "Physics- constrained deep learning of geomechanical logs," IEEE Trans. Geosci. Remote Sens., vol. 58, no. 8, pp. 5932- 5943, Aug. 2020. [14] S. Yu and J. Ma, "Deep learning for geophysics: Current and future trends," Rev. Geophys., vol. 59, no. 3, pp. 1- 74, Sep. 2021, doi: 10.1029/2021RG000742. [15] Y. Imamverdiyev and L. Sukhostat, "Lithological facies classification using deep convolutional neural network," J. Petroleum Sci. Eng., vol. 174, pp. 216- 228, Mar. 2019. [16] F. Alzubaidi, P. Mostaghimi, P. Swietojanski, S. R. Clark, and R. T. Armstrong, "Automated lithology classification from drill core images using convolutional neural networks," J. Petroleum Sci. Eng., vol. 197, Feb. 2021, Art. no. 107933, doi: 10.1016/J.PETROL.2020.107933. [17] J. Zhang, J. Li, X. Chen, and Y. Li, "Seismic lithology/fluid prediction via a hybrid ISD- CNN," IEEE Geosci. Remote Sens. Lett., vol. 18, no. 1, pp. 13- 17, Jan. 2021. [18] L. Zeng, W. Ren, and L. Shan, "Attention- based bidirectional gated recurrent unit neural networks for well logs prediction and lithology identification," Neurocomputing, vol. 414, pp. 153- 171, Nov. 2020. [19] J. Lin, H. Li, N. Liu, J. Gao, and Z. Li, "Automatic lithology identification by applying LSTM to logging data: A case study in X tight rock reservoirs," IEEE Geosci. Remote Sens. Lett., vol. 18, no. 8, pp. 1361- 1365, Aug. 2021, doi: 10.1109/LGRS.2020.3001282. [20] M. Tian, H. Omre, and H. Xu, "Inversion of well logs into lithology classes accounting for spatial dependencies by using hidden Markov models and recurrent neural networks," J. Petroleum Sci. Eng., vol. 196, Jan. 2021, Art. no. 107598, doi: 10.1016/J.PETROL.2020.107598. [21] N. Pham, X. Wu, and E. Zabihi Naeini, "Missing well log prediction using convolutional long short- term memory network," Geophysics, vol. 85, no. 4, pp. WA159- WA171, May 2020. [22] W. Chen, L. Yang, B. Zha, M. Zhang, and Y. Chen, "Deep learning reservoir porosity prediction based on multilayer long short- term memory network," Geophysics, vol. 85, no. 4, pp. WA213- WA225, Jul. 2020. [23] S. Kiranyaz, O. Avci, O. Abdeljaber, T. Ince, M. Gabbouj, and V. J. Inman, "1D convolutional neural networks and applications: A survey," Mech. Syst. Signal Process., vol. 151, Apr. 2021, Art. no. 107398, doi: 10.1016/j.ymssp.2020.107398. [24] S. Hochreiter and J. Schmidhuber, "Long short- time memory," Neural Comput., vol. 9, no. 8, pp. 1735- 1780, Dec. 1997. [25] D. Zhang, Y. Chen, and J. Meng, "Synthetic well logs generation via recurrent neural networks," Petroleum Exploration Develop., vol. 45, no. 4, pp. 629- 639, Aug. 2018.