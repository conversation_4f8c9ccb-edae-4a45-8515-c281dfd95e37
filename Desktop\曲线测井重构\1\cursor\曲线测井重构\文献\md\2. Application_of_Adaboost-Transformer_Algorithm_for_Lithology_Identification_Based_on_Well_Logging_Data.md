# Application of Adaboost-Transformer Algorithm for Lithology Identification Based on Well Logging Data  

Youzhuang $\operatorname { S u n } ^ { \mathbb { \oplus } } .$ , <PERSON><PERSON>, and <PERSON><PERSON>  

Abstract— In the field of oil and gas exploration, accurately predicting lithology during well logging is crucial. This research introduces a novel approach, the Adaboost-Transformer method, which utilizes data mining techniques to enhance logging lithology prediction. The first step involves applying the Adaboost algorithm for selecting features, which develops a strong classifier by focusing on weighted observations and particularly challenging samples. This approach not only boosts accuracy and durability but also lessens the likelihood of overfitting. Following this, we implement the Transformer as the principal classifier in creating the Adaboost-Transformer model. The Transformer is specifically designed for sequential data and is highly efficient in modeling sequences, especially in capturing temporal links within well logging data. When tested on two separate well logging datasets, this innovative model exhibited superior lithology identification capabilities, achieving accuracy rates of $9 5 . 2 0 \%$ and $9 5 . 5 0 \%$ . These figures notably surpass those of the standalone Transformer model, which scored $\mathbf {9 1 . 5 0 \% }$ and $\mathbf { 9 1 . 1 0 \% }$ in accuracy. In comparison, the random forest (RF) model displayed more limited performance with accuracy rates of $8 8 . 8 0 \%$ and $\mathbf { 8 7 . 1 0 \% }$ .  

Index Terms— Adaboost-Transformer method, data mining techniques, oil and gas exploration, well logging sequential data.  

# I. INTRODUCTION  

ITHOLOGIC prediction [1] is essential in detailed oil types and reservoir characteristics. While traditional methodologies face constraints and challenges in forming realistic physical models, machine learning [2] provides an automated alternative. By learning directly from logging data, machine learning techniques facilitate efficient lithology prediction, thereby saving time and reducing costs.  

Research has demonstrated that in the realm of lithological classification, support vector machines hold an advantage over both discriminant analysis and probabilistic neural networks [3]. Subsequent studies revealed the effectiveness of ensemble learning techniques, namely Bagging and Boosting, which outperformed multilayer perceptrons in similar tasks [4]. A fuzzy logic approach was implemented in a deep drilling project in Germany, yielding promising results for swift and accurate lithology prediction from logging data. Concurrently, methodologies employing Naive Bayes classifiers, support vector machines, and artificial neural networks were also applied to lithology prediction, achieving satisfactory results with data from 7 wells and 19 logging curves [5], [6]. Optimized support vector machines, particularly those configured for structural risk minimization (SRM) and empirical risk minimization (ERM), have shown improved efficacy in seismic inversion and petrophysical logging lithology prediction, as noted by Sebtosheikh and Salehi [7]. Recently, the adoption of wavelet transforms and enhanced $K$ -means clustering methods for lithology classification has been noted for its high accuracy [8]. Furthermore, multilayer feedforward neural networks have been effectively utilized in identifying coal, peat coal, and shale, with their predictions aligning closely with laboratory analyses [9].  

This letter proposes an innovative approach for enhancing machine learning models by weighting them using the Adaboost algorithm. The Adaboost-Transformer combines multiple Transformer models to concentrate on misclassified samples during the weighting process, thereby improving the overall prediction accuracy. Ensemble learning reduces the variance of the predictive model, ensuring stability even if one model underperforms on certain samples.  

In the introduction, we highlight the importance of lithology prediction in geology and provide an overview of current research in this area. In Section II, we explain in detail how we constructed the Adaboost-Transformer model, combining Adaboost’s weak learner concept with the deep learning capabilities of the Transformer. In the data analysis phase, we describe the well logging data we collected and our feature engineering approaches, as well as the evaluation metrics used to assess the model performance. We then analyze the experimental results, showcasing the advantages of our model over traditional methods. In the conclusion, we summarize the study and emphasize the practicality and benefits of our proposed model, providing readers with a clear understanding of our research contributions.  

# II. METHODOLOGY  

# A. AdaBoost-Transformer  

AdaBoost-Transformer is an ensemble learning method that combines AdaBoost classifiers and Transformer for improved lithology prediction in well logging. In the AdaBoost algorithm, it trains multiple weak classifiers by adjusting sample weights and linearly combines them using the Transformer model as a weak classifier. The Transformer, designed for sequential data processing, employs a self-attention mechanism to focus on different positions within a sequence, enhancing its ability to capture spatiotemporal dependencies in well logging data.  

where Score $( Q , K )$ is the attention score after scaling and $( d _ { k } ) ^ { 1 / 2 }$ is a scaling factor that controls the range of attention scores. The softmax function indexes the fractions and normalizes them to ensure that they are all between 0 and 1 and sum up to 1. This means that the output of each location is a probability distribution that represents the correlation of that location with other positions, which is necessary to perform a weighted average.  

1) AdaBoost improves overall model performance by integrating multiple weak classifiers, with the Transformer as one of them.   
2) The Transformer’s self-attention mechanism effectively captures long-range dependencies in sequences, crucial for well logging lithology prediction, especially in cases with distant geological connections.  

![](images/cd759d5ce72843f3b93b121fe43e32ffe62ac29c5a642aa1dec900094a51bb4b.jpg)  
Fig. 1. Structure of the Transformer module.  

Advantages of AdaBoost-Transformer in lithology prediction for well logging include.  

In the Transformer block, the dot product of softmax [10] with the value $( V )$ vector computes the self-attention mechanism’s output. This process applies attention weights to input features, generating the final output  

Fig. 1 outlines the Transformer block’s structure in this study. It starts with layer normalization (LN), crucial for training stability and convergence. Input features are divided into query $( Q )$ , key $( K )$ , and value $( V )$ components for the self-attention mechanism. This mechanism dynamically adjusts weights based on positional relationships, enhancing key information extraction. Query vectors assess relevance, key vectors establish similarity scores, and value vectors contribute to the attention-weighted average, capturing specific information at each location.  

In the Transformer block, the dot product operation (involving $\boldsymbol { Q }$ and $K$ ) computes self-attention similarity scores, a core of the self-attention mechanism. This operation is crucial, measuring similarity through direct dot product or alternative metrics. The dot product method is often used for its straightforward multiplication of query and key vectors, determining attention focus and adjusting weights based on positional relationships. This process enhances the capture of essential information within the self-attention mechanism  

$$
\operatorname { S c o r e } ( Q , K ) = Q \cdot K .
$$  

After the dot product in the Transformer block, scaling is employed to regulate attention weights, preventing gradient instability or training issues due to input dimensions. Scaled results then undergo softmax, crucial for normalizing attention scores into probabilities, facilitating effective feature averaging. The self-attention mechanism calculates scores, ensuring nonnegativity and normalization, vital for maintaining positive correlation and achieving efficient weighted averages. Softmax accomplishes this by converting input fractions into a probability distribution  

$$
\operatorname { A t t e n t i o n } ( Q , K ) = \operatorname { s o f t m a x } \left( \operatorname { S c o r e } ( Q , K ) \Big / \sqrt { d _ { k } } \right)
$$  

$$
{ \mathrm { O u t p u t } } = { \mathrm { A t t e n t i o n } } ( Q , K ) * V .
$$  

The self-attention mechanism [11] in the Transformer block dynamically adjusts attention weights in the input sequence, capturing intricate data relationships. The value vector, representing content at each location, is pivotal. Using a weighted average approach, the model aggregates information based on attention weights for the final output. To enhance robustness and prevent overfitting, a Dropout [12] nullifies specific neurons after self-attention, reducing complexity and improving generalization. The Transformer block includes a residual connection by adding input and output of Dropout, aiding the model in learning the identity function and addressing vanishing gradients in deep neural networks.  

The formal definition of the AdaBoost-Transformer model assumes that the feature is denoted as $\begin{array} { r l } { T } & { { } = } \end{array}$ $\{ ( x _ { 1 } , y _ { 1 } ) , ( x _ { 2 } , y _ { 2 } ) , \ldots , ( x _ { N } , y _ { N } ) \}$ . Each sample point consists of an instance and a marker. Instance $x _ { i } \in X \subseteq R ^ { n }$ and marker $y _ { i } \in Y$ . $X$ is the instance space and $Y$ is the collection of tags. The AdaBoost algorithm learns a series of weak classifiers or base classifiers from the training data and sets these weak classifiers linearly into one strong classifier.  

How Adaboost combines multiple Transformer models. Initially, each training sample gets equal weight. Specify the number of base classifiers and the error rate. Train the base classifier, adjusting the distribution based on classification errors. Emphasize learning from misclassified samples. Iterate until reaching the desired number of classifiers or meeting the error rate. Finally, a weighted majority vote determines the final outcome.  

1) Initialize the weight distribution of the training data  

$$
\begin{array} { c } { { D _ { 1 } = ( w _ { 1 1 } , \ldots , w _ { 1 i } , \ldots , w _ { 1 N } ) } } \\ { { w _ { 1 i } = \displaystyle \frac 1 N , \quad i = 1 , 2 , \ldots , N . } } \end{array}
$$  

2) AdaBoost iteratively learns the base classifier and sequentially performs the following operations.  

a) Use the training dataset with weight distribution $D _ { m }$ , the base classifier $G _ { m } ( x )$ is obtained $G _ { m } ( x ) { : } X \to Y$ .   
b) Calculate the classification error rate of $G _ { m } ( x )$ on the training set  

$$
e _ { m } = P ( G _ { m } ( x _ { i } ) \neq y _ { i } ) = \sum _ { i = 1 } ^ { N } w _ { m i } I ( G _ { m } ( x _ { i } ) \neq y _ { i } )
$$  

where $\boldsymbol { w _ { m i } }$ represents the weight of the $i$ th instance in round m and PiN=1 wmi = 1 indicates that the classification er or rate on the weighted training dataset is the sum of the weights of the $G _ { m } ( x )$ misclassified samples. The relationship between the data weight distribution $D _ { m }$ and the classification error rate of the base classifier $G _ { m } ( x )$ can be seen.  

c) Calculate the coefficient $\alpha _ { m }$ of the base classifier $G _ { m } ( x )$  

$$
\alpha _ { m } = \frac { 1 } { 2 } \log \frac { 1 - e _ { m } } { e _ { m } } .
$$  

The logarithm is the natural logarithm, and $\alpha _ { m }$ denotes the importance of $G _ { m } ( x )$ finally. It can be seen from this equation that when $e _ { m } \leq ( 1 / 2 )$ and $\alpha _ { m } \ \geq \ 0$ , and $\alpha _ { m }$ increases as $\boldsymbol { e } _ { m }$ decreases, the smaller the classification error rate, the greater the role of the base classifier in the final classifier.  

d) Update the weight distribution of the training dataset in preparation for the next round  

$$
\begin{array} { c } { { D _ { m + 1 } = \left( w _ { m + 1 , 1 } , \ldots , w _ { m + 1 , i } , \ldots , w _ { m + 1 , N } \right) } } \\ { { \ } } \\ { { w _ { m + 1 , i } = \displaystyle \frac { w _ { m } } { Z _ { m } } \exp ( - \alpha _ { m } y _ { i } G _ { m } ( x _ { i } ) ) } } \\ { { \displaystyle i = 1 , 2 , \ldots , N } } \\ { { \ } } \\ { { Z _ { m } = \displaystyle \sum _ { i = 1 } ^ { N } w _ { m i } \exp ( - \alpha _ { m } y _ { i } G _ { m } ( x _ { i } ) ) } } \end{array}
$$  

where $Z _ { m }$ is the normalization factor, which makes $D _ { m + 1 }$ a probability distribution. The weight of the misclassified sample of classifier $G _ { m } ( x )$ is expanded, but the weight of the correctly classified sample is reduced, and the weight of the misclassified sample is amplified $e ^ { 2 \alpha _ { m } } = \bar { ( \varepsilon _ { m } / ( 1 - \varepsilon _ { m } ) ) }$ times by comparing the two. Therefore, misclassified samples play a greater role in the next round of learning.  

3) Construct a linear combination of base classifiers  

$$
f ( x ) = \sum _ { m = 1 } ^ { M } \alpha _ { m } G _ { m } ( x ) .
$$  

Get the final classifier  

$$
G ( x ) = \mathrm { s i g n } ( f ( x ) ) = \mathrm { s i g n } \Bigg ( \sum _ { m = 1 } ^ { M } \alpha _ { m } G _ { m } ( x ) \Bigg ) .
$$  

The linear combination $f ( x )$ realizes the weighted voting of $M$ basis classifiers, and the coefficient $\alpha _ { m }$ indicates the importance of the basis classifier $G _ { m } ( x )$ . The AdaBoost ensemble algorithm trains multiple base classifiers into one strong learner.  

# B. Model Training Error Analysis  

In lithology classification, AdaBoost-Transformer improves the performance by learning weak classifiers and combining them through weighted voting. It adjusts data weights, reducing correct sample weight and increasing misclassified sample weight. AdaBoost-Transformer adapts to different classifiers, dynamically adjusting weights based on error rates, aiming for a goal. Its strength lies in effective data selection, obtaining a powerful learner with superior performance, continuously reducing training error  

$$
{ \frac { 1 } { N } } \sum _ { i = 1 } ^ { N } I ( G ( x _ { i } ) \neq y _ { i } ) \leq { \frac { 1 } { N } } \sum _ { i } \exp ( - y _ { i } f ( x _ { i } ) ) = \prod _ { m } Z _ { m }
$$  

. Initialize the weight distribution of the training data for i from 1 to $N$ do $w _ { i } = 1 / N$   
. (a) Iterate through classifiers for m from 1 to $M$ do $G _ { m } ( x ) { : } X \to Y$ (b) Calculate the error rate of the base classifier em = 0 for i from 1 to $N$ do if $G _ { m } ( x _ { i } ) \neq y _ { i }$ then $e _ { m } + = w _ { i }$ (c) Calculate the performance of the classifier $\alpha _ { m } = \frac { 1 } { 2 } l o g \frac { 1 - e _ { m } } { e _ { m } }$ (d) Update the weights for the next iteration for i from 1 to $N$ do if the training set is classified correctly then $w _ { i } \ast = \exp ( - \alpha _ { m } )$ else $\begin{array} { r } { w _ { i } \ast = \exp ( \alpha _ { m } ) } \\ { w _ { s u m } + = w _ { i } } \end{array}$ for i from 1 to $N$ do ${ w _ { i } } / = w _ { s u m }$   
. Construct the final classifier   
or m from 1 to $M$ do   
$O u t p u t + = \alpha _ { m } * G _ { m } ( x )$   
eturn sign(Output)  

where $G ( x ) , f ( x )$ and $Z _ { m }$ are obtained from the previous formula.  

When $G ( x _ { i } ) ~ \neq ~ y _ { i }$ , $y _ { i } f ( x _ { i } ) ~ < ~ 0$ , $\exp ( - y _ { i } f ( x _ { i } ) ) ~ \geq ~ 1$ . Therefore, the first half of the above formula holds. The proof of the second half is pushed to the following:  

$$
\begin{array} { r l } & { \frac { 1 } { N } \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s o u p } ( - \gamma _ { i } ( \alpha _ { i } ) ) } \\ & { \phantom { = } - \frac { 1 } { N } \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s o u p } \left( - \frac { A } { N } \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s o u p } ( C _ { i } ( \alpha _ { i } ) ) \right) } \\ & { \phantom { = } - \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } \left[ \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s o u p } _ { i } C _ { i } ( \alpha _ { i } ) \right] \phantom { = } } \\ & { = \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } \left[ \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s o u p } _ { i } C _ { i } ( \alpha _ { i } ) \right] \phantom { = } } \\ & { \phantom { = } - G _ { i } \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } \left[ \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } C _ { i } ( \alpha _ { i } ) \right] \phantom { = } } \\ & { = - \sigma _ { i } \displaystyle \sum _ { i = 1 } ^ { N } \sum _ { 1 } ^ { N } \mathrm { s u p } _ { i } \left[ \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } ( C _ { i } ( \alpha _ { i } ) ) \right] } \\ & { - \sigma _ { i } \displaystyle Z _ { i } ( \alpha _ { i } ) - Z _ { i } ( \alpha _ { i } - \lambda ) \mathrm { s u p } _ { i } C _ { i } ( \alpha _ { i } ) \mathrm { s u b } } \\ & { \phantom { = } - \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } \left[ \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } + \mathrm { s u p } _ { i } C _ { i } ( \alpha _ { i } ) \right] } \\ & { = \displaystyle \frac { 1 } { N } \displaystyle \sum _ { i = 1 } ^ { N } \mathrm { s u p } _ { i } } \end{array}
$$  

This theorem states that the appropriate $G _ { m }$ can be selected in each round to make $Z _ { m }$ the smallest so that the training error  

![](images/33adec2c7ea7d3ad553e87bcff2bbf5b180548e70f97c58a8ba5826262b2157c.jpg)  
Fig. 2. Flowchart of AdaBoost-Transformer model.  

decreases the fastest. Fig. 2 shows the AdaBoost-Transformer flowchart.  

# III. DATA ANALYSIS AND EXPERIMENTAL DESIGN  

The study utilizes logging data from Tarim Oilfield, situated in the heart of the Tarim Basin, with geographical coordinates spanning $8 2 ^ { \circ } - 9 0 ^ { \circ } \mathrm { E }$ and $3 8 ^ { \circ } - 4 4 ^ { \circ } \mathbf { N }$ . Tarim Oilfield stands as one of the most significant areas for oil and gas exploration and development in China, boasting substantial oil and gas reserves. The chosen parameters for analysis included PE (photoelectric index), GR (gamma), AC (acoustic time difference), CNL (neutron), and DEN (density). The data were collected using logging tools, which are sensor devices placed in oil or geological drilling wells. These tools are designed to record downhole measurements along the borehole during or after drilling, providing crucial geophysical and geochemical information. The correlation between the logging curve data and core samples was established for lithological marking, dividing the lithology into mudstone and sandstone categories, with sandstone being the primary reservoir rock formation.  

We conducted essential data preprocessing to optimize our model’s performance in both training and testing. This included handling missing values, detecting outliers, and normalization. Initially, our focus was on identifying and addressing missing values by filling gaps with the mean of respective features to ensure data integrity, mitigating detrimental effects on model training caused by missing data. In the outlier detection phase, we utilized the interquartile range (IQR) technique to identify potential outliers, ensuring robustness when our model encountered noise or exceptional situations in real-world scenarios. Finally, we performed normalization using the min–max method to scale data within the range of 0–1, enhancing training effectiveness and avoiding instability from significant feature value differences. Following data preprocessing, we used normalized features for model training and evaluated performance using a test set. These steps ensure that our model exhibits robust generalization and resilience in real-world environments, providing a reliable foundation for subsequent analysis and prediction tasks.  

The model training begins by preparing a dataset with known lithological labels, extracting features from logging curves for machine learning. Features undergo min–max normalization, scaling values between 0 and 1. The dataset consists of two sets, each with 5000 subsets (totaling 25 000 logging data points and 5000 lithological labels). It is split into a training set $( 8 0 \% )$ and a test set $( 2 0 \% )$ , with 4000 datasets for training and 1000 for testing. The training set is used for machine learning model training, while the validation set aids in parameter tuning and performance evaluation.  

TABLE I MODEL PARAMETERS   


<html><body><table><tr><td>Model</td><td>Parameter</td><td>Value</td></tr><tr><td>Adaboost- Transformer</td><td>Number of base classifiers</td><td>50</td></tr><tr><td></td><td>Adaboost algorithm learning rate</td><td>0.001</td></tr><tr><td></td><td>Adaboost maximum iterations</td><td>100</td></tr><tr><td></td><td>Transformer activation function</td><td>ReLU</td></tr><tr><td></td><td>Transformer learning_rate</td><td>0.001</td></tr><tr><td></td><td>Transformer optimizer</td><td>Adam</td></tr><tr><td></td><td>Transformer maximum iterations</td><td>100</td></tr><tr><td></td><td>The hidden dimensions of the</td><td>2048</td></tr><tr><td></td><td>feedforward network</td><td></td></tr><tr><td>Transformer</td><td>Transformer Dropout</td><td>0.3</td></tr><tr><td rowspan="6"></td><td>Activation function</td><td></td></tr><tr><td></td><td>ReLU</td></tr><tr><td>Learning_rate</td><td>0.001</td></tr><tr><td>Optimizer Maximum iterations</td><td>Adam</td></tr><tr><td>The hidden dimensions of the</td><td>100 2048</td></tr><tr><td>feedforward network</td><td></td></tr><tr><td>RF</td><td>Dropout</td><td>0.3</td></tr><tr><td rowspan="3"></td><td>Number of base learners</td><td>50</td></tr><tr><td>Max depth</td><td>10</td></tr><tr><td>Min samples split</td><td>10</td></tr></table></body></html>  

# IV. ANALYSIS OF RESULTS  

This letter introduces accuracy, precision, and recall as evaluation indicators for assessing the model [14]. These metrics are commonly used to measure the performance and effectiveness of classification models, offering insights into different aspects of the model’s predictions and error types. In addition, the confusion matrix is introduced as a statistical table used to measure the classification model performance. It provides a detailed understanding of prediction results, allowing evaluation of accuracy across different categories and identification of error types. Table I displays the model’s parameter settings in this document.  

The lithology prediction results and the confusion matrix of the machine learning model are illustrated in Figs. 3 and 4.  

![](images/0cb346ad3cc8fded972410f9529428896f04ecf7d8a0f34138bd43a23ce31c11.jpg)  
Fig. 3. Lithology prediction results and confusion matrix of the first data.  

![](images/925a86e90af2bbfecbb128a297c3079814bb69678388d6840c86395c10304508.jpg)  
Fig. 4. Lithology prediction results and confusion matrix of the second data.  

These visual representations provide an intuitive analysis of the model’s performance. The Adaboost-Transformer model achieves a $9 4 . 7 0 \%$ accuracy rate, exceeding that of the standalone Transformer model. Its high accuracy in binary classification for mudstone and sandstone demonstrates the model’s ability to correctly identify positive and negative samples, thereby reducing false predictions. By leveraging multiple Transformer models, the Adaboost-Transformer model enhances the overall classification accuracy. The Transformer, serving as a weak classifier, effectively manages high-dimensional data, addressing overfitting issues and delivering precise predictions.  

In the confusion matrix, the Adaboost-Transformer model shows higher accuracy and recall compared to traditional Transformer models. This increased accuracy and recall indicate more reliable prediction results and a lower risk of missing positive cases, respectively. High precision and recall represent a balanced prediction, signifying superior model performance and providing accurate, comprehensive results. Transformer models demonstrate greater accuracy than RF models, highlighting their proficiency in modeling sequential data and capturing temporal relationships in well logging data. Conversely, RF models, being tree-based, exhibit a relatively weaker capability in handling temporal sequences.  

# V. CONCLUSION  

Lithology prediction is a critical aspect of oil and gas exploration and development, playing a significant role in determining reservoir types, distribution, and characteristics, as well as in assessing reserve potential and reservoir effectiveness. Our hybrid model, tailored for well logging test data, showcased remarkable lithology identification accuracy rates of $9 5 . 2 0 \%$ and $9 5 . 5 0 \%$ in two datasets. In contrast, the accuracy rates for the standalone Transformer model and the RF model were lower at $9 1 . 5 0 \%$ , $9 1 . 1 0 \%$ , $8 8 . 8 0 \%$ , and $8 7 . 1 0 \%$ .  

In conclusion, the Adaboost-Transformer model’s lithology prediction results indicate that a combination of multiple Transformers significantly enhances the prediction accuracy. By assigning weights to these Transformers, the model effectively captures correlations and nonlinear relationships in complex classification scenarios. The iterative Adaboost algorithm reinforces samples that are frequently misclassified, continuously improving lithology prediction accuracy. This process emphasizes challenging samples in the subsequent decision trees, thereby enhancing the overall prediction accuracy.  

# REFERENCES  

[1] D. Joshi et al., “Prediction of sonic log and correlation of lithology by comparing geophysical well log data using machine learning principles,” GeoJournal, vol. 21, no. 2, pp. 47–68, 2021.   
[2] Q. Ren, H. Zhang, D. Zhang, and X. Zhao, “Lithology identification using principal component analysis and particle swarm optimization fuzzy decision tree,” J. Petroleum Sci. Eng., vol. 220, Jan. 2023, Art. no. 111233.   
[3] A. Al-Anazi and I. D. Gates, “On the capability of support vector machines to classify lithology from well logs,” Natural Resour. Res., vol. 19, no. 2, pp. 125–139, Jun. 2010.   
[4] V. R. C. Leite, P. M. C. Silva, M. Gattass, and A. C. Silva, “Analysis of ensemble methods applied to lithology classification from well logs,” in Proc. 13th Int. Congr. Brazilian Geophys. Soc. (EXPOGEF), 2013, pp. 949–952.   
[5] D. Bosch, J. Ledo, and P. Queralt, “Fuzzy logic determination of lithologies from well log data: Application to the KTB project data set (Germany),” Surv. Geophys., vol. 34, no. 2, pp. 413–439, 2013.   
[6] T. Horrocks, E. J. Holden, and D. Wedge, “Evaluation of automated lithology classification architectures using highly-sampled wireline logs for coal exploration,” Comput. Geosci., vol. 83, no. 1, pp. 209–218, 2015.   
[7] M. A. Sebtosheikh and A. Salehi, “Lithology prediction by support vector classifiers using inverted seismic attributes data and petrophysical logs as a new approach and investigation of training data set size effect on its performance in a heterogeneous carbonate reservoir,” J. Petroleum Sci. Eng., vol. 134, pp. 143–149, Oct. 2015.   
[8] H. Yang, H. Pan, H. Ma, A. A. Konaté, J. Yao, and B. Guo, “Performance of the synergetic wavelet transform and modified K-means clustering in lithology classification using nuclear log,” J. Petroleum Sci. Eng., vol. 144, no. 1, pp. 1–9, 2016.   
[9] S. Ghosh, R. Chatterjee, and P. Shanker, “Estimation of ash, moisture content and detection of coal lithofacies from well logs using regression and artificial neural network modelling,” Fuel, vol. 177, no. 2, pp. 279–287, 2016.   
[10] F. Wang, J. Cheng, W. Liu, and H. Liu, “Additive margin softmax for face verification,” IEEE Signal Process. Lett., vol. 25, no. 7, pp. 926–930, Jul. 2018.   
[11] W. Li, F. Qi, M. Tang, and Z. Yu, “Bidirectional LSTM with self-attention mechanism and multi-channel features for sentiment classification,” Neurocomputing, vol. 387, pp. 63–77, Apr. 2020.   
[12] P. Baldi and P. Sadowski, “The dropout learning algorithm,” Artif. Intell., vol. 210, pp. 78–122, May 2014.   
[13] H. P. Vinutha, B. Poornima, and B. M. Sagar, “Detection of outliers using interquartile range technique from intrusion dataset,” in Proc. Inf. Decis. Sci., 6th Int. Conf. (FICTA). Singapore: Springer, 2018, pp. 511–518.   
[14] B. Juba and H. S. Le, “Precision-recall versus accuracy and the role of large data sets,” in Proc. AAAI Conf. Artif. Intell., 2019, vol. 33, no. 1, pp. 4039–4048.  