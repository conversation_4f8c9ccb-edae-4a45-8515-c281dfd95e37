# ResGAT: A Residual Graph Attention Network for Lithology Identification  

<PERSON><PERSON> , <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON> , and <PERSON><PERSON><PERSON>bstract— Lithology identification is crucial for oil and gas exploration and reservoir evaluation, involving the analysis of physical and chemical characteristics of geological samples through well-logging data. This process requires understanding the complex nonlinear relationships between logging parameters and lithology. Recently, graph neural networks have gained prominence for their ability to uncover hidden relationships among samples, enhancing lithology identification. However, the imbalanced distribution of logging data often leads to incorrect interclass connections in logging graphs, which can skew feature aggregation and reduce prediction accuracy. To address this issue, this letter introduces the residual graph attention network (ResGAT), which integrates the residual information of well-logging data into the graph network based on graph relationships, adds residual connections to mitigate the impact of interclass edges, and enhances the weight of original information. To authentically assess the model’s practical effectiveness, we, respectively, conducted cross-well predictions in completely isolated well sets in oil fields in Daqing, China, and Kansas, USA. Compared to conventional GAT and GCN models, our proposed method achieves higher identification accuracy and significantly improves prediction accuracy for minority classes.  

Index Terms— Graph attention network, graph structure, lithology identification, residual connection.  

# I. INTRODUCTION  

WiInTdHu trhye, cwoenltli-nlougoguis  tveclohpnomleongt  f  ae  etsrsoelnetuiaml exploration and development method, is crucial for obtaining underground reservoir information. As conventional resources become depleted, the industry gradually shifts its focus  

Received 2 June 2024; revised 14 October 2024; accepted 6 November 2024. Date of publication 11 November 2024; date of current version 21 November 2024. This work was supported in part by the S&T Program of Hebei under Grant 246Z1817G; in part by the Natural Science Foundation of Xinjiang Uygur Autonomous Region, China, under Grant 2022D01A59; in part by the Key Research Foundation of Integration of Industry and Education and the Development of New Business Studies Research Center, Xinjiang University of Science and Technology, under Grant 2022-KYZD02; in part by Xinjiang College of Science and Technology Research Fund Key Talent Development Program under Grant 2024-GGYX02; and in part by the Innovation Capability Improvement Plan Project of Hebei Province under Grant 22567637H. (Corresponding author: Xianshan Li.)  

Fengda Zhao is with the School of Information Science and Engineering, Key Laboratory for Software Engineering of Hebei Province, Yanshan University, Qinhuangdao 066004, China, and also with the School of Information Science and Engineering, Xinjiang University of Science and Technology, Korla 841000, China.  

Zihan Zhou and Haobing Zhai are with the School of Information Science and Engineering, Yanshan University, Qinhuangdao 066004, China.  

Pengwei Zhang is with the School of Information Science and Engineering, Yanshan University, Qinhuangdao 066004, China, and also with the School of Information Science and Engineering, Xinjiang University of Science and Technology, Korla 841000, China.  

Xianshan Li is with the School of Information Science and Engineering, Key Laboratory for Software Engineering of Hebei Province, Yanshan University, Qinhuangdao 066004, China (e-mail: <EMAIL>). Digital Object Identifier 10.1109/LGRS.2024.3495976  

to unconventional reservoirs. In unconventional reservoirs, well logs can provide higher vertical resolution and capture microscale formation changes, making them a crucial tool for evaluating reservoir lithology [1], [2], [3]. Well-logging curves are physical parameters measured in the wellbore by logging tools, including natural gamma radiation, sonic velocity, resistivity, and so on. These curves reflect the formation characteristics at different depths in the wellbore. In recent years, machine learning and deep learning technologies have played a critical role in analyzing well-log curves, helping geological engineers more accurately predict lithology information in unconventional reservoirs. Owing to the scarcity of well-logging data, many researchers have focused on various SVM algorithm variants and ensemble learning models to better extract features [4], [5], [6]. The spatial sequential arrangement of well-logging data has led some scholars to draw analogies to the sequence of text, prompting attempts to employ models like the RNN and LSTM for prediction purposes [7], [8], [9].  

Although these models have made substantial progress in utilizing well-logging sample features, most overlook the potential interconnections between samples, leading to the comprehensive utilization of well-logging data not reaching the ideal level. The introduction of graph structures not only improves the utilization efficiency of well-logging data but also reveals the complex interdependencies between samples to a certain extent, thereby enhancing the accuracy and efficiency of lithology identification [10], [11], [12]. To alleviate the common class imbalance problem in well-logging and the introduction of a few wrong edges when constructing welllogging graphs, this letter proposes a residual graph attention network (ResGAT). We construct well-logging graphs based on depth distance and physical features and utilize graph attention networks to extract well-logging features. Simultaneously, based on the existing well-logging graphs, reasonable neighbor edge weights for each sample are calculated according to the attention heads in each network layer. After introducing the residual mechanism, the fusion of original features during the node aggregation stage can successfully enhance the feature representation of nodes, thereby effectively identifying the lithology of minority classes.  

# II. METHODOLOGY  

# A. Construction of Logging Graphs  

We treat each well-logging sample as a node and comprehensively consider the connections of nodes in terms of geographical distance and logging curves. This letter presents a method for constructing well-log graphs based on spatial relationships and feature similarity, similar to the construction strategy by Lu et al. [11] and Geng et al. [13]. We construct separate graphs using samples from the training set and the test set, respectively, and use them in two distinct phases. Connections are established for samples within the same well based on depth if the distance is less than the threshold $\Delta D$ . Based on the feature, unlike Lu et al. [11], this study employs the $K$ -nearest neighbors algorithm to identify the $C$ nearest neighbors for each sample node across all wells in the training and test sets, aiming to minimize the introduction of interclass edges. Furthermore, cosine similarity focuses more on the overall similarity of different features than numerical differences, so it demonstrates greater robustness when dealing with outliers in complex geological environments. Many studies in the well-logging field have also adopted cosine similarity as a feature similarity measure and achieved promising results [14]. Therefore, we use cosine similarity as the evaluation metric for the similarity of features, where a higher cosine similarity score between two vectors indicates greater similarity. The graph construction process for training is shown in the Graph Construction section of Fig. 1. The cosine similarity $s ( \vec { x } _ { u } , \vec { x } _ { v } )$ between two nodes $u$ and $\upsilon$ is defined by the following equation:  

$$
s \left( \vec { x } _ { u } , \vec { x } _ { v } \right) = \frac { \sum _ { k = 1 } ^ { d } \vec { x } _ { u } ^ { k } \times \vec { x } _ { v } ^ { k } } { \sqrt { \sum _ { k = 1 } ^ { d } \left( \vec { x } _ { u } ^ { k } \right) ^ { 2 } } \times \sqrt { \sum _ { k = 1 } ^ { d } \left( \vec { x } _ { v } ^ { k } \right) ^ { 2 } } }
$$  

where $\vec { x } _ { u } \in \mathbb { R } ^ { d }$ represents the feature vector of node $u$ . The set of feature vectors for all data, $\mathbf { X } ~ = ~ [ \vec { x } _ { 1 } , \vec { x } _ { 2 } , \dots , \vec { x } _ { n } ] ^ { \mathrm { T } }$ forms an $n \times d$ feature matrix, where $n$ is the total number of nodes and $d$ is the dimensionality of the features.  

# B. ResGAT Model  

Using the above algorithm, we obtained a well-logging graph with the same edge weights. Nevertheless, in reality, the contribution of different neighbors to a node varies. To determine the contribution level of each neighbor, the graph attention network employs the attention mechanism that dynamically adjusts the attention coefficient $\boldsymbol { e _ { u v } }$ between two nodes $u$ and $\upsilon$ during the training process. The coefficient is set to negative infinity if there is no connection between two nodes in the well-logging graph. We normalize the attention coefficients of all neighbors of node $u$ to obtain the final weight $\alpha _ { u v }$ , as defined in the following equations:  

$$
\begin{array} { r l } & { e _ { u v } = \sigma \left( a \left( \mathbf { W } \vec { x } _ { u } , \mathbf { W } \vec { x } _ { v } \right) \right) } \\ & { \alpha _ { u v } = \operatorname { s o f t m a x } _ { v } ( e _ { u v } ) = \cfrac { \exp ( e _ { u v } ) } { \sum _ { v ^ { \prime } \in \mathcal { N } _ { u } } \exp ( e _ { u v ^ { \prime } } ) } } \end{array}
$$  

where $\mathbf { W }$ is a learnable linear transformation matrix and $a$ is a learnable 1-D feature weight vector shared by all nodes. softmaxv represents the normalization across all neighbor nodes $\upsilon$ for a fixed node $u$ , where $\mathcal { N } _ { u }$ denotes the neighborhood of $u$ .  

The initial well-logging graph only contains each node’s 1-hop neighborhood. To better capture global information, it is necessary to go through multiple layers of graph convolution. With each layer of the graph attention passed, the node aggregates information from one more hop of neighbors. For node $u$ with its neighborhood $\mathcal { N } _ { u }$ , the attention coefficients $\alpha _ { u v }$ for neighboring node $\boldsymbol { v }$ are computed separately for each attention head in each layer. The features $\vec { h } _ { v } ^ { i - \mathrm { i } }$ of the neighbor nodes from layer $i - 1$ are linearly transformed using the matrix $\mathbf { W }$ and then multiplied by the attention coefficients $\alpha _ { u v } ^ { j }$ for the $j$ th head. These outputs are accumulated and undergo a nonlinear activation to yield the feature $h _ { u } ^ { i j }$ for the $j$ th attention head in the $i$ th layer. The process is described in the following equation:  

$$
\vec { h } _ { u } ^ { i j } = \sigma \left( \sum _ { v \in \mathcal { N } _ { u } } \alpha _ { u v } ^ { j } \mathbf { W } ^ { j } \vec { h } _ { v } ^ { i - 1 } \right) .
$$  

Following the aggregation of features from all attention heads and subsequently integrating them with the node’s original features $\vec { x } _ { v }$ through residual connections, we derive the final lithological features, denoted as $\vec { z }$ , depicted as the following equations:  

$$
\begin{array} { r l } & { \vec { h } _ { u } ^ { i } = \| _ { j = 1 } ^ { K } \sigma \left( \displaystyle \sum _ { v \in \mathcal { N } _ { u } } \alpha _ { u v } ^ { j } \mathbf { W } ^ { j } \vec { h } _ { v } ^ { i - 1 } \right) } \\ & { \vec { z } _ { u } = \vec { h } _ { u } ^ { i } = \vec { h } _ { \mathcal { N } _ { u } } ^ { i } \| \mathbf { W } \vec { x } _ { u } } \end{array}
$$  

where $\vec { h } _ { \mathcal { N } _ { u } } ^ { i }$ represents the features of node $u$ obtained after aggregating information from its neighborhood $\mathcal { N } _ { u }$ at the $i$ th layer, $K$ denotes the number of attention heads, $W ^ { j }$ represents the weight matrix for the $j$ th attention head, and $\vec { x } _ { u }$ is the original feature of node $u$ . The specific aggregation method is described in Algorithm 1.  

# Algorithm 1 Node Aggregation  

Input: Well-logging adjacency matrix $G$ ; Well-logging feature vectors $\hat { \vec { x } } _ { u }$ , $\forall u \in \{ 1 , \ldots , n \}$ ; Number of graph attention layers $L$ ; Number of attention heads $K$ ; Trainable shared linear transformation matrix $\mathbf { W } ^ { j }$ , $\forall j \in \{ 1 , \ldots , K \}$ ; Trainable shared feature weight vector $a$ ; Nonlinear transformation $\sigma$ ; Output: Aggregated new features $\vec { z } _ { u }$ , $\forall u \in \{ 1 , \ldots , n \}$ ; 1 $\vec { h } _ { u } ^ { 0 } \dot {  } \vec { x } _ { u }$ ; 2 for $i = 1 , 2 , \dots , L$ do 3 for $j = 1 , 2 , \dots , K$ do 4 ${ \boldsymbol { e } } _ { u v } ^ { j } = \mathrm { L e a k y R e L U } ( { a [ { \mathbf { W } } ^ { j } \vec { h } _ { u } ^ { i - 1 } \ \lVert \ { \mathbf { W } } ^ { j } \vec { h } _ { v } ^ { i - 1 } ] } ) ;$ ; 5 $\begin{array} { r } { \alpha _ { u v } ^ { j } = \mathrm { s o f t m a x } ( e _ { u v } ^ { j } ) = \frac { \exp ( e _ { u v } ^ { j } ) } { \sum _ { v ^ { \prime } \in \mathcal { N } _ { u } } \exp ( e _ { u v ^ { \prime } } ^ { j } ) } } \end{array}$ ? 6 h⃗iu = h⃗iu ∥σ   v∈ u αujvW j h⃗iv−1; 7 end 8 z⃗iu = h⃗iu∥Wx⃗u; 9 end  

The lithology identification model consists of three parts. The first part retrieves logging features from samples and constructs a well-logging graph as the input. The second part is the feature extraction module, which comprises $K$ parallel GAT modules that extract features; each GAT module performs feature aggregation on the nodes in the well-log graph based on the given adjacency matrices and feature matrices to obtain new output features. The outputs of these $K$ modules are concatenated with the original features to produce the final result of this layer. After multiple layers of feature aggregation, each sample node incorporates more neighboring features, which are then passed to the class module in the third part. In the classification module, attention weights are added to the features through dimensional changes, and finally, the ultimate lithology is differentiated through a linear layer. For some relevant lithologies, we have displayed real lithology images. The ResGAT model diagram is shown in Fig. 1.  

![](images/8a67368f6b146517ab0ed533befc855385f0597be2571ec3ffeb204a8bbdd312.jpg)  
Fig. 1. Architecture of the proposed ResGAT.  

# III. EXPERIMENT  

# A. Dataset  

To assess the effectiveness of this method, we conducted validation experiments on two datasets: one is the publicly available dataset of well-logging data from the Hugoton and Panoma gas fields in Kansas, USA, referred to as DA, and the other is a proprietary dataset from a particular oil field in Daqing, referred to as DB.  

The DA dataset contains well-log samples from ten wells, comprising 4062 samples across nine categories. All samples range in depth from 2573.5 to $3 1 6 0 . 5 ~ \mathrm { ~ m ~ }$ with a $0 . 5 – \mathrm { m }$ sampling interval, and each sample includes six logging features. These logging parameters include gamma ray (GR), resistivity logging (RL), photoelectric effect (PE), average neutron density porosity (PHIND), nonmarine–marine indicator (NM_M), neutron-density porosity difference (DeltaPHI), and relative position (RP). Further geological details can be referred to in [15]. The DB dataset contains 1628 well-log entries from 49 geographically adjacent wells, covering six categories. All samples range from 1212.1571 to $2 5 2 0 . 9 7 2 1 \mathrm { ~ m ~ }$ with a variable sampling interval. Each sample comprises nine logging features, including spontaneous potential (SP), photoelectric absorption factor (PE), GRs, formation resistivity (AT), acoustic compressional wave velocity (ac), neutron porosity (CNL), bulk density (DEN), porosity index (POR), and shale index (Ish). Due to the significant size differences and numerical disparities between features, it is necessary to standardize the data. The quantities and proportions of each category are shown in Table I. Due to the differing magnitudes and substantial numerical differences between features, it is necessary to normalize the data.  

TABLE I LITHOLOGY ON DA AND DB   


<html><body><table><tr><td>Dataset</td><td>Lithology</td><td>Label</td><td>Size</td><td>Proportion (%)</td></tr><tr><td rowspan="9">DA</td><td>Nonmarine coarse siltstone</td><td>CSiS</td><td>897</td><td>22.08</td></tr><tr><td>Nonmarine fine siltstone</td><td>FSiS</td><td>769</td><td>18.93</td></tr><tr><td>Packstone-grainstone</td><td>PS</td><td>710</td><td>17.48</td></tr><tr><td>Wackestone</td><td>WS</td><td>589</td><td>14.50</td></tr><tr><td>Nonmarine sandstone</td><td>Ss</td><td>279</td><td>6.87</td></tr><tr><td>Mudstone</td><td>MS</td><td>266</td><td>6.55</td></tr><tr><td>Marine siltstone and shale</td><td>SiSh</td><td>249</td><td>6.13</td></tr><tr><td>Phylloid-algal bafflestone</td><td>BS</td><td>179</td><td>4.40</td></tr><tr><td>Dolomite</td><td>D</td><td>124</td><td>3.06</td></tr><tr><td rowspan="6">DB</td><td>Silty Mudstone</td><td>SS</td><td>555</td><td>34.09</td></tr><tr><td>Heterogeneous Sandstone</td><td>HS</td><td>496</td><td>30.47</td></tr><tr><td></td><td></td><td></td><td></td></tr><tr><td>Hark Muneous Sandstone</td><td>S</td><td>346</td><td>21.25</td></tr><tr><td>Black Shale</td><td>BS</td><td>82</td><td>5.04</td></tr><tr><td>Tuff</td><td>T</td><td>45</td><td>2.76</td></tr></table></body></html>  

# B. Results  

We conducted ablation studies by adding and removing residual connection modules in GCN [12] and GAT [12] to validate the effectiveness of original feature aggregation and the residual mechanism. Given the rigor of the experiments, it was necessary to compare the same datasets against previous models. Yadigar mixed well-logging data from the DA dataset, using $80 \%$ as the training set and $20 \%$ as the validation set for experiments, and we compared our proposed graph models against the results from their experiments. We chose accuracy, precision, recall, and F1-score as evaluation metrics for model prediction in machine learning, focusing on accuracy and F1-score [13], [15]. Accuracy intuitively reflects the overall prediction results, while the F1-score provides a more objective evaluation of the model’s performance in the case of imbalanced lithology classes. In addition, to highlight the advantages of graph models, we compared several methods suitable for processing sequential data, such as the LSTM, GRU, and Transformer. We followed the same approach for the DB dataset, conducting related experiments. The results are shown in Table II. Compared to the study by Yadigar et al. [15], our proposed model offers higher prediction accuracy and more excellent stability.  

TABLE II COMPARISON OF EXPERIMENTAL RESULTS $( \% )$ ACROSS DIFFERENT METHODS ON MIXED WELLS   


<html><body><table><tr><td>DataSet</td><td>Model</td><td>Accuracy</td><td>Precision</td><td>Recall</td><td>F1-Score</td></tr><tr><td rowspan="9">DA</td><td>RNN [15] LSTM[15]</td><td>57.38</td><td>55.52 60.85</td><td>54.86 61.66</td><td>52.88 66.23</td></tr><tr><td>SVM[16] 1D-CNN [15]</td><td>62.95 73.73</td><td>72.89</td><td>72.68</td><td>74.22</td></tr><tr><td>CatBoost</td><td>76.87 71.83</td><td>75.93 74.12</td><td>75.57 69.45</td><td>79.09 71.28</td></tr><tr><td>BiGRU Transformer</td><td>74.91 76.26</td><td>76.48 77.41</td><td>75.06 75.41</td><td>75.58 75.95</td></tr><tr><td>GCN[12] ResGCN</td><td>76.63</td><td>77.74</td><td>75.95</td><td>76.55</td></tr><tr><td>GAT[12]</td><td>78.23</td><td>77.76</td><td>77.33</td><td>77.30</td></tr><tr><td>ResGAT (ours)</td><td>77.37 79.33</td><td>79.40</td><td>76.30</td><td>77.42</td></tr><tr><td></td><td></td><td>80.06</td><td>77.22</td><td>79.33</td></tr><tr><td>RNN</td><td></td><td>77.91 76.69</td><td>73.50 69.24</td><td>67.91</td><td>69.88</td></tr><tr><td>LSTM SVM</td><td></td><td></td><td></td><td>65.94</td><td>67.27</td></tr><tr><td>CatBoost</td><td>78.83 76.69</td><td>73.71 73.47</td><td>68.53 70.86</td><td></td><td>69.50</td></tr><tr><td>1D-CNN</td><td>78.22</td><td>71.68</td><td>73.84</td><td></td><td>71.63</td></tr><tr><td>BiGRU</td><td>78.22</td><td>71.56</td><td></td><td></td><td>72.48</td></tr><tr><td>DB</td><td>Transformer</td><td></td><td></td><td>72.36</td><td>71.76</td></tr><tr><td>GCN</td><td></td><td>79.14</td><td>74.17</td><td>72.42</td><td>72.21</td></tr><tr><td></td><td>77.30</td><td>69.66</td><td></td><td>73.16</td><td>71.06</td></tr><tr><td>ResGCN</td><td>80.06</td><td>74.80</td><td>71.64</td><td></td><td>71.76</td></tr><tr><td>GAT ResGAT(ours)</td><td>79.14 81.29</td><td>74.17 75.08</td><td></td><td>75.10 75.82</td><td>72.05 74.27</td></tr></table></body></html>

The results from [15] and [16] are experimental outcomes reported in the references,whereas [12] details the performance of the model employed in that reference on this dataset.  

TABLE III COMPARISON OF EXPERIMENTAL RESULTS $( \% )$ FROM DIFFERENT METHODS ON CROSS-WELLS   


<html><body><table><tr><td>DataSet</td><td>Model</td><td>Accuracy</td><td>Precision</td><td>Recall</td><td>F1-Score</td></tr><tr><td rowspan="7">DA</td><td>RNN LSTM[16]</td><td>61.32 56.32</td><td>63.22 54.15</td><td>61.21 58.62</td><td>61.43 56.22</td></tr><tr><td>SVM [16] CatBoost</td><td>56.98</td><td>49.62</td><td>52.12</td><td>49.66</td></tr><tr><td>1D-CNN [16]</td><td>63.71 66.52</td><td>64.67 65.82</td><td>62.09 68.98</td><td>62.34 62.12</td></tr><tr><td>BiGRU Transformer</td><td>63.16</td><td>63.57</td><td>66.18</td><td>63.55</td></tr><tr><td></td><td>64.36</td><td>61.83</td><td>64.36</td><td>61.74</td></tr><tr><td>GCN[12] ResGCN</td><td>62.84 65.22</td><td>60.58</td><td>63.70</td><td>60.96</td></tr><tr><td></td><td></td><td>65.35</td><td>65.88 65.53</td><td>64.05</td></tr><tr><td rowspan="9">DB</td><td>GAT[12] ResGAT (ours)</td><td>63.27 67.28</td><td>60.90 66.90</td><td>68.73</td><td></td><td>61.79 65.52</td></tr><tr><td>RNN</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>LSTM</td><td>76.69</td><td>71.38</td><td>66.55</td><td></td><td>67.66</td></tr><tr><td>SVM</td><td>75.56</td><td>67.55</td><td></td><td>65.82</td><td>65.38</td></tr><tr><td>CatBoost</td><td>77.53</td><td>78.72</td><td>67.35</td><td></td><td>65.73</td></tr><tr><td>BiGRU</td><td>77.25</td><td>70.14</td><td>68.35</td><td></td><td>67.27</td></tr><tr><td>Transformer</td><td>76.12</td><td>69.69</td><td>68.66</td><td></td><td>67.66</td></tr><tr><td>1D-CNN</td><td>78.09 77.53</td><td>70.43</td><td>75.10</td><td></td><td>72.05</td></tr><tr><td>GCN</td><td>77.25</td><td></td><td>71.12 70.37</td><td>66.66 68.74</td><td>76.06</td></tr><tr><td>ResGCN</td><td></td><td>80.34</td><td>73.33</td><td>72.03</td><td>69.36</td></tr><tr><td>GAT</td><td>77.81</td><td></td><td></td><td></td><td>72.13</td></tr><tr><td>ResGAT (ours)</td><td></td><td>71.27</td><td>70.38</td><td></td><td>70.09</td></tr><tr><td></td><td>80.62</td><td>75.13</td><td></td><td>72.55</td><td>72.55</td></tr></table></body></html>  

In practical well-logging exploration, it is necessary to predict the lithology of cross-wells in the well area. Therefore, to effectively evaluate the models on an application level, we divided the two datasets into training and testing sets isolated by well intervals. For the DA dataset, we selected 3139 logging samples from eight wells as the training set and 923 logging samples from two adjacent cross-wells as the testing set. For the DB dataset, we selected 1272 logging samples from eight wells as the training set and 356 logging samples from two adjacent cross-wells as the testing set. The experimental results are shown in Table III.  

Comparing the two experimental environments, it was found that compared to cross-well prediction, the prediction accuracy of the DA mixed well significantly improved. At the same time, the results of DB were roughly equivalent, indicating a certain robustness of the model. Although machine learning models such as SVM and CatBoost have strong generalization capabilities and stable predictions, they are overly sensitive to outliers, which leads to high requirements for the quality of well-logging data. According to the results from Tables II and III, these models are less capable than deep learning models. Models such as the LSTM, GRU, and Transformer, suitable for processing sequential data, can link well-logging data in terms of depth during prediction. Nevertheless, these connections’ randomness makes the models unstable and uninterpretable, which hinders their practical application.  

The results from Tables II and III indicate that graph-based methods are superior to machine learning and recurrent neural network approaches. This suggests that exploring hidden relationships between similar logging samples can make more efficient use of limited available data, thereby improving the accuracy of lithology identification. The residual mechanism has improved model performance compared to conventional GCN and GAT. The proposed method performed best on both DA and DB datasets, with accuracy and F1-score on DA increasing by $4 . 4 2 \%$ and $3 . 6 5 \%$ , respectively, and on DB by $2 . 8 1 \%$ and $2 . 4 6 \%$ , respectively. To demonstrate the practical value of the model in production, our subsequent discussion will primarily focus on the results of cross-well predictions.  

# C. Discussion  

To more intuitively showcase the lithology identification effects at various depths, Fig. 2 shows the comparison profiles of actual and predicted lithologies for Well A in the DA test set and Well B in the DB test set. In Fig. 2(a), the predicted results closely match the actual lithologies, while in Fig. 2(b), between depths of 3025 and $3 0 5 0 \mathrm { ~ m ~ }$ , the model tends to misclassify dolomite (D) as Phylloid-algal bafflestone (BS). The results suggest that at greater depths, due to higher formation pressures, different rocks may exhibit similar compaction characteristics, leading to similarities in parameters like acoustic delay and resistivity, thereby impacting lithology identification.  

During the graph construction process, noisy edges may interfere with the prediction of minority classes within neighboring major sample categories, leading to misclassification of these minority classes. Introducing the residual mechanism can reduce the feature distortion caused by interclass edges during node aggregation, thereby improving the prediction accuracy for minority classes. As can be seen in the lithology accuracy comparison chart in Fig. 3, compared to conventional GAT, most of the minority classes in Table I have seen substantial accuracy improvements: In the DA, SS improved by $12 \%$ , MS by $10 \%$ , BS by $13 \%$ , and $\mathrm { ~ D ~ }$ by $5 \%$ . In the DB, $\mathrm { H g } \mathrm { S }$ improved by $1 9 \%$ . The results indicate that incorporating residual connections in graph models can indeed effectively enhance the classification accuracy of minority classes.  

![](images/22a0d29cde9883b1748e1911cfa5e830c88463e51489629744574fdcecbeb12a.jpg)  
Fig. 2. Lithology profile diagram. (a) Well A. (b) Well B.  

![](images/81eda7704158d369b6cd317a9091d819928b878b11293cbe36da6861638efaa6.jpg)  
Fig. 3. Accuracy of each category on two datasets. (a) DA and (b) DB.  

TABLE IV COMPARISON OF DIFFERENT GRAPH CONSTRUCTION METHODS   


<html><body><table><tr><td>DataSet</td><td>Model</td><td>Accuracy</td><td>Precision</td><td>Recall</td><td>F1-Score</td></tr><tr><td></td><td>ResGAT-DA-feature</td><td>61.43</td><td>61.28</td><td>57.12</td><td>56.57</td></tr><tr><td>DA</td><td>ResGAT-DA-depth</td><td>61.32</td><td>57.41</td><td>52.71</td><td>53.89</td></tr><tr><td></td><td>ResGAT-DA-f-d</td><td>67.28</td><td>66.90</td><td>68.73</td><td>65.52</td></tr><tr><td></td><td>ResGAT-DB-feature</td><td>76.12</td><td>67.71</td><td>66.04</td><td>65.66</td></tr><tr><td>DB</td><td>ResGAT-DB-depth</td><td>77.81</td><td>70.85</td><td>69.58</td><td>69.81</td></tr><tr><td></td><td>ResGAT-DB-f-d</td><td>80.62</td><td>75.13</td><td>72.55</td><td>72.55</td></tr></table></body></html>  

To study the quality of graph construction, we conducted a comprehensive comparison experiment on depth and features, with the results shown in Table IV. In practical lithology analysis, many well-logging samples with similar lithologies but belonging to different categories are close to each other in the feature space. In this case, the feature similarity used to establish connections makes two classes closer in feature space, negatively affecting classification. Compared with using feature similarity, depth distance can avoid this situation to a certain extent. When based solely on depth, the well-logging graph will form a local chain, disregarding the broader linkages suggested by similarities in physical features. The results show that the well-logging graph constructed comprehensively considering both situations is of the highest quality.  

# IV. CONCLUSION  

We propose a graph-based lithology identification method, ResGAT, which utilizes residual connections to enhance the original information’s weight and reduce the influence of interclass edges. When tested on two well-logging datasets, this method achieved higher classification accuracy than other approaches, particularly for minority classes. The accuracy of Dark Mudstone, which constitutes less than $7 \%$ of the samples, increased by $1 9 \%$ . For Nonmarine sandstone, Mudstone, and Black Shale, which make up less than $7 \%$ of the samples, the improvements were $12 \%$ , $10 \%$ , and $13 \%$ , respectively. This study highlights the effectiveness of graph neural networks in addressing class imbalances in well-logging data and underscores their potential for advancing lithology identification.  

# ACKNOWLEDGMENT  

The authors gratefully acknowledge the helpful comments and suggestions from the reviewers, which have significantly improved the letter.  

# REFERENCES  

[1] Z. Ye, S. Guo, D. Chen, H. Wang, and S. Li, “Drilling formation perception by supervised learning: Model evaluation and parameter analysis,” J. Natural Gas Sci. Eng., vol. 90, Jun. 2021, Art. no. 103923.   
[2] A. N. Corina and S. Hovda, “Automatic lithology prediction from well logging using kernel density estimation,” J. Petroleum Sci. Eng., vol. 170, pp. 664–674, Nov. 2018.   
[3] Z. Li, S. Deng, Y. Hong, and Z. Wei, “Logging lithology identification of unconventional reservoir based on hybrid model,” in Proc. Int. Conf. New Trends Comput. Intell. (NTCI), Nov. 2023, pp. 336–340.   
[4] Y. Xie, C. Zhu, W. Zhou, Z. Li, X. Liu, and M. Tu, “Evaluation of machine learning methods for formation lithology identification: A comparison tunin cesses and model performances,” J. Petroleum Sci. Eng., vol. 160, pp. 182–193, Jan. 2018.   
[5] Z. Li, S. Deng, Y. Hong, Z. Wei, and L. Cai, “A novel hybrid CNN–SVM method for lithology identification in shale reservoirs based on logging measurements,” J. Appl. Geophysics, vol. 223, Apr. 2024, Art. no. 105346.   
[6] M. Appiah-Twum, H. Jia, and W. Xu, “Assessing Landsat-9 in identifying lithology, using a hybrid metric-learning and SVM method against baseline algorithms: A case study of the west African craton,” in Proc. IEEE Int. Geosci. Remote Sens. Symp., vol. 45, Jul. 2023, pp. 1968–1971.   
[7] D. T. dos Santos, M. Roisenberg, and M. D. S. Nascimento, “Deep recurrent neural networks approach to sedimentary facies classification using well logs,” IEEE Geosci. Remote Sens. Lett., vol. 19, pp. 1–5, 2022.   
[8] L. Shan, Y. Liu, M. Tang, M. Yang, and X. Bai, “CNN-BiLSTM hybrid neural networks with attention mechanism for well log prediction,” J. Petroleum Sci. Eng., vol. 20 Oct. 2021, Art. no. 108838.   
[9] L. Zeng, W. Ren, and Shan “Attention-based bidirectional gated recurrent unit neural networks well logs prediction and lithology identification,” Neurocomputin 414, pp. 153–171, Nov. 2020.   
[10] R. Gong and Y. Yang, “Application of graph neural network in lithology recognition,” in Proc. 4th Int. Conf. Natural Lang. Process. (ICNLP), Mar. 2022, pp. 184–188.   
[11] G. Lu et al., “Lithology identification using graph neural network in continental shale oil reservoirs: A case study in Mahu Sag, Junggar basin, western China,” Mar. Petroleum Geol., vol. 150, Apr. 2023, Art. no. 106168.   
[12] C. Yuan, Y. Wu, Z. Li, H. Zhou, S. Chen, and Y. Kang, “Lithology identification adaptive feature aggregation under scarce labels,” J. Petroleum Sci. Eng., vol. 215, Aug. 2022, Art. no. 110540.   
[13] Z. Geng et al., “Channel attention-based static-dynamic graph convolutional network for lithology identification with scarce labels,” Geoenergy Sci. Eng., vol. 223, Apr. 2023, Art. no. 211526.   
[14] X. Wang, S. Yan Y. Wang, “Lithology identification using an optimized KNN clustering method based on entropy-weighed cosine distance in Mes strata of Gaoqing field, Jiyang depression,” J. Petroleum Sci. Eng., vol. 166, pp. 157–174, Jul. 2018.   
[15] Y. Imamverdiyev and L . Sukhostat, “Lithological facies classification using deep convolutional neural network,” J. Petroleum Sci. Eng., vol. 174, pp. 216–228, Mar. 2019.   
[16] F. Zhao, Y. Yang, J. Kang, and X. Li, “CE-SGAN: Classification enhancement semi-supervised generative adversarial network for lithology identification,” Geoenergy Sci. Eng., vol. 223, Apr. 2023, Art. no. 211562.  