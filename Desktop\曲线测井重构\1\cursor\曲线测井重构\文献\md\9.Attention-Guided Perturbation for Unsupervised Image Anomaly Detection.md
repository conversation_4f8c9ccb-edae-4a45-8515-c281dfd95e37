# Attention-Guided Perturbation for Unsupervised Image Anomaly Detection

Yuxuan <PERSON>,∗∗, Tingfeng Huanga,∗∗, Yuxuan $\mathbf { C a i } ^ { b }$ , Jingbo $\operatorname { X i a } ^ { a }$ , Rui ${ \mathrm { Y u } ^ { c } }$ , Jinhai <PERSON> , Xinwei $\mathrm { H e } ^ { a , * }$

aHuazhong Agricultural University, Wuhan 430070, China bHuazhong University of Science and Technology, Wuhan 430074, China cUniversity of Louisville, USA

# Abstract

Reconstruction-based methods have significantly advanced unsupervised image anomaly detection involving only normal training images. However, it has been proven that modern neural networks generally have a strong reconstruction capacity and often reconstruct both normal and abnormal samples well, thereby failing to spot anomaly regions by checking the reconstruction quality. To prevent well-reconstructed anomalies, one simple but effective strategy is to perturb normal samples and then map perturbed versions to normal ones. Yet it treats each spatial position equally, disregarding the fact that the foreground locations are inherently more important for reconstruction. Motivated by this, we present a simple yet effective reconstruction framework named Attention-Guided Perturbation Network (AGPNet), which learns to add perturbations guided with an attention mask during training. Specifically, it consists of two branches, i.e., a reconstruction branch and an auxiliary attention-based perturbation branch. The reconstruction branch learns to reconstruct normal samples, while the auxiliary one aims to produce attention masks to guide the noise perturbation process for normal samples. By doing so, we are expecting to synthesize hard yet more informative anomalies for training, which enable the reconstruction branch to learn important inherent normal patterns both comprehensively and efficiently. Extensive experiments are conducted on several popular benchmarks covering MVTec-AD, VisA, and MVTec3D, and show that AGPNet obtains leading anomaly detection results under few-shot, one-class, and multi-class setups.

# Key words:

Unsupervised Image Anomaly Detection, Reconstruction, Perturbation, Attention.

# 1. Introduction

Automatically detecting and localizing anomalies in images, i.e., visual anomaly detection (VAD), has extensive applications in manufacturing Bergmann et al. (2019); Defard et al. (2021). However, this task typically encounters the challenge of cold-start Zavrtanik et al. (2021) where gathering normal samples is easy, whereas acquiring anomalous samples is costly or infeasible, which prohibits supervised learning. Therefore, significant effort has been devoted to developing unsupervised anomaly detection (UAD) algorithms Bergmann et al. (2019); You et al. (2022); Bergmann et al. (2020); Zhang et al. (2023); Deng and Li (2022); Schlu¨ ter et al. (2022) that learn to model normality distribution with solely normal images during the training process. During inference, images that are far away from the normality distribution are regarded to be anomalous.

Among existing UAD works, reconstruction-based methods Zavrtanik et al. (2022); Liang et al. (2023) hold great promise and have been extensively studied. Generally, they assume that reconstruction networks exposed to normal-only training images can reconstruct normal ones well, but find difficulty in reconstructing abnormal ones. Thus, an anomaly map can be computed by analyzing the reconstruction error between the input and its reconstructed counterpart. As per this assumption, some methods attempt to reconstruct input images with popular generative models, eg, AutoEncoder Gong et al. (2019); Nguyen et al. (2019); Bergmann et al. (2019), GANs Sabokrou et al. (2018); Kim et al. (2020); Yan et al. (2021), or Difusion-based models He et al. (2024); Zhang et al. (2023). However, reconstructing raw pixel values usually fails when normal and anomalous regions share similar values You et al. (2022). Another direction is reconstructing features since they provide more contextual information than raw pixels You et al. (2022); Shi et al. (2021); You et al. (2022). With the availability of general-purpose large pretrained networks, there has been a growing interest in learning a reconstruction network to reconstruct discriminative features from models pretrained on large-scale datasets, e.g., ImageNet Deng et al. (2009).

Despite the outstanding performance, it has been commonly observed You et al. (2022); Zavrtanik et al. (2021); You et al. (2022) that reconstruction-based methods often encounter the “identity shortcut” issue, a phenomenon of reconstructing anomalies well, due to their uncontrollable generalizability. To alleviate this issue, many strategies Haselmann et al. (2018); Zhou et al. (2020); Zavrtanik et al. (2021) have been presented. One simple yet effective strategy is to perturb input to suppress the reconstruction of anomalies (Fig. 1(a)). For instance, AESc Collin and De Vleeschouwer (2021) introduces a stain perturbation model to corrupt the image with an ellipse shape of irregular edges. During training, they simply restore the corruption. SCADN Yan et al. (2021) generates fixed multi-scale striped masks to erase a part of regions in the input image and learn to reconstruct the missing information. DRAEM Zavrtanik et al. (2021) obtains promising results by recovering pseudo-anomaly disturbed normal images for representation. By perturbing the input, the reconstruction network is forced to learn invariant patterns for reconstruction. Evidently, an effective perturbation scheme is critical. However, prior works simply apply fixed or random masks to perturb input images indiscriminately, regardless of their specific contextual and semantic characteristics. In realworld industrial settings with diverse categories and varying shapes and sizes of foreground objects, such strategies easily induce the reconstruction model to overfit the irrelevant background, leading to suboptimal performance.

![](images/fa9e18076273400c967ad3fa48fdec25ea6da88dde430fd910ceb36ab964fb07.jpg)  
Figure 1: Comparisons between ours and existing perturbation strategies in reconstruction paradigm for anomaly detection. Compared with (a) existing networks adopting fixed or random perturbations, (b) we introduce to learn to guide the perturbation with sample-aware masks from the network and help to learn a better reconstruction model.

In this paper, we argue that it is conducive to learning a better reconstruction model by considering sample-specific content and structural priors for perturbation. To this end, we introduce a simple yet effective reconstruction framework named AGPNet (see Fig. 2) for unsupervised anomaly detection. The core idea behind our framework is to perturb the normal input according to the importance of each location during training. This approach enables the reconstruction network to pay more attention to those important areas, thereby learning invariant normal patterns more compactly and efficiently. To achieve this goal, we carefully design an auxiliary branch that generates attention masks for the noise-adding process ((Fig. 1(b))). Specifically, the auxiliary branch combines two important cues to derive the final attention masks. 1) One is the attention maps from the pretrained feature extraction layers, which provide strong prior on foreground pixels and important localization cues for the reconstruction network. For instance, industrial categories like screws or toothbrushes typically have small foreground areas while textures may have complex structures occupying the whole image. Thus, it would be more efficient and beneficial to pay attention to these important locations. 2) The second cue is the attention maps from the decoder layers of the main branch, which indicates the importance of each location for the reconstruction task. By incorporating this cue, the auxiliary branch dynamically emulates the role of the reconstruction network, thereby preventing training stagnation by perturbing important localizations more aggressively as training progresses. Finally, we perturb the normal input at the main branch with simple Gaussian noise weighted by the final attention masks. This process further helps the framework reason about invariant normal patterns at both low and high levels, ultimately boosting the reconstruction networks to produce discrepancies regarding anomaly samples.

Our framework has several desirable merits. First, it identifies anomalies at high accuracy under both one-class and multi-class settings. By perturbating more aggressively on the important regions, we are in fact attempting to synthesize hard anomalous locations for training, which helps to learn more discriminative and compact boundaries to spot the abnormal patterns. Besides, the attention masks are sampleaware, accommodating images from diverse categories that exhibit different anomaly types. Second, it is more efficient to train. Anomalies in industrial images usually exhibit diverse structures and textures scattered across any foreground locations. Some anomalies can be easily identified by reconstructing them based on proximal pixels with similar colors, while anomalies with irregular structures near the contour or local edge pixels pose a challenge for reconstruction. Masking these critical areas is more conducive and efficient to representation learning for reconstruction. Lastly, it is a concise and effective framework. The whole main branch is just constructed with a pretrained backbone followed by a simple ViT-based decoder, and the auxiliary branch will be removed after training, incurring no computation cost for inference.

Empirical results show that AGPNet greatly improves industrial anomaly detection accuracy. For instance, with only 500 epochs of training, it achieves $9 8 . 0 \%$ P-AUC and $9 8 . 7 \%$ I-AUC on MVTec-AD Bergmann et al. (2019) under multiclass setting, outperforming UniAD You et al. (2022) with 1000 epochs of training by $+ 1 . 2 \%$ and $+ 2 . 2 \%$ , respectively.

Our contributions are summarized as follows:

• We present a simple yet effective framework named AGPNet, which integrates a simple reconstruction network with a novel perturbation scheme. Unlike existing perturbation schemes that treat each spatial location equally, it can apply more targeted perturbations to crucial areas for each sample to enhance the reconstruction process.   
• We propose to calculate the attention masks based on prior and learnable attention from the reconstruction network. These masks serve to guide the reconstruction network in comprehensively learning normal patterns across diverse industrial images.   
• Extensive experiments show that AGPNet obtains superior performance on both multi-class and one-class settings, compared with existing state-of-the-arts. Besides, it can also be versatilely and effectively extended to the few-shot setup.

# 2. Related Work

# 2.1. Unsupervised Visual Anomaly Detection

Due to the uncertain and scarce nature of visual anomalies, a dominant direction in this area is to formulate this problem as an unsupervised paradigm, also known as unsupervised anomaly detection. It assumes that all images in the training set are normal while the test set contains both normal and abnormal images. A great deal of work has been proposed, for more comprehensive surveys, see Liu et al. (2024); Zipfel et al. (2023). Generally, they can be broadly divided into three groups: synthesizing-based, embedding-based, and reconstruction-based methods.

Synthesizing-based methods Li et al. (2021); Schlu¨ter et al. (2022); Zavrtanik et al. (2022, 2021) synthesize anomalies for training. For instance, DRAEM Zavrtanik et al. (2021) attempts to blend predefined texture images with normal samples. CutPaste Li et al. (2021) simply cuts an image patch and randomly pastes it to another image location. These methods heavily rely on the synthesized anomaly quality. However, due to the uncertainty and unpredictability of anomalies, it is hard and even impossible to synthesize all types of real anomalies with high fidelity.

Embedding-based methods Defard et al. (2021); Roth et al. (2022); Liu et al. (2023); Bae et al. (2023) typically work by first utilizing pretrained models to embed normal images and then employing statistical models to model the normal distributions. During testing, samples are regarded as being abnormal if they are far away from the normal distributions. For instance, PaDiM Defard et al. (2021) models embedded patch features with multivariate Gaussian distribution. PatchCore Roth et al. (2022) exploits memory banks to store nominal features. However, they generally demand more computational resources to store the normal embeddings for the training set. In addition, the computing process to identify anomalies during inference is also time-consuming.

Reconstruction-based methods Zavrtanik et al. (2021); Schlu¨ter et al. (2022); Zhou et al. (2020) hold the assumption that the model pretrained only on normal data can reconstruct normal samples while finding difficulty in reconstructing anomalous during inference. However, this assumption sometimes is not satisfied because of the strong and uncontrollable generalization of deep neural networks. Therefore, many strategies have been proposed to address this issue. Some methods try to add prior from the images Zhou et al. (2020); Shi et al. (2021); Xia et al. (2020) as guidance for reconstructions. For instance, P-Net Zhou et al. (2020) proposes to feed the structure features into the reconstruction network. Other methods Collin and De Vleeschouwer (2021); Ye et al. (2020); Liu et al. (2023) frame anomaly detection as the image restoration problem by corrupting the selected content in the images and then learning to restore the corruptions. several methods Li et al. (2020); Zavrtanik et al. (2021) take the idea of image inpainting by first masking the image randomly and then learning to recover it.

Despite promising results, the above methods focus on the “one for one” paradigm, which trains one separate model for each category and thus is more computationally expensive. Recently, some researchers have shifted their attention to another challenging “one for all” paradigm, which only has to train a single for all categories. UniAD You et al. (2022) is the pioneering work for this task. It observses that under this setup the reconstruction network more easily falls into “identical shortcut” problem. They further introduce learnable queries to the transformer network. Later, OmniAL Zhao (2023) proposes a panel-guided anomaly synthesis method to prevent the model from performing identical reconstruction. RLR He et al. (2024a) introduces learnable reference representations to force the framework to learn normal feature patterns explicitly for all categories. MambaAD He et al. (2024b) is the first to integrate the recent Mamba Gu and Dao (2023) architecture into reconstruction-based anomaly detection frameworks, which achieves efficient global contextual modeling while preserving local features.

Following previous works, our work is also reconstructionbased but deals with both “one for one” and “one for all” paradigms. Unlike most existing methods of designing sophisticated architectures, we present a perturbation scheme that could effectively force a simple plain ViT-based reconstruction decoder to learn invariant normal features. Compared with other perturbation schemes treating each spatial location equally, we use attention masks calculated from the model itself to guide the perturbation. By doing so, our reconstruction network can have a better understanding of the intrinsic and important local normal patterns, which help suppress the anomaly reconstructions effectively.

# 2.2. Few-shot Visual Anomaly Detection

Recent research has begun to focus on a more challenging task setting: few-shot anomaly detection. It deals with scenarios where only a few training samples are available. RegAD Huang et al. (2022) employs a siamese network architecture to train a registration network, calculating the Mahalanobis distance between the features of the test image and those of a support set containing only normal samples for anomaly detection. With the rise of vision-language models, ${ \mathrm { W i n C L I P } } +$ Jeong et al. (2023) introduces CLIP Radford et al. (2021) for few-shot anomaly detection. PromptAD Li et al. (2024) further develops on CLIP, leveraging manually designed and learned anomaly prompts to expand the distance between anomalous and normal features, thereby assisting in anomaly detection.

Our framework can also be effectively extended to few-shot anomaly detection. Compared with existing works, our works does not rely additional memory bank or complex data augmentations. With the simple yet effective attention-based perturbation scheme, our framework is also sample-efficient, obtaining comparable performance to the current leading fewshot anomaly detection methods.

# 3. Method

Problem Formulation. Following previous works Zavrtanik et al. (2021); You et al. (2022), we formulate visual anomaly detection (VAD) as a reconstruction problem. Given a training set Xtrain = {Ii }N normal i=1 comprising only normal samples, our main goal is to train a reconstruction model $f ( \cdot )$ to accurately identify both pixel and image-level anomalies in a test set $\boldsymbol { X _ { t e s t } } = \{ I ^ { j } \} _ { j = 1 } ^ { M }$ containing both normal and abnormal samples. Note that the problem of VAD can be further divided into two settings: one-class and multi-class. The one-class setting involves training individual models with images from each semantic category, while the multi-class setting aims to train a unified model with all images from diverse semantic categories. The latter is more computationally efficient due to the $O ( 1 )$ task complexity regardless of the semantic category number. Yet it is more challenging due to the diverse distributions of different categories, making it difficult to learn compact representations. Lastly, in addition to conventional unsupervised anomaly detection, our framework can also be versatilly extended to few-shot anomaly detection. In this setup, only a few images are provided to the framework for training.

![](images/36828aab517ea9c860a5ca91f13ecfda5637a10f4d956a443aa5b61655662694.jpg)  
Figure 2: A framework overview of AGPNet. It consists of two branches, ie, the main reconstruction branch and the attention-guided perturbation branch. During training, given an input normal image $I _ { n o r m a l }$ , the main reconstruction branch is used for reconstruction, while the perturbation branch aims to generate attention masks based on the main branch for perturbation at both image and feature levels, making the reconstruction network focus on the important local details. During inference, we simply keep the main branch and generate the anomaly map by comparing the input and output of the decoder.

# 3.1. AGPNet

# 3.1.1. Model Overview

As shown in Fig. 2, our framework mainly consists of two branches. The main reconstruction branch aims to reconstruct the inputs, while the auxiliary branch aims to generate attention masks to guide the perturbation process for the inputs. With the attention mask as perturbation guidance, the reconstruction network tends to learn invariant normal patterns at those crucial locations. After training, the framework can be used for anomaly detection directly by comparing the input and output of the decoder directly, with the discrepancy indicating the anomaly locations. Below we describe each component in detail.

# 3.1.2. Reconstruction Branch

In the main reconstruction branch, we first utilize a frozen backbone to extract image representations and then utilize a lightweight decoder to learn to perform feature reconstructions. The main reconstruction branch is simple and concise, which will be retained for inference.

Feature Extractor. Given an input RGB image $\boldsymbol { I } \in \mathbb { R } ^ { H \times W \times 3 }$ , we feed it into a pretrained network $\phi ( \cdot )$ and only take a subset of $L$ layer outputs. Any off-the-shelf pre-trained ViT-like model, which relies on attention mechanisms, can readily serve as the feature extractor. In this paper, we follow previous work Reiss et al. (2022) and adopt DINO Caron et al. (2021) as our backbone, which is trained via self-distillation without labels, has demonstrated its efficacy in generating superior representations for VAD. Note that DINO keeps the resolution fixed along the layer hierarchy, and all the feature maps share the same dimension after packing them into 3D feature maps. We denote $F _ { l } \in \mathbb { R } ^ { H _ { f } \times W _ { f } \times C _ { f } }$ as the $l .$ -th output, and $\sigma ( \cdot )$ as the layer normalization function. The representations from specific layers will undergo layer normalization individually, followed by summation:

$$
F _ { \mathrm { c l e a n } } = \sum _ { l = 1 } ^ { L } \sigma ( F _ { l } ) ,
$$

which will serve as the reconstruction target and be perturbed for further processing by the decoder.

Another important reason to utilize DINO Caron et al. (2021) is that it is a vison-transformer-based architecture and its attention weights can be leveraged to generate the attention mask to guide the decoder training with minimal efforts. Therefore, in addition to the $L$ outputs, we propose to reuse the associated attention maps. denoted by $A _ { l } \in \mathbb { R } ^ { H _ { l } \times W _ { l } }$ , to provide the important prior cues for the image to guide the training of the decoder. The produced attention weights are taken out and will further be forwarded to the attention-guided perturbation branch for further integration.

Decoder. The decoder aims to decode the pretrained features of normal data in the training set. However, without proper design, it easily falls into the “identity shortcut” issue, especially under the multi-class setting. Recently UniAD You et al. (2022) attempts to alleviate this issue with a learnablequery decoder that incorporates additional learnable queries at each transformer layer. This design incurs additional costs. In our framework, we simply adopt a plain visiontransformer-based decoder thanks to an auxiliary attentionbased perturbation branch. During training, the decoder is guided to decode the attention-guided perturbed representations derived from the feature extractor, rather than solely relying on the normal representation. Consequently, the “identity shotcut” issue will be greatly alleviated. Furthermore, our decoder has a more lightweight structure with only four layers, demonstrating strong empirical performance during inference.

# 3.1.3. Attention-Guided Perturbation.

The goal of the auxiliary branch is to perturb normal input for training under the guidance of attention masks. The details are described below.

Attention Mask Generation. To calculate the attention mask, we leverage the attention maps from the feature extractor and the momentum distillation of the decoder, which is formulated as below:

$$
\begin{array} { r l } { A _ { \mathrm { f i n a l } } = } & { \Phi _ { \mathrm { n o r m } } ( A _ { \mathrm { p r i o r } } ) + \Phi _ { \mathrm { n o r m } } ( A _ { \mathrm { l e a r n } } ) } \\ { \mathrm { w h e r e } } \\ & { \left\{ \begin{array} { l l } { \Phi _ { \mathrm { n o r m } } : = \mathrm { m a x } \mathrm { - m i n ~ n o r m a l i z a t i o n ~ f u n c t i o n } . } \\ { A _ { \mathrm { p r i o r } } = \Phi _ { \mathrm { a g g r } } ( \{ A _ { l } \} _ { l = 1 } ^ { L } ) } \\ { A _ { \mathrm { l e a r n } } = \Phi _ { \mathrm { a g g r } } ( \{ A _ { k } \} _ { k = 1 } ^ { K } ) } \end{array} \right. } \end{array}
$$

As discussed earlier, the attention weights from the feature extractor directly reflect the importance of each localization in the feature maps Zeiler and Fergus (2014). Therefore, based on the extracted subset of attention weights $\{ A _ { l } \} _ { l = 1 } ^ { L }$ , we compute the prior attention mask by ${ \cal A } _ { \mathrm { p r i o r } } = \Phi _ { \mathrm { a g g r } } ( \{ A _ { l } \} _ { l = 1 } ^ { L } ) .$ , where $\Phi _ { a g g r }$ denotes the aggregation operation over the attention maps. For simplicity, we only apply element-wise average pooling on the attention maps.

Second, the attention weights within the decoder are learned to aggregate important contextual cues for reconstruction. Hence, we further propose to utilize it for attention mask generation. However, these attention weights fluctuate rapidly with high variance, especially at the beginning of the training. For stabilization, we employ mean-distillation Tarvainen and Valpola (2018), which includes an exponentialmoving-average (EMA) version of the decoder as the teacher.

$$
\theta _ { \mathrm { { m d } } } = \eta \cdot \theta _ { \mathrm { { m d } } } + ( 1 - \eta ) \cdot \theta _ { \mathrm { { d e c } } }
$$

where $\theta _ { \mathrm { { m d } } }$ and $\theta _ { \mathrm { d e c } }$ denote the parameters of the teacher and student (i.e., the decoder) models, respectively. $\eta$ controls the weight assigned to previous teacher parameters. The selfattention weights $\{ A _ { k } \} _ { k = 1 } ^ { K }$ from the teacher are taken to compute the learnable attention masks by ${ \cal A } _ { \mathrm { l e a r n } } = \Phi _ { \mathrm { a g g r } } ( \{ A _ { k } \} _ { k = 1 } ^ { K } )$ . Lastly, we derive the final mask as $A _ { \mathrm { f i n a l } } \ = \ \Phi _ { \mathrm { n o r m } } ( A _ { \mathrm { p r i o r } } ) \ +$ $\Phi _ { \mathrm { n o r m } } ( A _ { \mathrm { l e a r n } } )$ which will be utilized to guide the following noise-adding process.

Attention-Guided Noise. For unsupervised anomaly detection, synthesized anomalies play a critical role in learning a compact and discriminative boundary to discern normal and abnormal samples. Numerous approaches have been proposed Zavrtanik et al. (2021); Li et al. (2021) for synthesizing anomalies that closely resemble real ones. Concerning the impracticality of synthesizing all types of real anomalies, we argue that focusing on critical locations and perturbing them at the feature level could be a more efficient strategy. Besides, by perturbing those critical and informative locations, the models are forced to reason the relations across different localizations more comprehensively. To this end, we simply add Gaussian noise weighted by the attention mask in the normal feature space to synthesize the hard abnormal samples. Specifically, a noise tensor $\mathcal { E } \in \mathbb { R } ^ { H _ { f } \times W _ { f } \times C _ { f } }$ is first generated with each entry simply drawn from an i.i.d Gaussian distribution $\mathcal { N } ( \mu , \sigma )$ . Then we add the noise tensor to the normal features $F _ { \mathrm { c l e a n } }$ for perturbation based on attention mask $A _ { \mathrm { f i n a l } }$ as follows:

$$
F ^ { \prime } = F _ { \mathrm { c l e a n } } + \mathcal { E } \odot ( \alpha ( t ) \cdot \Phi _ { \mathrm { n o r m } } ( A _ { \mathrm { f i n a l } } ) + \beta )
$$

where $\odot$ means the elementwise product, $\alpha ( t )$ and $\beta$ control the intensity of adding noise to the features, which linearly increases with the training epochs. $\beta$ is a hyperparameter. $\Phi _ { \mathrm { n o r m } }$ denotes the max-min normalization function. The value of $\alpha ( t )$ can be calculated by

$$
\alpha ( t ) = \gamma \cdot ( \frac { t } { T } ( p - m ) + m ) ,
$$

where $\gamma$ represents the basic noise factor, $p$ represents the maximum noise intensity, $m$ represents the minimum noise intensity, $T$ represents the maximun training epoch and $t$ represents the current training epoch.

In addition to introducing noise at the feature level, we also incorporate noise at the image level, guided by the attention mask $A _ { \mathrm { f i n a l } }$ , resulting in improved performance. To achieve this, we first upsample $A _ { \mathrm { f i n a l } }$ to match the image dimensions, producing $A _ { \mathrm { i m g } } \in \mathbb { R } ^ { H \times W }$ . Subsequently, we progressively increase the mask ratio for binarization. Gaussian noise is also applied within the masked regions. Gradually increasing the mask ratio increases the difficulty of denoising the decoder, which helps model training.

# 3.2. Loss Function

During training, for each normal image $I _ { n o r m a l } \ \in \ X _ { t r a i n }$ , we aim to reconstruct its normal features $F _ { \mathrm { c l e a n } }$ from for the perturbed counterparts at both the image and feature levels. Our total loss is derived as

$$
L _ { \mathrm { t o t a l } } = \frac { 1 } { 2 } ( L _ { \mathrm { f e a t } } + L _ { \mathrm { i m g , f e a t } } )
$$

where $L _ { \mathrm { f e a t } }$ indicates reconstruction by perturbing features, and $L _ { \mathrm { i m g } }$ , feat represents reconstruction by perturbing image and features at the same time, which are defined below:

$$
\left\{ \begin{array} { c } { \displaystyle L _ { \mathrm { f e a t } } = \frac { 1 } { H _ { f } \times W _ { f } } \mathbf { M S E } ( F _ { \mathrm { f e a t } } , F _ { \mathrm { c l e a n } } ) } \\ { L _ { \mathrm { i m g , f e a t } } = \displaystyle \frac { 1 } { H _ { f } \times W _ { f } } \mathbf { M S E } ( F _ { \mathrm { i m g , f e a t } } , F _ { \mathrm { c l e a n } } ) } \end{array} \right.
$$

where $F _ { \mathrm { c l e a n } }$ indicates the features of the pretrained encoder output without noise-added, $F _ { \mathrm { f e a t } }$ represents the reconstructed features by perturbing features, and $F _ { \mathrm { i m g , f e a t } }$ means the reconstructed features by perturbing images and features at the same time.

# 3.3. Anomaly Map

During inference, we utilize reconstruction errors to calculate the anomaly map. Given an input image $I \in {  { \boldsymbol { X } } } _ { t e s t }$ from the test set, we first forward it to our framework to calculate pixel-level reconstruction errors in the feature map with $L _ { 2 }$ distance. Let $F _ { q } \in \mathbb { R } ^ { H _ { f } \times W _ { f } \times C _ { f } }$ and $\hat { F } _ { q } \in \mathbb { R } ^ { H _ { f } \times W _ { f } \times \bar { C } _ { f } }$ denote the input and output of the reconstruction network, the pixellevel reconstruction error $M \in \mathbb { R } ^ { H _ { f } \times W _ { f } }$ in the feature space at each pixel location $( h , w )$ is calculated by

$$
M _ { h , w } = \Vert { F } _ { h , w } - \hat { F } _ { h , w } \Vert _ { 2 }
$$

We further upsample $M$ to be the same size as the input image with bilinear interpolation to produce the final anomaly map. For the image level anomaly score, we simply compute the maximum value of the average-pooled $M$ .

# 4. Experiment

# 4.1. Experimental Setups

Datasets. MVTec-AD Bergmann et al. (2019) is one of the most widely used industrial anomaly detection datasets. It has a training set consisting of 3629 normal images and a test set of 467/1,258 normal/anomaly images. All the images are divided into 15 categories. In this paper, we conduct experiments with two settings. One involves training a single model for all classes (multi-class setting), while the other involves training a separate model for each class (one-class setting). The former is a more efficient yet challenging task.

VisA Zou et al. (2022) is another challenging anomaly detection dataset. It has a total of 10,821 images divided into 12 different categories. The training set has 8,659 normal images, and the test set includes 962/1,200 normal/anomaly images, providing a comprehensive benchmark for evaluating detection performance.

MVTec-3D Bergmann et al. (2021) is a collection of 4,147 scans obtained through the industrial 3D sensor. It covers 10 different categories, each accompanied by both RGB images and corresponding 3D point cloud data. The training set consists of 2,656 images that include only anomaly-free samples. The test set comprises 1,197 images, which encompass both normal and anomalous samples. In our experiments conducted, only the RGB images were utilized, with the 3D point cloud data not being considered.

Evaluation Metrics. Referring to prior research You et al. (2022); Zavrtanik et al. (2021); Defard et al. (2021), we report the anomaly detection and localization performance with Image-level and Pixel-level Area Under the Receiver Operating Curve, denoted by I-AUC and P-AUC respectively. We also adopt Per-Region-OverlapBergmann et al. (2020) to better evaluate model’s capability, denoted by PRO.

Implement Details. Each image is resized to $2 2 4 \times 2 2 4$ . The feature extractor is initialized by pre trained DINO(ViTS-16) Caron et al. (2021), which is frozen during training. We employ AdamWLoshchilov and Hutter (2019) optimizer with a weight decay of $1 \times 1 0 ^ { - 4 }$ . We train our framework for 500 epochs on a single GPU (NVIDIA RTX 4090) with a batch size of 32. The learning rate is initially set to $1 \times 1 0 ^ { - 3 }$ and reduced by a factor of 0.1 after 200 epochs. The ratio of added noise at the image level linearly increases from 0.6 to 1.0 from the $1 0 0 ^ { \mathrm { t h } }$ to $4 0 0 ^ { \mathrm { t h } }$ epochs. The intensity of added noise $\alpha$ at the feature level linearly increases from 0 to 1.0 from the $1 ^ { \mathrm { s t } }$ to $4 0 0 ^ { \mathrm { t h } }$ epochs. The parameter $\eta$ in EMA is set to 0.9999, and the mean teacher is updated every 10 steps. In particular, we performed a 32-fold data augmentation (rotate and flip) under a few-shot setting.

# 4.2. Anomaly Detection on MVTec-AD

Baseline. We comprehensively evaluated representative methods in both one-class and multi-class settings. In the multi-class setting, we first compare our method with two representative one-class approaches: DRAEM Zavrtanik et al. (2021) and SimpleNet Liu et al. (2023), and then compare with methods specifically designed for the multi-class setting, including UniAD You et al. (2022), RD4AD Deng and Li (2022), DiADHe et al. (2024), ViTAD Zhang et al. (2023), MambaAD He et al. (2024b). Note that all methods are trained under the multi-class scenario. In the one-class setting, the compared models includes: RD4AD Deng and Li (2022), PatchCore Roth et al. (2022), DRAEM Zavrtanik et al. (2021), DeSTSeg Zhang et al. (2023), Simplenet Liu et al. (2023).

Evaluation on MVTec-AD under the multi-class setting. As shown in Table 1, we conducted comprehensive comparative experiments on the MVTec-AD to demonstrate the superiority of our method. It can be observed that applying one-class methods to the multi-class scenario reflects undesirable performance. Furthermore, compared with existing multi-class methods, we outperform all of them, achieving the best $9 8 . 7 \%$ and $9 8 . 0 \%$ I-AUC and P-AUC, respectively. ViTAD is a competitive approach, while we outperform it in 12 out of 15 categories in detection capacity, and exceed it in 10 categories for localization performance. In the more challenging PRO metric, our model also achieves a great performance of $9 2 . 9 \%$ , with a $1 . 5 \%$ improvement over ViTAD. Compared to the MambaAD approach utilizing the latest Mamba-architecture decoder, our method achieves improvements of $+ 0 . 1 \%$ in I-AUC and $+ 0 . 2 \%$ in P-AUC, despite a $0 . 2 \%$ lower performance on the PRO metric.

The qualitative comparison in Figure 3 demonstrates the superior anomaly localization capability of our method compared to DiAD and UniAD. As shown in columns 4 and 10, our approach achieves exceptional precision in handling geometrically complex objects (toothbrushes) and textured surfaces (carpets). The heatmaps which generate by the proposed model accurately align with anomaly contours at the pixel level.

Evaluation on MVTec-AD under the one-class setting. As shown in Table 2, we can observe that compared with the two synthetic data-based methods, DRAEM Zavrtanik et al. (2021) and DeSTSeg Roth et al. (2022), we outperform them by a large margin. Specifically, compared with DRAEM, we outperform it by $1 . 2 \%$ and $1 . 0 \%$ in I-AUC and P-AUC, respectively. And compared to DeSTSeg, we also bring gains of $0 . 6 \%$ I-AUC and $0 . 4 \%$ P-AUC. Moreover, we achieved the best localization result of $9 8 . 3 \%$ P-AUC, while the detection performance is slightly lower than SimpleNet, ranking second among previous arts. It should be noted that when SimpleNet is adapted to the multi-class setup, its performance significantly drops, as evidenced in Table 1. In contrast, our method demonstrates robust performance across both setups, highlighting its strong generalization capability and suitability for diverse real-world applications.

Table 1: Comparison of image-level/pixel-level results on MVTec AD under multi-class setting. ∗ denotes the reproduced results using the official code. Bold and underline indicate the best and the second best, respectively.   

<html><body><table><tr><td rowspan="2">Category</td><td>DREM* (ICCV 2022)</td><td>SimpleNet* (CVPR 2023)</td><td>UniAD (NeurIPS 2022)</td><td>DiAD (AAAI 2024)</td><td>ViTAD (arXiv 2023)</td><td>MambaAD (NeurIPS 2024)</td><td>Ours</td></tr><tr><td colspan="7">I-AUC / P-AUC / PRO (%)</td></tr><tr><td>Bottle</td><td>97.5 / 87.6/ 80.7</td><td>97.7 /91.2/90.6</td><td>99.7/98.1/93.2</td><td>99.7/98.4/-</td><td>100.0/98.8/94.3</td><td>100.0/98.9/96.1</td><td>100.0/99.1/95.0</td></tr><tr><td>Cable</td><td>57.8 / 71.3 / 40.1</td><td>87.6 /88.1/85.4</td><td>95.2 /97.3 / 85.7</td><td>94.8 /96.8 / -</td><td>98.5/96.2/90.2</td><td>98.9/96.2/90.3</td><td>99.7 /98.0/92.9</td></tr><tr><td>Capsule</td><td>65.3 / 50.5 /27.3</td><td>78.3 / 89.7 / 84.5</td><td>86.9 / 98.5 /89.7</td><td>89.0 /97.1 /-</td><td>95.4 /98.3/92.0</td><td>94.5/98.5/93.9</td><td>93.8 / 98.5 /91.9</td></tr><tr><td>Hazelnut</td><td>93.7 /96.9 /78.7</td><td>99.2 / 95.7 / 87.4</td><td>99.8 /98.1/92.9</td><td>99.5 / 98.3 / -</td><td>99.8/99.0 /95.2</td><td>100.0/99.0/95.8</td><td>100.0/98.9/93.6</td></tr><tr><td>Metal_nut</td><td>72.8 / 62.2 /66.4</td><td>85.1 /90.9 / 85.2</td><td>99.2 / 94.8 / 84.7</td><td>99.1/ 97.3 / -</td><td>99.7 / 96.4 /92.4</td><td>99.9/96.9/94.0</td><td>100.0 / 95.9 / 92.6</td></tr><tr><td>Pill</td><td>82.2 / 94.4 / 53.9</td><td>78.3 /89.7 / 81.9</td><td>93.7 / 95.0 / 94.7</td><td>95.7 /95.7 / -</td><td>96.2 / 98.7 /95.3</td><td>97.5/97.6/96.2</td><td>97.6 / 98.2 / 96.2</td></tr><tr><td>Screw</td><td>92.0 /95.5 / 55.2</td><td>45.5 /93.7 / 84.0</td><td>87.5 / 98.3 /94.9</td><td>90.7 /97.9 / -</td><td>91.3 /99.0 /93.5</td><td>95.9/99.5/97.3</td><td>91.8/99.0 / 95.5</td></tr><tr><td>Toothbrush</td><td>90.6 /97.7 / 68.9</td><td>94.7 / 97.5 / 87.4</td><td>94.2/98.4 / 88.0</td><td>99.7/99.0/ -</td><td>98.9 / 99.1/90.9</td><td>98.6/99.0/92.4</td><td>100.0 /98.9 /91.2</td></tr><tr><td>Transistor</td><td>74.8 / 64.5 / 39.0</td><td>82.0 /86.0 /83.2</td><td>99.8 / 97.9 / 93.3</td><td>99.8/95.1/-</td><td>98.8 /93.9 / 76.8</td><td>100.0/96.6/88.3</td><td>98.7 / 93.8/84.3</td></tr><tr><td>Zipper</td><td>98.8 /98.3/91.9</td><td>99.1 /97.0 /90.7</td><td>95.8 /96.8 /91.6</td><td>95.1/96.2 / -</td><td>97.6 / 95.9 / 87.2</td><td>99.6/98.4/95.1</td><td>99.7 / 97.6 / 90.5</td></tr><tr><td>Carpet</td><td>98.0 /98.6 /93.1</td><td>95.9 /92.4 / 90.6</td><td>99.8 / 98.5 /93.6</td><td>99.4 / 98.6 / -</td><td>99.5 / 99.0/ 94.7</td><td>99.9/99.3/97.5</td><td>100.0 / 99.2 / 95.6</td></tr><tr><td>Grid</td><td>99.3 /98.7 /92.1</td><td>49.8 / 46.7 / 88.6</td><td>98.2 /96.5 /91.1</td><td>98.5 /96.6 / -</td><td>99.7 /98.6 /95.8</td><td>100.0/99.3/97.3</td><td>99.9/98.9/96.3</td></tr><tr><td>Leather</td><td>98.7 / 97.3 / 88.5</td><td>93.9 / 96.9 /92.7</td><td>100.0 / 98.8 / 96.5</td><td>99.8 /98.8 / -</td><td>100.0 / 99.6 / 97.9</td><td>100.0/99.4/98.8</td><td>100.0 / 97.5 /97.6</td></tr><tr><td>Tile</td><td>99.8/98.0/97.0</td><td>93.7 / 93.1/90.6</td><td>99.3 /91.8 / 78.6</td><td>96.8 /92.4 / -</td><td>100.0 /96.6 /87.0</td><td>98.6/93.9/81.3</td><td>100.0 /97.4 /90.2</td></tr><tr><td>Wood</td><td>99.8 / 96.0 / 94.2</td><td>95.2/84.8 / 76.3</td><td>98.6/93.2 / 85.0</td><td>99.7/93.3/-</td><td>98.7 /96.4 /88.0</td><td>99.0/94.7/92.4</td><td>98.9/96.6/90.2</td></tr><tr><td>Mean</td><td>88.1 / 87.2 / 71.1</td><td>85.1 / 88.9 / 86.5</td><td>96.5 /96.8 /90.2</td><td>97.2 /96.8 / 90.7</td><td>98.3 / 97.7 / 91.4</td><td>98.6/97.7/93.1</td><td>98.7 / 98.0 / 92.9</td></tr></table></body></html>

Table 2: Comparison of image-level/pixel-level results on MVTec AD under one-class setting. ∗ denotes the reproduced results using the official code. Bold and underline indicate the best and the second best, respectively.   

<html><body><table><tr><td rowspan="2">Category</td><td>DREM (ICCV 2021)</td><td>RD4AD (CVPR 2022)</td><td>PatchCore (CVPR 2022)</td><td>(CVPR 2023) DeSTSeg</td><td>Simplenet (CVPR 2023)</td><td>Ours</td></tr><tr><td colspan="6">I-AUC /P-AUC/PRO (%)</td></tr><tr><td>Bottle</td><td>99.2/99.1/ -</td><td>100.0/ 98.7/ 96.6</td><td>100.0/98.6 /-</td><td>-/99.2/-</td><td>100.0/98.0 /-</td><td>100.0/99.1/94.6</td></tr><tr><td>Cable</td><td>99.9/97.6 / -</td><td>95.0 / 97.4/91.0</td><td>99.5 /98.4 / -</td><td>- /97.3 / -</td><td>94.8 /96.8 / -</td><td>100.0 / 98.7 / 93.9</td></tr><tr><td>Capsule</td><td>98.5 / 94.3 / -</td><td>96.3 / 98.7 / 95.8</td><td>98.1/98.8/ -</td><td>- /99.1/ -</td><td>97.7 /98.9 / -</td><td>97.9 /98.3 / 90.4</td></tr><tr><td>Hazelnut</td><td>100.0 / 99.7 / -</td><td>99.9 / 98.9 / 95.5</td><td>100.0 / 98.7 / -</td><td>-/99.6/-</td><td>100.0 / 97.9 / -</td><td>100.0 / 99.0 /94.4</td></tr><tr><td>Metal_nut</td><td>98.7 / 99.5 / -</td><td>100.0/97.3/92.3</td><td>100.0 / 98.4 / -</td><td>- /98.6/-</td><td>100.0 /98.8 / -</td><td>100.0/ 97.0/91.5</td></tr><tr><td>Pill</td><td>98.9 /97.6 / -</td><td>96.6 / 98.2 / 96.4</td><td>96.6 /97.4 / -</td><td>- / 98.7 / -</td><td>99.0 /98.6 / -</td><td>98.2 / 98.6 / 96.4</td></tr><tr><td>Screw</td><td>93.9 /97.6 / -</td><td>97.0 / 99.6 / 98.2</td><td>98.1/99.4 / -</td><td>- /98.5/ -</td><td>98.2 / 99.3 / -</td><td>97.2/99.3/96.7</td></tr><tr><td>Toothbrush</td><td>100.0 /98.1 / -</td><td>99.5 / 99.1 / 94.5</td><td>100.0 /98.7 / -</td><td> - /99.3 / -</td><td>99.7 /98.5</td><td>97.2 /98.9 / 90.3</td></tr><tr><td>Transistor</td><td>93.1/90.9 / -</td><td>96.7 / 92.5 / 78.0</td><td>100.0 / 96.3 / -</td><td>- /89.1/ -</td><td>100.0 / 97.6 / -</td><td>99.5 /97.3 / 91.3</td></tr><tr><td>Zipper</td><td>100.0 / 98.8 / -</td><td>98.5 / 98.2 / 95.4</td><td>99.4 /98.8 / -</td><td>- /99.1/ -</td><td>99.9/98.9 / -</td><td>99.3 / 97.4 / 93.0</td></tr><tr><td>Carpet</td><td>97.0 / 95.5 / -</td><td>98.9 /98.9 / 97.0</td><td>98.7 /99.0 / -</td><td>- /96.1/ -</td><td>99.7/98.2 / -</td><td>100.0 / 99.2 / 95.5</td></tr><tr><td>Grid</td><td>99.9 / 99.7 / -</td><td>100.0 / 99.3 / 97.6</td><td>98.2/98.7 / -</td><td>- /99.1/ -</td><td>99.7 /98.8 / -</td><td>100.0 / 98.9 /96.2</td></tr><tr><td>Leather</td><td>100.0 / 98.6 / -</td><td>100.0 / 99.4 / 99.1</td><td>100.0 / 99.3 / -</td><td>- / 99.7 / -</td><td>100.0 /99.2 / -</td><td>100.0 / 99.5 / 97.7</td></tr><tr><td>Tile</td><td>99.6 / 99.2 / -</td><td>99.3 / 95.6 / 90.6</td><td>98.7 /95.6 / -</td><td>-/98.0/-</td><td>99.8 /97.0 / -</td><td>100.0 / 97.5 / 89.1</td></tr><tr><td>Wood</td><td>99.1 /96.4 / -</td><td>99.2/95.3/90.9</td><td>99.2/95.0 / -</td><td>- /97.7 / -</td><td>100.0 / 94.5 / -</td><td>98.8 /96.7 / 88.5</td></tr><tr><td>Mean</td><td>98.0 /97.3 / -</td><td>98.5/ 97.8/ 93.9</td><td>99.1/98.1/ 93.5</td><td>98.6 /97.9 / -</td><td>99.6/98.1 /</td><td>99.2 / 98.3/ 93.1</td></tr></table></body></html>

# 4.3. Anomaly Detection on VisA

Baseline. We further conduct experiments on the challenging VisA dataset. On this dataset, we train a unified model with images of all categories. For comprehensive comparisons, we have selected state-of-the-art models including DRAEM Zavrtanik et al. (2021), UniAD You et al. (2022),

Simplenet Liu et al. (2023), ViTAD Zhang et al. (2023), and DiAD He et al. (2024).

Evaluation on VisA. As shown in Table 3, our method once gain achieves outstanding performance. Compared with the pioneering unified framework UniAD, we outperform it greatly by $6 . 8 \%$ and $3 . 5 \%$ in I-AUC and P-AUC, respectively. When compared with the pure ViT based model ViTAD, we outperform it in 10 out of 12 categories for detection and exceed it in 6 categories for localization. Specially, we improve upon it by $1 . 9 \%$ , $0 . 2 \%$ and $0 . 4 \%$ in I-AUC, P-AUC and PRO, respectively. DiAD is one diffusion-based framework for multi-class anomaly detection. We surpass it by $1 . 5 \%$ and $1 . 2 \%$ in I-AUC and P-AUC, respectively. The consistent improvements over the competitors validate the superiority of our method.

![](images/56025c26592ca5ec061185be0f159b1b5fec8a6c80ed9d4a3bffd882faa8b594.jpg)  
Figure 3: Qualitative illustration on MVTec-AD dataset.

Table 3: Anomaly detection results on VisA. ∗ denotes the reproduced results using the official code. Bold and underline indicate the best and the second best, respectively.   

<html><body><table><tr><td rowspan="3">Category</td><td>DREM* (ICCV 2021)</td><td>UniAD* (NeurIPS 2022)</td><td>SimpleNet* (CVPR 2023)</td><td>DiAD (AAAI 2024)</td><td>ViTAD (arXiv 2023)</td><td>Ours</td></tr><tr><td></td><td></td><td>I-AUC / P-AUC/PRO (%)</td><td></td><td></td><td></td></tr><tr><td colspan="6"></td></tr><tr><td>PCB1</td><td>71.9/94.6/52.9</td><td>92.8/93.3/64.1</td><td>91.6 /99.2/ 83.6</td><td>88.1/98.7 / 80.2</td><td>95.8 /99.5 / 89.6</td><td>97.1/99.5/92.2</td></tr><tr><td>PCB2</td><td>78.4 / 92.3 / 66.2</td><td>87.8 / 93.9 / 66.9</td><td>92.4 / 96.6 / 85.7</td><td>91.4 /95.2/ 67.0</td><td>90.6 97.9 82.0</td><td>94.7 / 98.0 / 82.0</td></tr><tr><td>PCB3</td><td>76.6 / 90.8/ 43.0</td><td>78.6 / 97.3 / 70.6</td><td>89.1 /97.2 / 85.1</td><td>86.2 / 96.7 / 68.9</td><td>90.9 98.2 88.0</td><td>92.1 / 98.3 / 81.4</td></tr><tr><td>PCB4</td><td>97.3 / 94.4 / 75.7</td><td>98.8 / 94.9 / 72.3</td><td>97.0 /93.9 / 61.1 98.9</td><td>99.6 / 97.0 / 85.0</td><td>99.1 / 99.1 /91.8</td><td>99.2/ 98.7/90.0</td></tr><tr><td>Macaroni1</td><td>69.8 /95.0/ 67.0 59.4 / 94.6 / 65.3</td><td>79.9 / 97.4 / 84.0 71.6 / 95.2 / 76.6</td><td>85.9 92.0 68.3/</td><td>85.7 68.5 62.5 /93.6 / 73.1</td><td>85.8 89</td><td>91.0 98. /86.4</td></tr><tr><td>Macaroni2 Capsules</td><td>83.4 /97.1/ 62.9</td><td>55.6 / 88.7 / 43.7</td><td>/77.8 74.1/97.1/73.7</td><td>58.2/97.3/77.9</td><td>79. 79.2 5.</td><td>79.8 /96.9 / 85.0 78.2/98.5 / 78.5</td></tr><tr><td>Candles</td><td>69.3 / 82.2 / 65.6</td><td>94.1/98.5 /91.6</td><td>84.1 87.6</td><td>92.8 89.4</td><td>90</td><td>93.6/ /88.5</td></tr><tr><td>Cashew</td><td>81.7/ 80.7/ 38.5</td><td>92.8/98.6/87.9</td><td>88.0 98.9 84.</td><td>91.5 61.8</td><td>8 8.</td><td>99.2/87.7 94.1</td></tr><tr><td>Chewing gum</td><td>93.7 / 91.0 / 41.0</td><td>96.3 / 98.8 / 81.3</td><td>96.4 97.9 78.3</td><td>99.1 / 94.7 / 59.5</td><td>94.9</td><td>96.1 / 98.9 / 73.5</td></tr><tr><td>Fryum</td><td>89.1 / 92.4 / 69.5</td><td>83.0/95.9 76.2</td><td>88.4 93.0 85.1</td><td>89.8 / 97.6/81.3</td><td>94.3 87.8</td><td>93.7/97.5/86.8</td></tr><tr><td>Pipe fryum</td><td>82.8 /91.1/61.9</td><td>98.9 94.7</td><td>90.8 98. 83.0</td><td>96.2 9g 89.9</td><td>97.8 9 94.7</td><td>98.4 /99.4 /94.4</td></tr><tr><td>Mean</td><td></td><td>85.5 /95.9 / 75.6</td><td>87.2 96.8 81.4</td><td>86.8 /96.0 5.2</td><td>90.5 98.2 85.1</td><td>92.3 / 98.4 / 85.5</td></tr><tr><td></td><td>79.1 / 91.3/ 59.1</td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

# 4.4. Anomaly Detection on MVTec-3D

Table 4: Anomaly detection results on MVTec-3D. ∗ denotes reproduced results using official code. Bold and underline indicate the best and second best, respectively.   

<html><body><table><tr><td>Metric</td><td>DREM*</td><td>UniAD* (ICCV 2021) (NeurIPS 2022) (AAAI2024) (arXiv 2023)</td><td>DiAD</td><td>ViTAD</td><td>Ours</td></tr><tr><td>I-AUC</td><td>65.2</td><td>78.9</td><td>84.6</td><td>78.7</td><td>84.9</td></tr><tr><td>P-AUC</td><td>93.2</td><td>96.5</td><td>96.4</td><td>98.0</td><td>98.0</td></tr><tr><td>PRO</td><td>55.0</td><td>88.1</td><td>87.8</td><td>91.3</td><td>92.9</td></tr></table></body></html>

Baseline. To further verify the superiority of the proposed method, we conduct experiments on the MVTec-3D dataset and compare it with state-of-the-art models including

DRAEM Zavrtanik et al. (2021), UniAD You et al. (2022), and DiAD He et al. (2024).

Evaluation on MVTec-3D. As illustrated in Table 4, our approach outperforms all others across every metric evaluated. Notably, when compared to UniAD, our model demonstrates a considerable advantage, with improvements of $6 . 0 \%$ , $1 . 5 \%$ , and $4 . 8 \%$ in the I-AUC, P-AUC, and PRO, respectively. Compared to the recent diffusion-based method DiAD, our approach has achieved significant improvements in the evaluation metrics, with enhancements of $0 . 3 \%$ , $1 . 6 \%$ , and $5 . 1 \%$ in the I-AUC, P-AUC, and PRO, respectively. ViTAD, a competitive method based on Vision Transformers, also performs well. However, our method still outperforms ViTAD in IAUC and PRO, further highlighting its effectiveness. These results substantiate the superior performance of our method.

# 4.5. Ablation Study

In this subsection, we perform a thorough analysis of our framework on MVTec-AD under the multi-class setting.

Table 5: Ablation studies on attention-guided noise. ‘-’ means no noise is added. ‘R’ indicates adding random noise. ‘A’ denotes adding attentionguided noise.   

<html><body><table><tr><td>Noise Type</td><td>No Noise</td><td colspan="3">Only Random</td><td colspan="3">Only Attention</td><td colspan="2">Hybrid</td></tr><tr><td>Image/Feature-level</td><td>-/-</td><td>R/-</td><td>-/R</td><td>R/R</td><td>A/-</td><td>-/A</td><td>A/A</td><td>A/R</td><td>R/A</td></tr><tr><td>I-AUC (%)</td><td>90.5</td><td>90.7</td><td>98.0</td><td>97.7</td><td>91.9</td><td>98.3</td><td>98.7</td><td>98.3</td><td>98.6</td></tr><tr><td>P-AUC (%)</td><td>95.5</td><td>95.5</td><td>97.7</td><td>97.8</td><td>96.0</td><td>97.7</td><td>98.0</td><td>97.8</td><td>97.8</td></tr></table></body></html>

Attention-Guided Noise. Table 5 studies the impact of noise addition techniques. We can observe that (1) without perturbing the input, this baseline model only achieves inadequate results of $9 0 . 5 \%$ and $9 5 . 5 \%$ detection (I-AUC) and localization (P-AUC) capacities, repectively. Such unsatisfactory results suggest that without perturbation the model also well-reconstruct the anomalous patterns. (2) When perturbing the input at the image level in a random manner, we observe a slight increase in I-AUC. However, adding noise at the feature level results in a significant increase, with gains of $+ 7 . 5 \%$ I-AUC and $+ 2 . 2 \%$ P-AUC, respectively. The phenomenon shows that random noise in the image and feature levels forces the model to learn normal patterns more comprehensively, effectively preventing the model from reconstructing anomaly regions. (3) When we introduce noise at the image-/feature-level using our Attention-guided method, compared to randomly adding noise at the same levels, we achieve significant benefits. Specifically, in terms of I-AUC, we bring gains of $1 . 2 \%$ ( $9 1 . 9 \%$ vs. $9 0 . 7 \%$ and $0 . 3 \%$ $( 9 8 . 3 \%$ vs. $9 8 . 0 \%$ ), indicating the effectiveness of our strategy. Moreover, applying our strategy to both image and feature levels further enhances the model’s ability for anomaly detection and localization, achieving the best $9 8 . 7 \%$ I-AUC and $9 8 . 0 \%$ P-AUC. (4) Lastly, employing a hybrid noise addition approach only achieves suboptimal performance, further proving the superiority of our attention-guided strategy.

Table 6: Ablation studies on backbones and compare with the previous methods in multi-class setting.   

<html><body><table><tr><td>Backbone</td><td>Method</td><td>I-AUC/P-AUC(%)</td></tr><tr><td rowspan="2">WideResNet-50</td><td>SimpleNet</td><td>85.1 / 88.9</td></tr><tr><td>Ours</td><td>87.4 / 93.4</td></tr><tr><td rowspan="2">EfficientNet-b4</td><td>UniAD</td><td>96.5 / 96.8</td></tr><tr><td>Ours</td><td>97.0 / 96.7</td></tr><tr><td rowspan="2">CLIP(ViT-B)</td><td>ViTAD-CLIP</td><td>71.2 / 81.6</td></tr><tr><td>Ours</td><td>94.6 / 95.8</td></tr><tr><td rowspan="2">DINO(ViT-S)</td><td>ViTAD</td><td>98.3 / 97.7</td></tr><tr><td>Ours</td><td>98.7 / 98.0</td></tr></table></body></html>

Backbone. To prove the generality of our method, we conduct comprehensive experiments using various backbones. As shown in Table 6, We employed two mainstream CNN-based backbones (Wide-Resnet-50He et al. (2015) and EfficientNet-b4Tan and Le (2020)) and two mainstream ViTbased backbones (CLIPRadford et al. (2021) and DINOCaron et al. (2021)) for our experiments. Compared to other mainstream anomaly detection frameworks, our method maintains competitive performance while using the same pre-trained backbones. Specifically, comparing to the ViTAD-CLIP, our method achieves a $2 3 . 4 \%$ improvement in I-AUC and a $1 4 . 2 \%$ improvement in P-AUC. Given the reconstructionbased strategy and plain Transformer architecture adopted by ViTAD, this significant enhancement can be attributed to the dynamic difficulty escalation and learnable updates provided by our attention mechanism, which effectively prevents training stagnation and ensures the robustness of anomaly detection. When facing SimpleNet with WideResNet50, our method outperforms with a $2 . 3 \%$ increase in I-AUC and a $4 . 5 \%$ increase in P-AUC. Furthermore, the consistent performance across both CNN and ViT-based architectures suggests that our method is not constrained by the specific characteristics of the backbone models. Instead, it effectively integrates with various feature extraction mechanisms, enhancing their anomaly detection capabilities. This adaptability is crucial for real-world applications where the choice of backbone may vary based on computational constraints or specific task requirements. In conclusion, the results fully demonstrate the generality of the proposed attention-guided perturbation method.

Multi-Layer Features. Table 8 systematically studies the impact of using multi-layer features fusion through four configurations: (1) Using only the deepest layer $f _ { 1 1 }$ achieves suboptimal $9 7 . 7 \%$ I-AUC and $9 7 . 8 \%$ P-AUC. This aligns with the fact that high-level features lose spatial details through complex neural networks, limiting precise anomaly location and local anomaly region perception. (2) Compared with only using the deepest feature, combining shallow layers features $f _ { 2 } , f _ { 5 }$ boosts detection $( 9 8 . 1 \%$ I-AUC) yet degrades localization performance $9 7 . 0 \%$ P-AUC). We attribute this to low-level noise amplification. While early layers retain object boundaries and detailed information, their sensitivity to texture variations increases false positives in complex backgrounds. (3) Fusing the mid-level layers’ features $\left( f _ { 8 } \right)$ and the deepest layers’ features $( f _ { 1 1 } )$ could improve the performance at image-level, compared with only utilizing the deepest layer features. This further proves that the shallower features have a better representation of the basic texture and contour of the sample to help the model achieve better image-level performance. 4) Overall, integrating both shallow and deep features yields optimal performance. This evidences our core hypothesis: Multi-layer fusion is non-additive but emergent. Just when features from both shallow and deep features interact cooperatively, the model simultaneously resolves differentgrained anomaly analyses.

Table 7: Ablation Studies on Noise Intensity $\alpha$ at Feature-level and Attention Map. ‘D’ indicates just using the $A _ { \mathrm { l e a r n } }$ to guide the noise tensor, ‘L’ denotes just using the $A _ { \mathrm { p r i o r } }$ to guide the noise tensor, and $\mathbf { \cdot _ { B } } ,$ means combining $A _ { \mathrm { p r i o r } }$ and $A _ { \mathrm { l e a r n } }$ to guide the noise tensor.   

<html><body><table><tr><td>Components</td><td colspan="5">Noise Intensity α at Feature-level</td></tr><tr><td>Options</td><td>0.5</td><td>1.0</td><td>1.5</td><td>0-1.0</td><td>Attention map L</td><td>D</td></tr><tr><td>I-AUC (%)</td><td>98.5</td><td>98.6</td><td>98.3</td><td>98.7</td><td>98.4</td><td>98.7</td></tr><tr><td>P-AUC (%)</td><td>97.9</td><td>97.9</td><td>97.9</td><td>98.0</td><td>98.6 97.9 97.9</td><td>98.0</td></tr></table></body></html>

Mean Teacher Decoder. Table 8 validates the effectiveness of the Mean Teacher decoder. Using the decoder of the reconstructed model to generate attention maps serves as the baseline. Introducing the Mean Teacher decoder results in an improvement of $0 . 3 \%$ for detection and $0 . 1 \%$ for localization, respectively. The experimental results demonstrate that the Mean Teacher decoder effectively mitigates the high variance of learnable attention weights during the initial training stages. Specifically, the Mean Teacher decoder supports a more stabilized attention-guided perturbation strategy, ensuring more stable weight updates in essential early training phases. This approach contrasts with the baseline decoder, where perturbations in initialization-sensitive regions (such as object boundaries or texture anomalies) induce unstable gradient updates that degrade detection consistency and localization precision.

Easy-to-Hard Noise Addition. As analyzed in Sec. 3.3, the hyperparameter $\alpha$ controls feature-level noise intensity in our noise addition curriculum.Table 7 explores the impact of noise intensity. Specially, we can observe that (1)Low noise intensity $( \alpha = 0 . 5 )$ ) reduces the difficulty of decoder reconstruction. It is not conducive to modeling normal samples. This causes the model to fall behind in image-level evaluation $( 9 8 . 5 \%$ vs. $9 8 . 7 \%$ ). (2) Excessive noise intensity $( \alpha =$ 1.5)induces severe semantic distortion in feature representations, leading to performance degradation of $0 . 4 \%$ I-AUC and $0 . 1 \%$ P-AUC compared to the optimal configuration. (3) Appropriate noise intensity $( \alpha = 1 )$ ) enhances the model’s ability to capture normal distribution and enables smooth training, resulting in near-optimal performance. (4)The proposed easy-to-hard noise with suitable intensity helps the model capture the target distribution and achieve the best performance. Its advantage is that it dynamically adjusts the noise intensity, which facilitates the model to learn the de-noise task from easy to hard. The results support the advantages of this method.

Table 8: Ablation Studies on Multi-level Features and Mean Distillation. “M.T.D” indicates generating attention maps with mean distillation, while “w.o.” means without using mean distillation.   

<html><body><table><tr><td rowspan="2">Components Options</td><td colspan="4">Multi-layer</td><td colspan="2">Mean Teacher</td></tr><tr><td>f11</td><td>f,f5</td><td>f8,f1</td><td>f,fs,f8,fu</td><td>w.0.</td><td>M.T.D</td></tr><tr><td>I-AUC (%)</td><td>97.7</td><td>98.1</td><td>98.1</td><td>98.7</td><td>98.4</td><td>98.7</td></tr><tr><td>P-AUC (%)</td><td>97.8</td><td>97.0</td><td>97.8</td><td>98.0</td><td>97.9</td><td>98.0</td></tr></table></body></html>

Attention Map. Table 7 ablates two components for the final attention mask. We can observe that 1) using $A _ { \mathrm { p r i o r } }$ alone to guide the noise tensor already gives us satisfactory performance, and 2) only $A _ { \mathrm { l e a r n } }$ guiding the noise tensor can achieve results close to optimal. 3) Finally, combining both obtains the highest performance. In particular, the strong performance achieved with learnable attention mask $A _ { \mathrm { l e a r n } }$ further demonstrates the superiority of attention-directed perturbation schemes. Specifically, the $A _ { \mathrm { l e a r n } }$ escalates denoising difficulty during normal distribution modeling, implementing efficient dynamic updating that prevents training plateauing in later phases. At the same time, $A _ { \mathrm { p r i o r } }$ provides good prior experience in the early stages of training.Their fusion optimally balances exploratory adaptation and exploitation of prior con

straints.

# 4.6. Efficiency Comparison with SoTAs

Table 9: Efficiency comparison of different methods   

<html><body><table><tr><td>Method</td><td>Learnable Parameters(M)</td><td>FLOPs(G)</td><td>FPS</td></tr><tr><td>DREM (ICCV2021)</td><td>97.42</td><td>198.15</td><td>95.17</td></tr><tr><td>UniAD (NeurIPS 2022)</td><td>7.48</td><td>6.46</td><td>286.92</td></tr><tr><td>DeSTSeg (CVPR2023)</td><td>32.37</td><td>30.67</td><td>386.76</td></tr><tr><td>SimpleNet (CVPR 2023)</td><td>3.94</td><td>18.34</td><td>63.66</td></tr><tr><td>Ours</td><td>4.42</td><td>5.63</td><td>326.65</td></tr></table></body></html>

In addition to model performance, the efficiency of the model is a critical concern for practical applications. We evaluate the efficiency of various methods on a single RTX4090 GPU with a batch size of 32 using Learnable Parameters (M), Floating Point Operations (FLOPs), and Frames Per Second (FPS). As demonstrated in Table 9, our method ranks second in terms of both Learnable Parameters and FPS, while achieving the highest FLOPs. These results indicate that our proposed method effectively balances efficiency and performance, exhibiting strong potential for deployment on enduser devices.

# 4.7. Extending to Few-shot Anomaly Detection

To further demonstrate the generality and potential of our framework, we conduct experiments on MVTec-AD under the challenging few-shot anomaly detection setups. We compared our method with some competitive methods and the models designed specifically for few-shot setting, including SPADE Cohen and Hoshen (2020), PaDiM Defard et al. (2021), PatchCore Roth et al. (2022), WinCLIP $^ +$ Jeong et al. (2023), RWDA Tamura (2023), FastRcon Fang et al. (2023), PromptAD Li et al. (2024). As shown in Table 10, our method also obtains a leading few-shot anomaly detection performance, especially at pixel level. Specifically, our method achieves the best localization performance of $9 6 . 2 \%$ and $9 7 . 3 \%$ P-AUC under 2-shot and 4-shot settings. Compared to the competitive method PromptAD, which depends on a large pretrained CLIP model and intricate prompt tuning, we achieved a $0 . 8 \%$ P-AUC increase under 4-shot settings. Patchcore is a competitive anomaly detection model that relies on a resource-intensive memory bank to store nominal patch features. However, when faced with the more challenging few-shot setup, our approach remarkably outperforms it, with improvements of $6 . 4 \%$ , $7 . 0 \%$ , and $9 . 0 \%$ in I-AUC, and $3 . 0 \%$ , $2 . 9 \%$ , and $3 . 6 \%$ in P-AUC under the 4-shot, 2-shot, and 1-shot settings, respectively. The outstanding performance of our framework in few-shot settings highlights the unique advantages of our attention-guided perturbation strategy for scenarios with severely limited data. This success is rooted in the strategy’s ability to prioritize perturbations in semantically critical regions of the input samples, which fundamentally accelerates model convergence and maximizes the utility of every available training sample. By dynamically focusing on areas that are most informative for modeling normal distribution, the attention-guided perturbation method ensures robust normal distribution modeling even with minimal training data, making it suited for few-shot setting.

Table 10: Comparison of image-level/pixel-level results on MVTec under few shot setting. Bold and underline indicate the best and the second best, respectively.   

<html><body><table><tr><td rowspan="2">Method</td><td>1-shot</td><td>2-shot</td><td>4-shot</td></tr><tr><td colspan="3">I-AUC / P-AUC (%)</td></tr><tr><td>SPADE (arXiv 2020) PaDiM (ICPR2020) PatchCore (CVPR 2022) WinCLIP+ (CVPR2023)</td><td>81.0 /91.2 76.6 / 89.3 83.4 /92.0</td><td>82.9 /92.0 78.9/91.3 86.3/93.3</td><td>84.8/92.7 80.4/92.6 88.8/94.3</td></tr><tr><td>RWDA (BMVC 2023)</td><td>93.1/95.2 93.3/-</td><td>94.4/96.0 94.0/-</td><td>95.2/96.2 94.5 / -</td></tr><tr><td>FastRcon (ICCV 2023) PromptAD (CVPR 2024)</td><td>-/- 94.6 / 95.9</td><td>91.0 /95.9 95.7 /96.2</td><td>94.2 /97.0 96.6/96.5</td></tr><tr><td>Ours</td><td>92.4 / 95.6</td><td>93.3 / 96.2</td><td>95.2 / 97.3</td></tr></table></body></html>

# 5. Limitation

Similar to other unsupervised anomaly methods, the proposed method may be sensitive to noisy samples. In future investigations, it is worthy of focusing on framework robustness especially when abnormal samples corrupt the training set. Furthermore, our framework mainly demonstrates the superior performance in industrial anomaly detection, while its generality in other areas such as medical anomaly detection and video anomaly analysis deserves more extensive investigation.

# 6. Conclusion

In this paper, we have proposed a simple yet effective reconstruction-based framework AGPNet to alleviate the issue of ‘identical shortcut’ for visual anomaly detection. It consists of a reconstruction branch for reconstruction and an auxiliary branch that aims to generate an attention mask for perturbations. To accommodate various samples across diverse categories, the attention mask is based on prior attention weights from the frozen feature extractor and meandistillation of the decoder. The versatility of our framework is demonstrated on a variety of setups in visual anomaly detection, including multi-class-based, one-class-based, and fewshot ones, all with leading performance.

# References

P. Bergmann, M. Fauser, D. Sattlegger, C. Steger, Mvtec ad–a comprehensive real-world dataset for unsupervised anomaly detection, in: CVPR, 2019, pp. 9592–9600.   
T. Defard, A. Setkov, A. Loesch, R. Audigier, Padim: a patch distribution modeling framework for anomaly detection and localization, in: ICPR, 2021, pp. 475–489.   
V. Zavrtanik, M. Kristan, D. Skoˇcaj, Draem-a discriminatively trained reconstruction embedding for surface anomaly detection, in: ICCV, 2021, pp. 8330–8339.   
P. Bergmann, S. L¨owe, M. Fauser, D. Sattlegger, C. Steger, Improving unsupervised defect segmentation by applying structural similarity to autoencoders, in: Proceedings of the 14th International Joint Conference on Computer Vision, Imaging and Computer Graphics Theory and Applications, 2019.   
Z. You, L. Cui, Y. Shen, K. Yang, X. Lu, Y. Zheng, X. Le, A unified model for multi-class anomaly detection, NeurIPS 35 (2022) 4571–4584.   
P. Bergmann, M. Fauser, D. Sattlegger, C. Steger, Uninformed students: Student-teacher anomaly detection with discriminative latent embeddings, in: CVPR, 2020, pp. 4183–4192.   
X. Zhang, S. Li, X. Li, P. Huang, J. Shan, T. Chen, Destseg: Segmentation guided denoising student-teacher for anomaly detection, in: CVPR, 2023, pp. 3914–3923.   
H. Deng, X. Li, Anomaly detection via reverse distillation from one-class embedding, in: CVPR, 2022, pp. 9737–9746.   
H. M. Schlu¨ter, J. Tan, B. Hou, B. Kainz, Natural synthetic anomalies for self-supervised anomaly detection and localization, in: ECCV, 2022, pp. 474–489.   
V. Zavrtanik, M. Kristan, D. Skoˇcaj, Dsr – a dual subspace re-projection network for surface anomaly detection, 2022. arXiv:2208.01521.   
Y. Liang, J. Zhang, S. Zhao, R. Wu, Y. Liu, S. Pan, Omni-frequency channelselection representations for unsupervised anomaly detection, IEEE TIP (2023).   
D. Gong, L. Liu, V. Le, B. Saha, M. R. Mansour, S. Venkatesh, A. v. d. Hengel, Memorizing normality to detect anomaly: Memory-augmented deep autoencoder for unsupervised anomaly detection, in: ICCV, 2019, pp. 1705–1714.   
D. T. Nguyen, Z. Lou, M. Klar, T. Brox, Anomaly detection with multiplehypotheses predictions, in: ICML, 2019, pp. 4800–4809.   
M. Sabokrou, M. Khalooei, M. Fathy, E. Adeli, Adversarially learned oneclass classifier for novelty detection, in: CVPR, 2018, pp. 3379–3388.   
J. Kim, K. Jeong, H. Choi, K. Seo, Gan-based anomaly detection in imbalance problems, in: Computer Vision–ECCV 2020 Workshops: Glasgow, UK, August 23–28, 2020, Proceedings, Part VI 16, 2020, pp. 128–145.   
X. Yan, H. Zhang, X. Xu, X. Hu, P.-A. Heng, Learning semantic context from normal samples for unsupervised anomaly detection, in: AAAI, volume 35, 2021, pp. 3110–3118.   
H. He, J. Zhang, H. Chen, X. Chen, Z. Li, X. Chen, Y. Wang, C. Wang, L. Xie, A diffusion-based framework for multi-class anomaly detection, in: Proceedings of the AAAI conference on artificial intelligence, volume 38, 2024, pp. 8472–8480.   
X. Zhang, N. Li, J. Li, T. Dai, Y. Jiang, S.-T. Xia, Unsupervised surface anomaly detection with diffusion probabilistic model, in: ICCV, 2023, pp. 6782–6791.   
Z. You, K. Yang, W. Luo, L. Cui, Y. Zheng, X. Le, Adtr: Anomaly detection transformer with feature reconstruction, in: International Conference on Neural Information Processing, 2022, pp. 298–310.   
Y. Shi, J. Yang, Z. Qi, Unsupervised anomaly segmentation via deep feature reconstruction, Neurocomputing 424 (2021) 9–22.   
J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, L. Fei-Fei, Imagenet: A largescale hierarchical image database, in: 2009 IEEE conference on computer vision and pattern recognition, 2009, pp. 248–255.   
M. Haselmann, D. P. Gruber, P. Tabatabai, Anomaly detection using deep learning based image completion, in: 2018 17th IEEE international conference on machine learning and applications (ICMLA), 2018, pp. 1237– 1242.   
K. Zhou, Y. Xiao, J. Yang, J. Cheng, W. Liu, W. Luo, Z. Gu, J. Liu, S. Gao, Encoding structure-texture relation with p-net for anomaly detection in retinal images, in: ECCV, 2020, pp. 360–377.   
A.-S. Collin, C. De Vleeschouwer, Improved anomaly detection by training an autoencoder with skip connections on images corrupted with stainshaped noise, in: ICPR, 2021, pp. 7915–7922.   
J. Liu, G. Xie, J. Wang, S. Li, C. Wang, F. Zheng, Y. Jin, Deep industrial image anomaly detection: A survey, Machine Intelligence Research 21 (2024) 104–135.   
J. Zipfel, F. Verworner, M. Fischer, U. Wieland, M. Kraus, P. Zschech, Anomaly detection for industrial quality assurance: A comparative evaluation of unsupervised deep learning models, Computers & Industrial Engineering 177 (2023) 109045.   
C.-L. Li, K. Sohn, J. Yoon, T. Pfister, Cutpaste: Self-supervised learning for anomaly detection and localization, in: CVPR, 2021, pp. 9664–9674.   
H. M. Schlu¨ter, J. Tan, B. Hou, B. Kainz, Natural synthetic anomalies for self-supervised anomaly detection and localization, in: ECCV, 2022, pp. 474–489.   
K. Roth, L. Pemula, J. Zepeda, B. Sch¨olkopf, T. Brox, P. Gehler, Towards total recall in industrial anomaly detection, in: CVPR, 2022, pp. 14318– 14328.   
W. Liu, H. Chang, B. Ma, S. Shan, X. Chen, Diversity-measurable anomaly detection, in: CVPR, 2023, pp. 12147–12156.   
J. Bae, J.-H. Lee, S. Kim, Pni: industrial anomaly detection using position and neighborhood information, in: ICCV, 2023, pp. 6373–6383.   
V. Zavrtanik, M. Kristan, D. Skoˇcaj, Reconstruction by inpainting for visual anomaly detection, PR 112 (2021) 107706. self-supervised anomaly detection and localization, in: European Conference on Computer Vision, 2022, pp. 474–489.   
Y. Xia, Y. Zhang, F. Liu, W. Shen, A. L. Yuille, Synthesize then compare: Detecting failures and anomalies for semantic segmentation, in: Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part I 16, 2020, pp. 145–161.   
F. Ye, C. Huang, J. Cao, M. Li, Y. Zhang, C. Lu, Attribute restoration framework for anomaly detection, IEEE TMM 24 (2020) 116–127.   
T. Liu, B. Li, X. Du, B. Jiang, L. Geng, F. Wang, Z. Zhao, Fair: Frequencyaware image restoration for industrial visual anomaly detection, arXiv preprint arXiv:2309.07068 (2023).   
Z. Li, N. Li, K. Jiang, Z. Ma, X. Wei, X. Hong, Y. Gong, Superpixel masking and inpainting for self-supervised anomaly detection., in: BMVC, 2020.   
Y. Zhao, Omnial: A unified cnn framework for unsupervised anomaly localization, in: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2023, pp. 3924–3933.   
L. He, Z. Jiang, J. Peng, W. Zhu, L. Liu, Q. Du, X. Hu, M. Chi, Y. Wang, C. Wang, Learning unified reference representation for unsupervised multi-class anomaly detection, in: European Conference on Computer Vision, 2024a, pp. 216–232.   
H. He, Y. Bai, J. Zhang, Q. He, H. Chen, Z. Gan, C. Wang, X. Li, G. Tian, L. Xie, Mambaad: Exploring state space models for multi-class unsupervised anomaly detection, arXiv preprint arXiv:2404.06564 (2024b).   
A. Gu, T. Dao, Mamba: Linear-time sequence modeling with selective state spaces, arXiv preprint arXiv:2312.00752 (2023).   
C. Huang, H. Guan, A. Jiang, Y. Zhang, M. Spratling, Y.-F. Wang, Registration based few-shot anomaly detection, in: European Conference on Computer Vision, Springer, 2022, pp. 303–319.   
J. Jeong, Y. Zou, T. Kim, D. Zhang, A. Ravichandran, O. Dabeer, Winclip: Zero-/few-shot anomaly classification and segmentation, in: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2023, pp. 19606–19616.   
A. Radford, J. W. Kim, C. Hallacy, A. Ramesh, G. Goh, S. Agarwal, G. Sastry, A. Askell, P. Mishkin, J. Clark, et al., Learning transferable visual models from natural language supervision, in: International conference on machine learning, PMLR, 2021, pp. 8748–8763.   
X. Li, Z. Zhang, X. Tan, C. Chen, Y. Qu, Y. Xie, L. Ma, Promptad: Learning prompts with only normal samples for few-shot anomaly detection, in: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2024, pp. 16838–16848.   
T. Reiss, N. Cohen, E. Horwitz, R. Abutbul, Y. Hoshen, Anomaly detection requires better representations, 2022. arXiv:2210.10773.   
M. Caron, H. Touvron, I. Misra, H. J´egou, J. Mairal, P. Bojanowski, A. Joulin, Emerging properties in self-supervised vision transformers, in: ICCV, 2021, pp. 9650–9660.   
M. D. Zeiler, R. Fergus, Visualizing and understanding convolutional networks, in: Computer Vision–ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part I 13, 2014, pp. 818–833.   
A. Tarvainen, H. Valpola, Mean teachers are better role models: Weightaveraged consistency targets improve semi-supervised deep learning results, 2018. arXiv:1703.01780.   
Y. Zou, J. Jeong, L. Pemula, D. Zhang, O. Dabeer, Spot-the-difference self-supervised pre-training for anomaly detection and segmentation, in: ECCV, 2022, pp. 392–408.   
P. Bergmann, X. Jin, D. Sattlegger, C. Steger, The mvtec 3d-ad dataset for unsupervised 3d anomaly detection and localization, arXiv preprint arXiv:2112.09045 (2021).   
P. Bergmann, M. Fauser, D. Sattlegger, C. Steger, Uninformed students: Student-teacher anomaly detection with discriminative latent embeddings, in: 2020 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), IEEE, 2020. URL: http://dx.doi.org/ 10.1109/CVPR42600.2020.00424. doi:10.1109/cvpr42600.2020. 00424.   
I. Loshchilov, F. Hutter, Decoupled weight decay regularization, 2019. URL: https://arxiv.org/abs/1711.05101. arXiv:1711.05101.   
Z. Liu, Y. Zhou, Y. Xu, Z. Wang, Simplenet: A simple network for image anomaly detection and localization, in: CVPR, 2023, pp. 20402–20411.   
J. Zhang, X. Chen, Y. Wang, C. Wang, Y. Liu, X. Li, M.-H. Yang, D. Tao, Exploring plain vit reconstruction for multi-class unsupervised anomaly detection, arXiv preprint arXiv:2312.07495 (2023).   
K. He, X. Zhang, S. Ren, J. Sun, Deep residual learning for image recognition, 2015. URL: https://arxiv.org/abs/1512.03385. arXiv:1512.03385.   
M. Tan, Q. V. Le, Efficientnet: Rethinking model scaling for convolutional neural networks, 2020. URL: https://arxiv.org/abs/1905.11946. arXiv:1905.11946.   
N. Cohen, Y. Hoshen, Sub-image anomaly detection with deep pyramid correspondences, arXiv preprint arXiv:2005.02357 (2020).   
M. Tamura, Random word data augmentation with clip for zero-shot anomaly detection, arXiv preprint arXiv:2308.11119 (2023).   
Z. Fang, X. Wang, H. Li, J. Liu, Q. Hu, J. Xiao, Fastrecon: Few-shot industrial anomaly detection via fast feature reconstruction, in: Proceedings of the IEEE/CVF International Conference on Computer Vision, 2023, pp. 17481–17490.