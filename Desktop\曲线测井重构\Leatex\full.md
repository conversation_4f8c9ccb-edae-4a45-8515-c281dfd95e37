# How to Use the IEEEtran LTEX Templates

IEEE Publication Technology Department

Abstract- This document describes the most common article elements and how to use the IEEEtran class with  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  to produce files that are suitable for submission to the Institute of Electrical and Electronics Engineers (IEEE). IEEEtran can produce conference, journal and technical note (correspondence) papers with a suitable choice of class options.

Index Terms- Class, IEEEtran,  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  paper, style, template, typesetting.

# I. INTRODUCTION

WELCOME to the updated and simplified documentation to using the IEEEtran  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  class file. The IEEE has examined hundreds of author submissions using this package to help formulate this easy to follow guide. We will cover the most commonly used elements of a journal article. For less common elements we will refer back to the "IEEEtran_HOWTO.pdf".

This document applies to version 1.8b of IEEEtran.

The IEEEtran template package contains the following example files:

bare_jrnl.tex bare_conf.tex bare_jrnl_compsoc.tex bare_conf_compsoc.tex bare_jrnl_comsoc.tex

These are bare bones templates to quickly understand the document structure.

It is assumed that the reader has a basic working knowledge of  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  Those who are new to  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  are encouraged to read Tobias Oetiker's "The Not So Short Introduction to  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}^{\gg}$  available at: http://tug.ctan.org/info/lshort/english/ lshort.pdf which provides an overview of working with  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$

# II. THE DESIGN, INTENT AND LIMITATIONS OF THE TEMPLATES

The templates are intended to approximate the final look and page length of the articles/papers. Therefore, they are NOT intended to be the final produced work that is displayed in print or on IEEEXplore. They will help to give the authors an approximation of the number of pages that will be in the final version. The structure of the  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  files, as designed, enable easy conversion to XML for the composition systems used by the IEEE's outsource vendors. The XML files are used to produce the final print/IEEEXplore pdf and then converted to HTML for IEEEXplore. Have you looked at your article/paper in the HTML version?

Manuscript created October, 2020; This work was developed by the IEEE Publication Technology Department. This work is distributed under the  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  Project Public License (LPPL) (http://www.latex- project.org/) version 1.3. A copy of the LPPL, version 1.3, is included in the base  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  documentation of all distributions of  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  released 2003/12/01 or later. The opinions expressed here are entirely that of the author. No warranty is expressed or implied. User assumes all risk.

III.  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  DISTRIBUTIONS: WHERE TO GET THEM IEEE recommends using the distribution from the  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  User Group at http://www.tug.org. You can join TUG and obtain a DVD distribution or download for free from the links provided on their website: http://www.tug.org/texlive/. The DVD includes distributions for Windows, Mac OS X and Linux operating systems.

# IV. WHERE TO GET THE IEEETRAN TEMPLATES

The IEEE Template Selector will always have the most up- to- date versions of the  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  and MSWord templates. Please see: https://template- selector.ieee.org/ and follow the steps to find the correct template for your intended publication. Many publications use the IEEETran LaTeX templates, however, some publications have their own special templates. Many of these are based on IEEETran, but may have special instructions that vary slightly from those in this document.

# V. WHERE TO GET  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  HELP - USER GROUPS

The following on- line groups are very helpful to beginning and experienced  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  users. A search through their archives can provide many answers to common questions.

http://www.latex- community.org/ https://tex.stackexchange.com/

# VI. DOCUMENT CLASS OPTIONS IN IEEETRAN

At the beginning of your  $\mathbf{L}\mathbf{I}\mathbf{E}\mathbf{X}$  file you will need to establish what type of publication style you intend to use. The following list shows appropriate documentclass options for each of the types covered by IEEETran.

Regular Journal Article \documentclass[journal] IEEEtran

Conference Paper \documentclass[conference] IEEEtran

Computer Society Journal Article \documentclass[10pt,journal,compsoc] IEEEtran

Computer Society Conference Paper \documentclass[conference,compsoc] IEEEtran

Communications Society Journal Article \documentclass[journal, comsoc] IEEEtran

Brief, Correspondence or Technote \documentclass[9pt,technote] IEEEtran

There are other options available for each of these when submitting for peer review or other special requirements. IEEE

recommends to compose your article in the base 2- column format to make sure all your equations, tables and graphics will fit the final 2- column format. Please refer to the document "IEEEtran HOWTO.pdf" for more information on settings for peer review submission if required by your EIC.

# VII. HOW TO CREATE COMMON FRONT MATTER

The following sections describe general coding for these common elements. Computer Society publications and Conferences may have their own special variations and will be noted below.

# A. Paper Title

The title of your paper is coded as:

\title{The Title of Your Paper}

Please try to avoid the use of math or chemical formulas in your title if possible.

# B. Author Names and Affiliations

The author section should be coded as follows:

\author{Masahito Hayashi \IEEEmembership{Fellow, IEEE}, Masaki Owari \thanks{M. Hayashi is with Graduate School of Mathematics, Nagoya University, Nagoya, Japan} \thanks{M. Owari is with the Faculty of Informatics, Shizuoka University, Hamamatsu, Shizuoka, Japan.}

Be sure to use the \IEEMembership command to identify IEEE membership status. Please see the "IEEEtran HOWTO.pdf" for specific information on coding authors for Conferences and Computer Society publications. Note that the closing curly brace for the author group comes at the end of the thanks group. This will prevent you from creating a blank first page.

# C. Running Heads

The running heads are declared by using the \markboth command. There are two arguments to this command: the first contains the journal name information and the second contains the author names and paper title.

\markboth{Journal of Quantum Electronics, Vol. 1, No. 1, January 2021} \{Author1, Author2, \MakeLowercase{\textit{et al.}}: Paper Title\}

# D. Copyright Line

For Transactions and Journals papers, this is not necessary to use at the submission stage of your paper. The IEEE production process will add the appropriate copyright line. If you are writing a conference paper, please see the "IEEEtran HOWTO.pdf" for specific information on how to code "Publication ID Marks".

# E. Abstracts

The abstract is the first element of a paper after the \maketitle macro is invoked. The coding is simply:

\begin{abstract} Text of your abstract. \end{abstract}

Please try to avoid mathematical and chemical formulas in the abstract.

# F. Index Terms

The index terms are used to help other researchers discover your paper. Each society may have its own keyword set. Contact the EIC of your intended publication for this list.

\begin{abstract} Text of your abstract. \end{abstract}

# VIII. HOW TO CREATE COMMON BODY ELEMENTS

The following sections describe common body text elements and how to code them.

# A. Initial Drop Cap Letter

The first text paragraph uses a "drop cap" followed by the first word in ALL CAPS. This is accomplished by using the \IEEEPARstart command as follows:

\begin{abstract} \textit{his} \end{abstract} \textit{your paper.}

# B. Sections and Subsections

Section headings use standard  $\mathrm{LTX}$  commands: \section, \subsection and \subsubsection. Numbering is handled automatically for you and varies according to type of publication. It is common to not indent the first paragraph following a section head by using \noindent as follows:

\section{Section Head} \noindent The text of your paragraph

# C. Citations to the Bibliography

The coding for the citations are made with the  $\mathrm{LTX}$  \cite command. This will produce individual bracketed reference numbers in the IEEE style. At the top of your  $\mathrm{LTX}$  file you should include:

\usepackage{cite}

For a single citation code as follows:

see \cite{ams}

This will display as: see [1]

For multiple citations code as follows:

\cite{ams,oxford,lacomp}

This will display as [1], [2], [3]

![](images/fb3c09c41bf9130ae41d0ddd05ad866a2f84223115f0f22424a498c0707985f3.jpg)  
Fig. 1. This is the caption for one fig.

# D. Figures

Figures are coded with the standard  $\mathrm{L}\mathrm{I}\mathrm{E}\mathrm{X}$  commands as follows:

\begin{figure}[!t] \centering \includegraphics[width=2.5in]{fig1} \caption{This is the caption for one fig.} \label{fig1} \end{figure}

The [t] argument enables floats to the top of the page to follow IEEE style. Make sure you include:

\usepackage{graphicx}

at the top of your  $\mathrm{L}\mathrm{I}\mathrm{E}\mathrm{X}$  file with the other package declarations.

To cross- reference your figures in the text use the following code example:

See figure \ref{fig1}...

This will produce: See figure 1. ..

# E. Tables

Tables should be coded with the standard  $\mathrm{L}\mathrm{I}\mathrm{E}\mathrm{X}$  coding. The following example shows a simple table.

\begin{table} \begin{center} \caption{Filter design equations ...} \label{tab1} \begin{tabular} | | c | c | c | \hline Order & Arbitrary coefficients & coefficients \ of filter & \\e_m\$ & \\b_{ij} \ \\hline 1& \\b_{ij}=\hat{\texttt{hat}}\{e\}.\hat{\texttt{hat}}\{\beta \texttt{e}\texttt{a}_{- }\{ij\} \} \ & \\b_{ij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{ij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{ij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{ij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{ij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{ij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{lij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{lij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{lij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{lij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{lij}=\hat{\texttt{e}}.\beta_{ij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{ij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{ij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{ij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{ij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{ij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{lij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{lij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{lij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{lij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{lij}, & \\b_{lij}=\hat{\mathcal{E}}.\beta_{lij}, & \\b_lij=\hat{\mathcal{E}}.\beta_{lij}, & \\b_lij=\hat{\mathcal{E}}.\beta_{lij}, & \\b_lij=\hat{\mathcal{E}}.\beta_{lij}, & \\b_lij=\hat{\mathcal{E}}.\beta_{lij}, & \\b_lij=\hat{\mathcal{E}}.\beta_{lij}, & \\b_lij=\hat{\mathcal{E}}.\alpha_{lij}, & \\b_lij=\hat{\mathcal{E}}.\alpha_{lij}, & \\b_lij=\hat{\mathcal{E}}.\alpha_{lij}, & \\b_lij=\hat{\mathcal{E}}.\alpha_{lij}, & \\b_lij=\hat{\mathcal{E}}.\alpha_{lij}, & \\b_lij=\hat{\mathcal{E}}.\alpha_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\alpha_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=\beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=β_{lij}, & \\b_lij=beta_{lij}, & \\b_lij

To reference the table in the text as follows:

\begin{center} \item{bare\_jrn1. tex} \item{bare\_jrn2. tex} \item{bare\_jrn3. tex} \item{bare\_jrn4. tex} \item{bare\_jrn5. tex} \item{bare\_jrn6. tex} \item{bare\_jrn7. tex} \item{bare\_jrn8. tex} \item{bare\_jrn9. tex} \item{bare\_jrn10. tex} \item{bare\_jrn11. tex} \item{bare\_jrn12. tex} \item{bare\_jrn13. tex} \item{bare\_jrn14. tex} \item{bare\_jrn15. tex} \item{bare\_jrn16. tex} \item{bare\_jrn17. tex} \item{bare\_jrn18. tex} \item{bare\_jrn19. tex} \item{bare\_jrn20. tex} \item{bare\_jrn21. tex} \item{bare\_jrn22. tex} \item{bare\_jrn23. tex} \item{bare\_jrn24. tex} \item{bare\_jrn25. tex} \item{bare\_jrn26. tex} \item{bare\_jrn27. tex} \item{bare\_jrn28. tex} \item{bare\_jrn29. tex} \item{bare\_jrn30. tex} \item{bare\_jrn31. tex} \item{bare\_jrn32. tex} \item{bare\_jrn33. tex} \item{bare\_jrn34. tex} \item{bare\_jrn35. tex} \item{bare\_jrn36. tex} \item{bare\_jrn37. tex} \item{bare\_jrn38. tex} \item{bare\_jrn39. tex} \item{bare\_jrn40. tex} \item{bare\_jrn41. tex} \item{bare\_jrn42. tex} \item{bare\_jrn43. tex} \item{bare\_jrn44. tex} \item{bare\_jrn45. tex} \item{bare\_jrn46. tex} \item{bare\_jrn47. tex} \item{bare\_jrn48. tex} \item{bare\_jrn49. tex} \item{bare\_jrn50. tex} \item{bare\_jrn51. tex} \item{bare\_jrn52. tex} \item{bare\_jrn53. tex} \item{bare\_jrn54. tex} \item{bare\_jrn55. tex} \item{bare\_jrn56. tex} \item{bare\_jrn57. tex} \item{bare\_jrn58. tex} \item{bare\_jrn59. tex} \item{bare\_jrn60. tex} \item{bare\_jrn61. tex} \item{bare\_jrn62. tex} \item{bare\_jrn63. tex} \item{bare\_jrn64. tex} \item{bare\_jrn65. tex} \item{bare\_jrn66. tex} \item{bare\_jrn67. tex} \item{bare\_jrn68. tex} \item{bare\_jrn69. tex} \item{bare\_jrn70. tex} \item{bare\_jrn71. tex} \item{bare\_jrn72. tex} \item{bare\_jrn73. tex} \item{bare\_jrn74. tex} \item{bare\_jrn75. tex} \item{bare\_jrn76. tex} \item{bare\_jrn77. tex} \item{bare\_jrn78. tex} \item{bare\_jrn79. tex} \item{bare\_jrn80. tex} \item{bare\_jrn81. tex} \item{bare\_jrn82. tex} \item{bare\_jrn83. tex} \item{bare\_jrn84. tex} \item{bare\_jrn85. tex} \item{bare\_jrn86. tex} \item{bare\_jrn87. tex} \item{bare\_jrn88. tex} \item{bare\_jrn89. tex} \item{bare\_jrn90. tex} \item{bare\_jrn91. tex} \item{bare\_jrn92. tex} \item{bare\_jrn93. tex} \item{bare\_jrn94. tex} \item{bare\_jrn95. tex} \item{bare\_jrn96. tex} \item{bare\_jrn97. tex} \item{bare\_jrn98. tex} \item{bare\_jrn99. tex} \item{bare\_jrn10. tex} \item{bare\_jrn11. tex} \item{bare\_jrn12. tex} \item{bare\_jrn13. tex} \item{bare\_jrn14. tex} \item{bare\_jrn15. tex} \item{bare\_jrn16. tex} \item{bare\_jrn17. tex

\begin{center} \item{bare\_jrn1. tex} \item{bare\_jrn2. tex} \item{bare\_jrn23. tex} \item{bare\_jrn24. tex} \item{bare\_jrn25. tex} \item{bare\_jrn26. tex} \item{bare\_jrn27. tex} \item{bare\_jrn28. tex} \item{bare\_jrn29. tex} \item{bare\_jrn30. tex) \item{bare\_jrn31. tex} \item{bare\_jrn32. tex} \item{bare\_jrn33. tex} \item{bare\_jrn34. tex} \item{bare\_jrn35. tex} \item{bare\_jrn36. tex} \item{bare\_jrn37. tex} \item{bare\_jrn38. tex} \item{ bare\_jrn39. tex} \item{bare\_jrn40. tex} \item{bare\_jrn41. tex} \item{bare\_jrn42. tex} \item{bare\_jrn43. tex} \item{bare\_jrn44. tex} \item{bare\_jrn45. tex} \item{bare\_jrn46. tex} \item{bare\_jrn

\begin{center} \item{bare\_jrn1. tex} \item{bare\_jrn2. tex} \item{bare\_jrn23. tex} \item{bare\_jrn24. tex} \item{bare\_jrn25. tex} \item{bare\_jrn26. tex} \item{bare\_jrn27. tex} \item{bare\_jrn

\begin{center} \item{bare\_jrn1. tex} \item{bare\_jrn2. tex} \item{bare\_jrn23. tex} \item{bare\_jrn24. tex} \item{bare\_jrn25. tex} \item{bare\_jrn26. tex} \item{bare\_jrn27. tex} \item{bare\_jrn30. tex} \item{bare\_jrn31. tex} \item{bare\_jrn32. tex} \item{bare\_jrn33. tex} \item{bare\_jrn34. tex} \item{bare\_jrn35. tex} \item{bare\_jrn36. tex} \item{bare\_jrn37. tex}

\begin{center} \item{bare\_jrn1. tex} \item{bare\_jrn2. tex} \item{bare\_jrn23. tex} \item{bare\_jrn24. tex} \item{bare\_jrn25. tex} \item{bare\_jrn26. tex} \item{bare\_jrn27. tex} \item{bare\_jrn10. tex} \item{bare\_jrn11. tex} \item{bare\_jrn12. tex} \item{bare\_jrn13. tex} \item{bare\_jrn14. tex} \item{bare\_jrn15. tex} \item{bare\_jrn16. tex} \item{bare\_jrn17. tex}

\begin{center} \item{bare\_jrn1. tex} \item{bare\_jrn2. tex} \item{bare\_jrn23. tex} \item{bare\_jrn24. tex} \item{bare\_jrn25. tex} \item{bare\_jrn26. tex} \item{bare\_jrn27. tex} \item{bare\_jrn9. tex} \item{bare\_jrn91. tex} \item{bare\_jrn92. tex} \item{bare\_jrn93. tex} \item{bare\_jrn94. tex} \item{bare\_jrn95. tex} \item{bare\_jrn96. tex} \item{bare\_jrn97. tex} \item{bare\_jrn98. tex}

\item\{bare\_conf\_compsoc.tex\} \item\{bare\_jrnl\_comsoc.tex\} \end{enumerate}

# A simple bulleted list

- bare_jrnl.tex- bare_conf.tex- bare_jrnl_compsoc.tex- bare_conf_compsoc.tex- bare_jrnl_comsoc.tex

coded as:

\begin{itemize} \item\{bare\_jrnl.tex\} \item\{bare\_conf.tex\} \item\{bare\_jrnl\_compsoc.tex\} \item\{bare\_conf\_compsoc.tex\} \item\{bare\_jrnl\_comsoc.tex\} \end{itemize}

# G. Other Elements

For other less common elements such as Algorithms, Theorems and Proofs, and Floating Structures such as pagewide tables, figures or equations, please refer to the "IEEEtran_HOWTO.pdf" section on "Double Column Floats."

# IX. HOW TO CREATE COMMON BACK MATTER ELEMENTS

The following sections demonstrate common back matter elements such as Acknowledgments, Bibliographies, Appendicies and Author Biographies.

# A. Acknowledgments

This should be a simple paragraph before the bibliography to thank those individuals and institutions who have supported your work on this article.

\section{Acknowledgments} \noindent Text describing those who supported your paper.

# B. Bibliographies

References Simplified: A simple way of composing references is to use the \bibitem macro to define the beginning of a reference as in the following examples:

[6] H. Sira- Ramirez. "On the sliding mode control of nonlinear systems," Systems & Control Letters, vol. 19, pp. 303- 312, 1992.

\bibitem\{Sira3\} H. Sira- Ramirez. On the sliding mode control of nonlinear systems, \textit{Systems \& Control Letters}, vol. 19, pp. 303- 312, 1992.

[7] A. Levant. "Exact differentiation of signals with unbounded higher derivatives," in Proceedings of the 45th IEEE Conference on Decision and Control, San Diego, California, USA, pp. 5585- 5590, 2006.

\bibitem\{Levant\}

A. Levant. \Exact differentiation of signals with unbounded higher derivatives,' in \textit{Proceedings of the 45th IEEE Conference on Decision and Control}, San Diego, California, USA, pp. 5585-5590, 2006.

[8] M. Fliess, C. Join, and H. Sira- Ramirez. "Non- linear estimation is easy," International Journal of Modelling, Identification and Control, vol. 4, no. 1, pp. 12- 27, 2008.

\bibitem\{Cedric\} M. Fliess, C. Join, and H. Sira- Ramirez. \Non- linear estimation is easy, \textit{International Journal of Modelling, Identification and Control}, vol. 4, no. 1, pp. 12- 27, 2008.

[9] R. Ortega, A. Astolfi, G. Bastin, and H. Rodriguez. "Stabilization of food- chain systems using a port- controlled Hamiltonian description," in Proceedings of the American Control Conference, Chicago, Illinois, USA, pp. 2245- 2249, 2000.

coded as:

\bibitem\{Ortega\} R. Ortega, A. Astolfi, G. Bastin, and H. Rodriguez. \Stabilization of food- chain systems using a port- controlled Hamiltonian description, in \textit{Proceedings of the American Control Conference}, Chicago, Illinois, USA, pp. 2245- 2249, 2000.

# C. Accented Characters in References

When using accented characters in references, please use the standard LaTeX coding for accents. Do not use math coding for character accents. For example:

$$
\backslash^{\prime}\in ,\backslash^{\prime \prime}\circ ,\backslash^{\prime}\mathsf{a},\backslash^{\sim}\mathsf{e}
$$

will produce: e, o, a, e

# D. Use of BibTeX

If you wish to use BibTeX, please see the documentation that accompanies the IEEEtran Bibliography package.

# E. Biographies and Author Photos

Authors may have options to include their photo or not. Photos should be a bit- map graphic (.tif or .jpg) and sized to fit in the space allowed. Please see the coding samples below:

\begin{title} \textit{IEEEbiographynophoto}\{Jane Doe\} \end{title}

Biography text here without a photo. \end{IEEEbiographynophoto}

# or a biography with a photo

\begin{IEEEbiography}[{\includegraphics [width=1in,height=1.25in,clip, keepaspectratio]{fig1. png}}] {IEEE Publications Technology Team} In this paragraph you can place your educational, professional background and research and other interests. \end{IEEEbiography}

Please see the end of this document to see the output of these coding examples.

# X. MATHEMATICAL TYPOGRAPHY AND WHY IT MATTERS

Typographical conventions for mathematical formulas have been developed to provide uniformity and clarity of presentation across mathematical texts. This enables the readers of those texts to both understand the author's ideas and to grasp new concepts quickly. While software such as  $\mathrm{E}\mathrm{TeX}$  and MathType can produce aesthetically pleasing math when used properly, it is also very easy to misuse the software, potentially resulting in incorrect math display.

IEEE aims to provide authors with the proper guidance on mathematical typesetting style and assist them in writing the best possible article.

As such, IEEE has assembled a set of examples of good and bad mathematical typesetting. You will see how various issues are dealt with. The following publications have been referenced in preparing this material:

Mathematics into Type, published by the American Mathematical Society The Printing of Mathematics, published by Oxford University Press The  $\mathrm{E}\mathrm{TeX}$  Companion, by F. Mittelbach and M. Goossens More Math into LaTeX, by G. Gratzer AMS- StyleGuide- online.pdf, published by the American Mathematical Society

Further examples can be seen at http://journals. ieeeauthorcenter.ieee.org/wp- content/uploads/sites/7/ IEEE- Math- Typesetting- Guide.pdf

# A. Display Equations

A simple display equation example shown below uses the "equation" environment. To number the equations, use the \label macro to create an identifier for the equation. LaTeX will automatically number the equation for you.

$$
x = \sum_{i = 0}^{n}2iQ. \tag{1}
$$

is coded as follows:

\begin{equation} \label \{deqn_ex1\} \end{equation}

$\texttt{x} = \backslash \texttt{sum\_}\{\texttt{i} = 0\} \hat{\texttt{\textbf{\textsf{n}}}}\{n\} 2\{\texttt{i}\} \texttt{Q}.$  \end{equation}

To reference this equation in the text use the \ref macro. Please see (1) is coded as follows:

Please see  $\langle \backslash \texttt{ref}\{\texttt{deqn\_ex1}\} \rangle$

# B. Equation Numbering

Consecutive Numbering: Equations within an article are numbered consecutively from the beginning of the article to the end, i.e., 1, 2, 3, 4, 5, etc. Do not use roman numerals or section numbers for equation numbering.

Appendix Equations: The continuation of consecutively numbered equations is best in the Appendix, but numbering as A1A2),etc.,is permissible.

Hyphens and Periods: Hyphens and periods should not be used in equation numbers, i.e., use 1a) rather than 1- a) and 2a) rather than 2. a) for sub- equations. This should be consistent throughout the article.

# C. Multi-line equations and alignment

Here we show several examples of multi- line equations and proper alignments.

A single equation that must break over multiple lines due to length with no specific alignment.

The first line of this example

The second line of this example

The third line of this example (2)

is coded as:

\begin{multline} \text{The first line of this example}\text{The second line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The third line of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline of this example}\text{The thirdline}\end{align*}

A single equation with multiple lines aligned at the  $=$  signs

$$
\begin{array}{l}{a=c+d}\\{b=e+f}\end{array} \tag{3}
$$

is coded as:

\begin{align} \text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align}\text{align} \end{align}

The align environment can align on multiple points as shown in the following example:

$$
\begin{array}{lll}{x=y}&{X=Y}&{a=bc}\\{x'=y'}&{X'=Y'}&{a'=bz}\end{array} \tag{6}
$$

is coded as:

\begin{align}  $\texttt{x}\& = \texttt{y}\& \texttt{X}\& = \texttt{Y}\& \texttt{a}\& = \texttt{bc}\backslash \backslash$ $\times^{\prime}\& = \mathrm{y}^{\prime}\& \mathrm{X}^{\prime}\& = \mathrm{Y}^{\prime}\& \mathrm{a}^{\prime}\& = \mathrm{bz}$  \end{align}

# D. Subnumbering

The amsmath package provides a subequations environment to facilitate subnumbering. An example:

$$
\begin{array}{c}{f=g}\\{f^{\prime}=g^{\prime}}\\{\mathcal{L}f=\mathcal{L}g}\end{array} \tag{7b}
$$

is coded as:

\begin{subequations} \label{eq:2} \begin{align} f&=g \label{eq:2A} \\\textbf{f}&=g' \label{eq:2B} \\\textbf{\mathcal{L}}\end{align} \end{subequation}

# E. Matrices

There are several useful matrix environments that can save you some keystrokes. See the example coding below and the output.

A simple matrix:

$$
\begin{array}{cc}{0}&{1}\\{1}&{0}\end{array} \tag{8}
$$

is coded as:

\begin{equation} \begin{matrix} \texttt{V}\texttt{b}\texttt{e}\texttt{g}\texttt{i}\texttt{n}\texttt{g}\texttt{t}\texttt{i}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{b}\texttt{e}\texttt{g}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{e}\texttt{g}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{e}\texttt{g}\texttt{e}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{u}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{v}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{w}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{h}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{c}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{r}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{z}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{ t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tt}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tr}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tot}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{to}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{p}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{f}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{a}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{s}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{y}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{}}\end{array}\)

A matrix with parenthesis

$$
\left( \begin{array}{cc}0 & -i \\ i & 0 \end{array} \right) \tag{9}
$$

is coded as:

\begin{equation} \begin{matrix} \texttt{V}\texttt{b}\texttt{e}\texttt{g}\texttt{i}\texttt{n}\texttt{g}\texttt{t}\texttt{a}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{e}\texttt{g}\texttt{a}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{e}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{e}\texttt{e}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tt}\texttt{tt}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tt}\texttt{e}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{u}\texttt{tt}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{u}\texttt{u}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tt}\texttt{u}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{u}\texttt{v}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tt}\texttt{v}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{v}\texttt{v}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{u}\texttt{r}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{u}\texttt{p}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{u}\texttt{b}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{dt}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{d}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{ts}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{td}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{th}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{st}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tn}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tv}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{ty}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tc}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{te}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tw}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tp}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tf}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tb}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tx}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{ti}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{it}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{lt}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{ht}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{tl}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{ll}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{l}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{L}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{I}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{H}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{[t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{T}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{T}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{~t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}\texttt{t}
\]

# A matrix with curly braces

$$
\left\{ \begin{array}{ll}1 & 0 \\ 0 & -1 \end{array} \right\} \tag{11}
$$

is coded as:

\begin{equation} \begin{array}{c} \begin{array}{c} \texttt{begin}\{\texttt{equation}\} \\ \texttt{begin}\{\texttt{matrix}\} \texttt{a}\& \texttt{b} \backslash \backslash \\ \texttt{c}\& \texttt{d} \backslash \texttt{end}\{\texttt{matrix}\} \\ \texttt{end}\{\texttt{equation}\} \end{array} \end{array} \tag{12}
\]

\begin{equation} \begin{array}{c} \begin{array}{c} \texttt{begin}\{\texttt{equation}\} \\ \texttt{begin}\{\texttt{matrix}\} \texttt{i}\& \texttt{0} \backslash \backslash \\ \texttt{0}\& \texttt{- i} \backslash \texttt{end}\{\texttt{Vmatrix}\} \end{array} \end{array} \tag{13} \end{array} \tag{14}
\]

is coded as:

\begin{equation} \begin{array}{c} \begin{array}{c} \texttt{begin}\{\texttt{equation}\} \\ \texttt{begin}\{\texttt{matrix}\} \texttt{a}\& \texttt{b} \backslash \backslash \\ \texttt{c}\& \texttt{d} \backslash \texttt{end}\{\texttt{Vmatrix}\} \end{array} \end{array} \tag{13}
\]

\begin{equation} \begin{array}{c} \begin{array}{c} \texttt{begin}\{\texttt{equation}\} \\ \texttt{begin}\{\texttt{matrix}\} \texttt{i}\& \texttt{0} \backslash \backslash \\ \texttt{0}\& \texttt{- i} \backslash \texttt{end}\{\texttt{matrix}\} \\ \texttt{end}\{\texttt{equation}\} \end{array} \end{array} \tag{13}
\]

A simple array

\begin{equation} \begin{array}{c} \begin{array}{c} a + b + c \\ a + b \\ u + v \\ z \\ 134 \end{array} \end{array} \end{array} \tag{14}
\]

F. Arrays

The array environment allows you some options for matrix- like equations. You will have to manually key the fences, but you'll have options for alignment of the columns and for setting horizontal and vertical rules. The argument to array controls alignment and placement of vertical rules.

A simple array

$$
\begin{array}{r}\left( \begin{array}{ccc}a + b + c & uv & x - y & 27 \\ a + b & uv & x - y & 27 \\ a + b & uv & x - y & 27 \\ a + b & uv & x - y & 27 \\ a + b & uv & x - y & 27 \\ a + b & uv & x - y & 27 \\ a + b & uv & x - y & 27 \\ a + b & uv & x - y & 27 \\ a + b & u + v & z & 134 \end{array} \right) \tag{15}
$$

is coded as:

\begin{equation} \begin{array}{c} \begin{array}{c} a + b + c \\ a + b \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ IV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ uv \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ Iv \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ iv \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\  IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV\\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV}\\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \end{array} \end{array} \tag{15}
\]

\begin{equation} \begin{array}{c} \begin{array}{c} a + b + c \\ a + b \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ Iv \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ iv \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\  IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV\\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV\\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV\\ IV \\ IV \\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV}\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV\\ IV \end{array} \end{array} \tag{16}
\]

A slight variation on this to better align the numbers in the last column

$$
\begin{array}{r}( \begin{array}{c}a + b + c \\ a + b \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ UV \\ UV \\ UV \\ UV \\ uv \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ IV \\ IV \\ IV \\ IV \\ IV \\ UV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\  I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I\\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \\ I \end{array} \end{array} \tag{17}
$$

\begin{equation} \begin{array}{c} \begin{array}{c} a + b + c \\ a + b \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ uv \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ UV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ Iv \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ I \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\  IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV\\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \\ IV \end{array} \end{array} \tag{18}
\]

\begin{array}{c}\texttt{array}\{cccc\} \\a+b+c\& uv\& x- y\& 27\\a+b\& u+v\& z\& 134\\end\{array\} \end\texttt{array}

An array with vertical and horizontal rules

$$
\left(\frac{a + b + c\mid uv\mid x - y\mid 27}{a + b\mid a + v\mid z\mid 134}\right) \tag{16}
$$

is coded as:

\begin{array}{c}\texttt{equation}\} \\\texttt{array}\{c|c|c|r\} \\a+b+c\& uv\& x- y\& 27\\a+b\& u+v\& z\& 134\\end\{array\} \end\texttt{array}

Note the argument now has the pipe  $\because$  included to indicate the placement of the vertical rules.

# G. Cases Structures

Many times we find cases coded using the wrong environment, i.e., array. Using the cases environment will save keystrokes (from not having to type the \left\{left\brace) and automatically provide the correct column alignment.

$$
z_{m}(t) = \left\{ \begin{array}{ll}1, & \mathrm{if} \beta_{m}(t)\\ 0, & \mathrm{otherwise}. \end{array} \right.
$$

is coded as follows:

\begin{array}{c}\texttt{equation}\} \\z\_ m(t)\} = \\\texttt{array}\{cases\} \\1,\& \{\texttt{text}\{if\}\} \backslash \{\texttt{beta}\} \_ m(t),\backslash \backslash \\\{0,\} \& \{\texttt{text}\{otherwise.\} \} \\end\{cases\} \\end\{equation\* \} \end

Note that the  $\&$  is used to mark the tabular alignment. This is important to get proper column alignment. Do not use \quad or other fixed spaces to try and align the columns. Also, note the use of the \text macro for text elements such as "if" and "otherwise".

# H. Function Formatting in Equations

In many cases there is an easy way to properly format most common functions. Use of the \ in front of the function name will in most cases, provide the correct formatting. When this does not work, the following example provides a solution using the \text macro.

$$
d_R^{KM} = \underset {d_{b}^{KM}}{\arg \min}\{d_1^{KM},\ldots ,d_6^{KM}\} .
$$

is coded as follows:

\begin{array}{c}\texttt{equation}\} \end{array}

d_{\texttt{R}}\{KM\} = \texttt{underset{\texttt{d}_{\texttt{R}}\{1\}}{\texttt{K}}\{KM\} \} \{text\{arg\min\} \} \backslash \{d_{\texttt{R}}\{1\} \texttt{K} \{KM\} \} \texttt{K} \{KM\} \} \texttt{K} \{KM\} \} \texttt{K} \{KM\} \} \texttt{K} \{KM\} \} \texttt{K} \{KM\} \} \texttt{K} \{KM\} \} \texttt{K} \{KM\} \} \texttt{K} \texttt{K} \{KM\} \} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \textit{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} \texttt{K} 
\]

# I. Text Acronyms inside equations

This example shows where the acronym "MSE" is coded using \text $\}$  to match how it appears in the text.

$$
\mathrm{MSE} = \frac{1}{n}\sum_{i = 1}^{n}(Y_{i} - \hat{Y_{i}})^{2}
$$

\begin{array}{c}\texttt{equation}\} \\text\{MSE\} = \texttt{frac}\{1\} \{n\} \texttt{sum\_i = 1\} \texttt{\{n\}} \\(Y_{i}\{i\} - \texttt{hat}\{Y_{i}\{i\}\} \texttt{\{2\}} \\end\{equation\* \} \end\} \end\}

# J. Obsolete Coding

Avoid the use of outdated environments, such as eqnarray and \(\) \(\mathbb{S}\) math delimiters, for display equations. The \(\) \(\mathbb{S}\(display math delimiters are left over from PlainTeX and should not be used in LTEX, ever. Poor vertical spacing will result.

# K. Use Appropriate Delimiters for Display Equations

Some improper mathematical coding advice has been given in various YouTube™ videos on how to write scholarly articles, so please follow these good examples:

For single- line unnumbered display equations, please use the following delimiters:

\[\texttt{begin\{equation\*}\ldots\end\{equation\* \}}\]

Note that the \* in the environment name turns off equation numbering.

For multiline unnumbered display equations that have alignment requirements, please use the following delimiters:

\begin{align*} \end\{align* \}

For single- line numbered display equations, please use the following delimiters:

\begin{equation\} \ldots \end\{equation\}

For multiline numbered display equations, please use the following delimiters:

\begin{align*} \end\{align\}

# XI. LATEX PACKAGE SUGGESTIONS

Immediately after your documenttype declaration at the top of your LTEX file is the place where you should declare any packages that are being used. The following packages were used in the production of this document.

\usepackage\amsmath,amsfonts\usepackage\algorithmic

\usepackage{array} \usepackage{caption=false,font=normalsize, labelfont=sf,textfont=sf}{subfig} \u00sepackage{textcomp} \usepackage{stfl oats} \usepackage{url} \usepackage{verbatim} \usepackage{graphicx} \usepackage{balance}

# XII. ADDITIONAL ADVICE

Please use softe.g.,\egref{Eq}) or \ref{Eq}) cross references instead of hard' references e.g.,1)).That will make it possible to combine sections, add equations, or change the order of figures or citations without having to go through the file line by line.

Please note that the  $\{\mathtt{subequations}\}$  environment in  $\mathrm{LATEX}$  will increment the main equation counter even when there are no equation numbers displayed. If you forget that, you might write an article in which the equation numbers skip from (17) to (20), causing the copy editors to wonder if you've discovered a new method of counting.

$\mathrm{BIBTEX}$  does not work by magic. It doesn't get the bibliographic data from thin air but from .bib files. If you use  $\mathrm{BIBTEX}$  to produce a bibliography you must send the .bib files.

$\mathrm{LATEX}$  can't read your mind. If you assign the same label to a subsubsection and a table, you might find that Table I has been cross referenced as Table IV- B3.

$\mathrm{LATEX}$  does not have precognitive abilities. If you put a \label command before the command that updates the counter it's supposed to be using, the label will pick up the last counter to be cross referenced instead. In particular, a \label command should not go before the caption of a figure or a table.

Please do not use  $\lambda$  number or  $\lambda$  notag inside the {array} environment. It will not stop equation numbers inside {array} (there won't be any anyway) and it might stop a wanted equation number in the surrounding equation.

# XIII. A FINAL CHECKLIST

1) Make sure that your equations are numbered sequentially and there are no equation numbers missing or duplicated.

Avoid hyphens and periods in your equation numbering. Stay with IEEE style, i.e., 1),2)3 or for subequations (1a), (1b). For equations in the appendix (A1), A2,etc. 2) Are your equations properly formatted? Text, functions, alignment points in cases and arrays, etc. 3) Make sure all graphics are included. 4) Make sure your references are included either in your main LaTeX file or a separate .bib file if calling the external file.

# REFERENCES

[1] Mathematics into Type, American Mathematical Society. Online available: [2] T.W. Chaundy, P.R. Barrett and C. Batey, The Printing of Mathematics, Oxford University Press. London, 1954. [3] The BTEXCompanion, by F. Mittelbach and M. Goossens [4] More Math into LaTeX, by G. Gratzer [5] AMS- StyleGuide- online.pdf, published by the American Mathematical Society [6] H. Sira- Ramirez. "On the sliding mode control of nonlinear systems," Systems & Control Letters, vol. 19, pp. 303- 312, 1992. [7] A. Levant. "Exact differentiation of signals with unbounded higher derivatives," in Proceedings of the 45th IEEE Conference on Decision and Control, San Diego, California, USA, pp. 5585- 5590, 2006. [8] M. Fliess, C. Join, and H. Sira- Ramirez. "Non- linear estimation is easy," International Journal of Modelling, Identification and Control, vol. 4, no. 1, pp. 12- 27, 2008. [9] R. Ortega, A. Astolfi, G. Bastin, and H. Rodriguez. "Stabilization of food- chain systems using a port- controlled Hamiltonian description," in Proceedings of the American Control Conference, Chicago, Illinois, USA, pp. 2245- 2249, 2000.

Jane Doe Biography text here without a photo.

![](images/98646f48b6f195d928efdf46aa263709b9bf2362e64e6c9d7efa03aa7982e043.jpg)

IEEE Publications Technology Team In this paragraph you can place your educational, professional background and research and other interests.