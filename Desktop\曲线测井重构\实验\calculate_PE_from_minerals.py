#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于矿物成分计算光电因子(PE)
根据DRSN-GAF论文要求，计算PE参数以完善五参数测井数据组合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def calculate_PE_from_minerals(clay, feldspar, quartz, calcite, dolomite, pyrite):
    """
    基于矿物成分计算光电因子(PE)
    
    参数:
        clay: 黏土矿物含量 (%)
        feldspar: 斜长石含量 (%)  
        quartz: 石英含量 (%)
        calcite: 方解石含量 (%)
        dolomite: 铁白云石含量 (%)
        pyrite: 黄铁矿含量 (%)
    
    返回:
        PE: 光电因子 (barns/electron)
    
    参考PE值:
        - 石英: 1.81
        - 黏土矿物: 3.0-3.3 (平均3.15)
        - 斜长石: 2.8
        - 方解石: 5.08
        - 白云石: 3.14
        - 黄铁矿: 16.97
    """
    
    # 标准矿物PE值 (barns/electron)
    PE_values = {
        'quartz': 1.81,
        'clay': 3.15,  # 黏土矿物平均值
        'feldspar': 2.8,  # 斜长石
        'calcite': 5.08,
        'dolomite': 3.14,  # 铁白云石近似白云石
        'pyrite': 16.97
    }
    
    # 确保输入为numpy数组
    clay = np.array(clay)
    feldspar = np.array(feldspar) 
    quartz = np.array(quartz)
    calcite = np.array(calcite)
    dolomite = np.array(dolomite)
    pyrite = np.array(pyrite)
    
    # 计算总矿物含量（用于归一化）
    total_minerals = clay + feldspar + quartz + calcite + dolomite + pyrite
    
    # 避免除零错误
    total_minerals = np.where(total_minerals == 0, 1, total_minerals)
    
    # 归一化矿物含量到100%
    clay_norm = clay / total_minerals * 100
    feldspar_norm = feldspar / total_minerals * 100
    quartz_norm = quartz / total_minerals * 100
    calcite_norm = calcite / total_minerals * 100
    dolomite_norm = dolomite / total_minerals * 100
    pyrite_norm = pyrite / total_minerals * 100
    
    # 计算加权平均PE值
    PE = (clay_norm * PE_values['clay'] + 
          feldspar_norm * PE_values['feldspar'] +
          quartz_norm * PE_values['quartz'] + 
          calcite_norm * PE_values['calcite'] +
          dolomite_norm * PE_values['dolomite'] +
          pyrite_norm * PE_values['pyrite']) / 100
    
    return PE

def main():
    """主函数：读取数据，计算PE，保存结果"""
    
    print("🔬 开始基于矿物成分计算光电因子(PE)...")
    
    # 读取数据
    input_file = 'Desktop/曲线测井重构/实验/data/daqin_with_lithology.csv'
    df = pd.read_csv(input_file)
    
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名: {df.columns.tolist()}")
    
    # 提取矿物成分数据
    clay = df['黏土矿物（）'].values
    feldspar = df['斜长石（）'].values
    quartz = df['石英（）'].values
    calcite = df['方解石（）'].values
    dolomite = df['铁白云石（）'].values
    pyrite = df['黄铁矿（）'].values
    
    # 计算PE
    print("⚡ 正在计算光电因子...")
    PE = calculate_PE_from_minerals(clay, feldspar, quartz, calcite, dolomite, pyrite)
    
    # 添加PE列到数据框
    df['PE_calculated'] = PE
    
    # 统计信息
    print(f"\n📈 PE计算结果统计:")
    print(f"   最小值: {PE.min():.3f}")
    print(f"   最大值: {PE.max():.3f}")
    print(f"   平均值: {PE.mean():.3f}")
    print(f"   标准差: {PE.std():.3f}")
    
    # 检查PE值的合理性
    reasonable_range = (PE >= 1.5) & (PE <= 6.0)  # 常见沉积岩PE范围
    print(f"   合理范围内的数据点: {reasonable_range.sum()}/{len(PE)} ({reasonable_range.mean()*100:.1f}%)")
    
    # 保存带PE的完整数据
    output_file = 'Desktop/曲线测井重构/实验/data/daqin_with_PE.csv'
    df.to_csv(output_file, index=False)
    print(f"💾 已保存带PE的完整数据到: {output_file}")
    
    # 创建DRSN-GAF五参数数据集
    five_params_df = df[['深度', '自然伽马', '声波时差', '补偿中子', '岩性密度', 'PE_calculated', '岩性', '岩性编码']].copy()
    five_params_df.columns = ['Depth', 'GR', 'AC', 'CNL', 'DEN', 'PE', 'Lithology', 'Lithology_Code']
    
    five_params_file = 'Desktop/曲线测井重构/实验/data/daqin_five_params_DRSN_style.csv'
    five_params_df.to_csv(five_params_file, index=False)
    print(f"🎯 已创建DRSN-GAF风格五参数数据集: {five_params_file}")
    
    # 可视化PE分布
    create_PE_visualization(df, PE)
    
    print("✅ PE计算完成！")
    
    return df, PE

def create_PE_visualization(df, PE):
    """创建PE数据的可视化图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    
    # 1. PE分布直方图
    axes[0, 0].hist(PE, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_xlabel('PE (barns/electron)')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('PE Distribution')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. PE vs 深度
    axes[0, 1].plot(PE, df['深度'], 'b-', linewidth=0.5, alpha=0.7)
    axes[0, 1].set_xlabel('PE (barns/electron)')
    axes[0, 1].set_ylabel('Depth (m)')
    axes[0, 1].set_title('PE vs Depth')
    axes[0, 1].invert_yaxis()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 不同岩性的PE分布
    lithologies = df['岩性'].unique()
    colors = ['red', 'blue', 'green', 'orange', 'purple']
    for i, lith in enumerate(lithologies):
        mask = df['岩性'] == lith
        if mask.sum() > 0:
            axes[1, 0].hist(PE[mask], bins=30, alpha=0.6, 
                           label=lith, color=colors[i % len(colors)])
    
    axes[1, 0].set_xlabel('PE (barns/electron)')
    axes[1, 0].set_ylabel('Frequency')
    axes[1, 0].set_title('PE Distribution by Lithology')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. PE vs 石英含量关系
    axes[1, 1].scatter(df['石英（）'], PE, alpha=0.6, s=1)
    axes[1, 1].set_xlabel('Quartz Content (%)')
    axes[1, 1].set_ylabel('PE (barns/electron)')
    axes[1, 1].set_title('PE vs Quartz Content')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    output_path = 'Desktop/曲线测井重构/实验/PE_analysis.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"📊 PE分析图表已保存到: {output_path}")
    
    plt.show()

if __name__ == "__main__":
    df, PE = main()
