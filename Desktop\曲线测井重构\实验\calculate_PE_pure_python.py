#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于矿物成分计算光电因子(PE) - 纯Python版本
不依赖任何外部库，只使用Python内置模块
"""

import csv
import math

def calculate_PE_from_minerals(clay, feldspar, quartz, calcite, dolomite, pyrite):
    """
    基于矿物成分计算光电因子(PE)
    
    参数:
        clay: 黏土矿物含量 (%)
        feldspar: 斜长石含量 (%)  
        quartz: 石英含量 (%)
        calcite: 方解石含量 (%)
        dolomite: 铁白云石含量 (%)
        pyrite: 黄铁矿含量 (%)
    
    返回:
        PE: 光电因子 (barns/electron)
    """
    
    # 标准矿物PE值 (barns/electron)
    PE_values = {
        'quartz': 1.81,
        'clay': 3.15,  # 黏土矿物平均值
        'feldspar': 2.8,  # 斜长石
        'calcite': 5.08,
        'dolomite': 3.14,  # 铁白云石近似白云石
        'pyrite': 16.97
    }
    
    # 计算总矿物含量（用于归一化）
    total_minerals = clay + feldspar + quartz + calcite + dolomite + pyrite
    
    # 避免除零错误
    if total_minerals == 0:
        total_minerals = 1
    
    # 归一化矿物含量到100%
    clay_norm = clay / total_minerals * 100
    feldspar_norm = feldspar / total_minerals * 100
    quartz_norm = quartz / total_minerals * 100
    calcite_norm = calcite / total_minerals * 100
    dolomite_norm = dolomite / total_minerals * 100
    pyrite_norm = pyrite / total_minerals * 100
    
    # 计算加权平均PE值
    PE = (clay_norm * PE_values['clay'] + 
          feldspar_norm * PE_values['feldspar'] +
          quartz_norm * PE_values['quartz'] + 
          calcite_norm * PE_values['calcite'] +
          dolomite_norm * PE_values['dolomite'] +
          pyrite_norm * PE_values['pyrite']) / 100
    
    return PE

def calculate_statistics(values):
    """计算基本统计量"""
    if not values:
        return 0, 0, 0, 0
    
    n = len(values)
    mean_val = sum(values) / n
    min_val = min(values)
    max_val = max(values)
    
    # 计算标准差
    variance = sum((x - mean_val) ** 2 for x in values) / n
    std_val = math.sqrt(variance)
    
    return min_val, max_val, mean_val, std_val

def main():
    """主函数：读取数据，计算PE，保存结果"""
    
    print("🔬 开始基于矿物成分计算光电因子(PE)...")
    
    # 读取数据
    input_file = '../实验/data/daqin_with_lithology.csv'
    
    data = []
    headers = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            headers = next(reader)  # 读取表头
            
            for row in reader:
                data.append(row)
        
        print(f"📊 数据形状: {len(data)} 行, {len(headers)} 列")
        print(f"📋 列名: {headers}")
        
        # 找到矿物成分列的索引
        clay_idx = headers.index('黏土矿物（）')
        feldspar_idx = headers.index('斜长石（）')
        quartz_idx = headers.index('石英（）')
        calcite_idx = headers.index('方解石（）')
        dolomite_idx = headers.index('铁白云石（）')
        pyrite_idx = headers.index('黄铁矿（）')
        
        # 计算PE
        print("⚡ 正在计算光电因子...")
        PE_values = []
        error_count = 0
        
        for i, row in enumerate(data):
            try:
                clay = float(row[clay_idx])
                feldspar = float(row[feldspar_idx])
                quartz = float(row[quartz_idx])
                calcite = float(row[calcite_idx])
                dolomite = float(row[dolomite_idx])
                pyrite = float(row[pyrite_idx])
                
                PE = calculate_PE_from_minerals(clay, feldspar, quartz, calcite, dolomite, pyrite)
                PE_values.append(PE)
                
            except (ValueError, IndexError) as e:
                print(f"警告: 第{i+2}行数据处理错误: {e}")
                PE_values.append(0.0)  # 默认值
                error_count += 1
        
        # 统计信息
        min_pe, max_pe, mean_pe, std_pe = calculate_statistics(PE_values)
        
        print(f"\n📈 PE计算结果统计:")
        print(f"   最小值: {min_pe:.3f}")
        print(f"   最大值: {max_pe:.3f}")
        print(f"   平均值: {mean_pe:.3f}")
        print(f"   标准差: {std_pe:.3f}")
        print(f"   错误数据点: {error_count}")
        
        # 检查PE值的合理性
        reasonable_count = sum(1 for pe in PE_values if 1.5 <= pe <= 6.0)
        reasonable_percent = reasonable_count / len(PE_values) * 100 if PE_values else 0
        print(f"   合理范围内的数据点: {reasonable_count}/{len(PE_values)} ({reasonable_percent:.1f}%)")
        
        # 保存带PE的完整数据
        output_file = '../实验/data/daqin_with_PE.csv'
        
        # 添加PE列到表头
        new_headers = headers + ['PE_calculated']
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(new_headers)
            
            for i, row in enumerate(data):
                new_row = row + [f"{PE_values[i]:.6f}"]
                writer.writerow(new_row)
        
        print(f"💾 已保存带PE的完整数据到: {output_file}")
        
        # 创建DRSN-GAF五参数数据集
        depth_idx = headers.index('深度')
        gr_idx = headers.index('自然伽马')
        ac_idx = headers.index('声波时差')
        cnl_idx = headers.index('补偿中子')
        den_idx = headers.index('岩性密度')
        lith_idx = headers.index('岩性')
        lith_code_idx = headers.index('岩性编码')
        
        five_params_file = '../实验/data/daqin_five_params_DRSN_style.csv'
        five_params_headers = ['Depth', 'GR', 'AC', 'CNL', 'DEN', 'PE', 'Lithology', 'Lithology_Code']
        
        with open(five_params_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(five_params_headers)
            
            for i, row in enumerate(data):
                five_params_row = [
                    row[depth_idx],      # Depth
                    row[gr_idx],         # GR
                    row[ac_idx],         # AC
                    row[cnl_idx],        # CNL
                    row[den_idx],        # DEN
                    f"{PE_values[i]:.6f}",  # PE
                    row[lith_idx],       # Lithology
                    row[lith_code_idx]   # Lithology_Code
                ]
                writer.writerow(five_params_row)
        
        print(f"🎯 已创建DRSN-GAF风格五参数数据集: {five_params_file}")
        
        # 显示前几行数据作为验证
        print(f"\n📋 五参数数据集预览:")
        print("Depth, GR, AC, CNL, DEN, PE, Lithology, Lithology_Code")
        
        with open(five_params_file, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for i, row in enumerate(reader):
                if i < 5:  # 只显示前5行
                    print(", ".join(row))
                else:
                    break
        
        # 按岩性分析PE分布
        print(f"\n📊 不同岩性的PE分布:")
        lithology_pe = {}
        
        for i, row in enumerate(data):
            lith = row[lith_idx]
            pe = PE_values[i]
            
            if lith not in lithology_pe:
                lithology_pe[lith] = []
            lithology_pe[lith].append(pe)
        
        for lith, pe_list in lithology_pe.items():
            min_pe, max_pe, mean_pe, std_pe = calculate_statistics(pe_list)
            print(f"   {lith}: 平均PE={mean_pe:.3f}, 标准差={std_pe:.3f}, 样本数={len(pe_list)}")
        
        print("✅ PE计算完成！")
        
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {input_file}")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
