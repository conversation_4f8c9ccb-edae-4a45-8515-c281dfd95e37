#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为大庆数据集添加矿物总和列
在六个矿物成分后面添加一个新列显示它们的总和
"""

import pandas as pd
import numpy as np

def add_mineral_sum_column(input_file, output_file):
    """
    为CSV文件添加矿物总和列
    
    参数:
    - input_file: 输入CSV文件路径
    - output_file: 输出CSV文件路径
    """
    
    print("🔄 开始为数据添加矿物总和列...")
    
    # 读取数据
    print(f"📖 读取数据: {input_file}")
    df = pd.read_csv(input_file, encoding='utf-8')
    
    print(f"📊 原始数据统计:")
    print(f"   - 总行数: {len(df):,}")
    print(f"   - 总列数: {len(df.columns)}")
    print(f"   - 数据形状: {df.shape}")
    
    # 定义矿物成分列
    mineral_columns = [
        '黏土矿物（）', '斜长石（）', '石英（）', 
        '方解石（）', '铁白云石（）', '黄铁矿（）'
    ]
    
    print(f"\n🔍 矿物成分列:")
    for i, col in enumerate(mineral_columns, 1):
        print(f"   {i}. {col}")
    
    # 检查所有矿物列是否存在
    missing_columns = [col for col in mineral_columns if col not in df.columns]
    if missing_columns:
        print(f"❌ 错误：以下矿物列不存在: {missing_columns}")
        return False
    
    print(f"✅ 所有矿物列都存在")
    
    # 计算矿物总和
    print(f"\n📈 计算矿物总和...")
    df['矿物总和'] = df[mineral_columns].sum(axis=1)
    
    # 统计矿物总和
    print(f"📊 矿物总和统计:")
    print(f"   - 最小值: {df['矿物总和'].min():.3f}")
    print(f"   - 最大值: {df['矿物总和'].max():.3f}")
    print(f"   - 平均值: {df['矿物总和'].mean():.3f}")
    print(f"   - 标准差: {df['矿物总和'].std():.3f}")
    
    # 显示矿物总和分布
    print(f"\n📈 矿物总和分布分析:")
    
    # 统计不同范围的行数
    ranges = [
        (0, 50, "0-50"),
        (50, 100, "50-100"), 
        (100, 150, "100-150"),
        (150, 200, "150-200"),
        (200, 300, "200+")
    ]
    
    for min_val, max_val, label in ranges:
        if label == "200+":
            count = (df['矿物总和'] >= min_val).sum()
        else:
            count = ((df['矿物总和'] >= min_val) & (df['矿物总和'] < max_val)).sum()
        percentage = count / len(df) * 100
        print(f"   - {label}: {count:,} 行 ({percentage:.1f}%)")
    
    # 重新排列列的顺序，将矿物总和放在矿物列之后
    print(f"\n🔄 重新排列列顺序...")
    
    # 获取所有列名
    all_columns = df.columns.tolist()
    
    # 找到最后一个矿物列的位置
    last_mineral_col = '黄铁矿（）'
    last_mineral_index = all_columns.index(last_mineral_col)
    
    # 重新排列：矿物总和列放在最后一个矿物列之后
    new_columns = all_columns[:last_mineral_index + 1] + ['矿物总和'] + all_columns[last_mineral_index + 1:-1]
    
    # 重新排列DataFrame
    df_reordered = df[new_columns]
    
    print(f"✅ 列顺序调整完成")
    print(f"   - 矿物总和列位置: 第{new_columns.index('矿物总和') + 1}列")
    print(f"   - 总列数: {len(new_columns)}")
    
    # 显示前几行作为预览
    print(f"\n👀 数据预览 (前5行):")
    print("深度 | 矿物成分 | 矿物总和")
    print("-" * 60)
    
    for i in range(min(5, len(df_reordered))):
        depth = df_reordered.iloc[i]['深度']
        minerals = [f"{df_reordered.iloc[i][col]:.3f}" for col in mineral_columns]
        total = df_reordered.iloc[i]['矿物总和']
        print(f"{depth:.3f} | {' + '.join(minerals)} | {total:.3f}")
    
    # 保存结果
    print(f"\n💾 保存数据到: {output_file}")
    df_reordered.to_csv(output_file, index=False, encoding='utf-8')
    
    # 最终统计
    print(f"\n📈 最终数据统计:")
    print(f"   - 最终行数: {len(df_reordered):,}")
    print(f"   - 最终列数: {len(df_reordered.columns)}")
    print(f"   - 新增列: 矿物总和")
    print(f"   - 深度范围: {df_reordered['深度'].min():.3f} - {df_reordered['深度'].max():.3f} m")
    
    # 验证计算正确性
    print(f"\n🔍 验证计算正确性:")
    
    # 随机选择几行验证
    sample_indices = np.random.choice(len(df_reordered), min(3, len(df_reordered)), replace=False)
    
    for idx in sample_indices:
        row = df_reordered.iloc[idx]
        manual_sum = sum(row[col] for col in mineral_columns)
        calculated_sum = row['矿物总和']
        difference = abs(manual_sum - calculated_sum)
        
        print(f"   行{idx+1}: 手动计算={manual_sum:.6f}, 自动计算={calculated_sum:.6f}, 差异={difference:.6f}")
        
        if difference < 1e-10:
            print(f"        ✅ 计算正确")
        else:
            print(f"        ❌ 计算有误")
    
    return True

def main():
    """主函数"""
    
    # 文件路径
    input_file = "daqin_cleaned.csv"
    output_file = "daqin_with_mineral_sum.csv"
    
    try:
        # 执行添加矿物总和列
        success = add_mineral_sum_column(input_file, output_file)
        
        if success:
            print(f"\n🎉 矿物总和列添加完成！")
            print(f"📁 输入文件: {input_file}")
            print(f"📁 输出文件: {output_file}")
            print(f"✨ 新增功能: 在六个矿物成分后添加了矿物总和列")
            
            # 显示列结构
            df_result = pd.read_csv(output_file, encoding='utf-8')
            print(f"\n📋 最终列结构:")
            for i, col in enumerate(df_result.columns, 1):
                if '矿物' in col:
                    print(f"   {i:2d}. {col} ⭐")
                else:
                    print(f"   {i:2d}. {col}")
        else:
            print(f"\n❌ 处理失败")
            return False
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 大庆数据集矿物总和列添加成功完成！")
        print("🎯 现在可以直观看到每行的矿物成分总和")
    else:
        print("\n❌ 大庆数据集矿物总和列添加失败！")
