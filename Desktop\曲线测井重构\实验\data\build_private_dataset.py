#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大庆私有数据集构建脚本
基于真实矿物成分的岩性预测数据集构建

核心特色：
1. 基于矿物成分的科学岩性分类
2. 滑动窗口序列构建
3. 注意力稳定性验证数据生成
4. 地质一致性测试数据构建
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split

class PrivateDatasetBuilder:
    """私有数据集构建器"""
    
    def __init__(self, data_path):
        """
        初始化数据集构建器
        
        参数:
        - data_path: 归一化后的CSV文件路径
        """
        self.data_path = data_path
        self.data = None
        self.lithology_map = {
            '泥岩': 0,
            '粉砂岩': 1, 
            '细砂岩': 2,
            '中砂岩': 3,
            '粗砂岩': 4,
            '石灰岩': 5,
            '白云岩': 6
        }
        
    def load_data(self):
        """加载归一化后的数据"""
        print("📖 加载归一化数据...")
        self.data = pd.read_csv(self.data_path, encoding='utf-8')
        print(f"   - 数据形状: {self.data.shape}")
        print(f"   - 深度范围: {self.data['深度'].min():.3f} - {self.data['深度'].max():.3f} m")
        return self.data
    
    def classify_lithology_by_minerals(self):
        """
        基于矿物成分进行岩性分类
        参考Folk (1974)和Doveton (1994)的分类标准
        """
        print("\n🔬 基于矿物成分进行岩性分类...")
        
        # 提取矿物成分
        clay = self.data['黏土矿物（）'].values
        feldspar = self.data['斜长石（）'].values
        quartz = self.data['石英（）'].values
        calcite = self.data['方解石（）'].values
        dolomite = self.data['铁白云石（）'].values
        pyrite = self.data['黄铁矿（）'].values
        
        lithology_labels = []
        
        for i in range(len(self.data)):
            # 计算主要矿物比例
            silicate_total = clay[i] + feldspar[i] + quartz[i]
            carbonate_total = calcite[i] + dolomite[i]
            
            # 岩性分类逻辑 (基于地质学经典理论)
            if carbonate_total > 15:  # 碳酸盐岩
                if calcite[i] > dolomite[i]:
                    lithology = "石灰岩"
                else:
                    lithology = "白云岩"
            
            elif silicate_total > 70:  # 硅质岩
                if clay[i] > 40:  # 高黏土含量
                    lithology = "泥岩"
                elif clay[i] > 25:  # 中等黏土含量
                    lithology = "粉砂岩"
                else:  # 低黏土含量，按石英含量细分
                    if quartz[i] > 25:
                        lithology = "中砂岩"
                    elif quartz[i] > 20:
                        lithology = "细砂岩"
                    else:
                        lithology = "粗砂岩"
            else:
                # 混合岩性，按主要成分分类
                if clay[i] > 30:
                    lithology = "泥岩"
                else:
                    lithology = "细砂岩"
            
            lithology_labels.append(lithology)
        
        # 添加岩性标签到数据中
        self.data['岩性'] = lithology_labels
        self.data['岩性编码'] = [self.lithology_map[lith] for lith in lithology_labels]
        
        # 统计岩性分布
        lithology_counts = pd.Series(lithology_labels).value_counts()
        print(f"📊 岩性分布统计:")
        for lith, count in lithology_counts.items():
            percentage = count / len(lithology_labels) * 100
            print(f"   - {lith}: {count:,} 样本 ({percentage:.1f}%)")
        
        return lithology_labels
    
    def create_sequence_windows(self, window_size=64, stride=16, min_samples_per_class=50):
        """
        创建序列窗口数据
        
        参数:
        - window_size: 窗口大小
        - stride: 滑动步长
        - min_samples_per_class: 每个类别的最小样本数
        """
        print(f"\n🔄 创建序列窗口 (窗口大小: {window_size}, 步长: {stride})...")
        
        # 选择输入特征列
        feature_columns = [
            '井径', '自然伽马', '自然电位', '补偿中子', '岩性密度',
            '声波时差', '深侧向电阻率', '浅侧向电阻率', 'TOC'
        ]
        
        sequences = []
        labels = []
        depths = []
        mineral_compositions = []
        
        for i in range(0, len(self.data) - window_size + 1, stride):
            window_data = self.data.iloc[i:i+window_size]
            
            # 检查窗口内岩性的一致性
            window_lithology = window_data['岩性编码'].mode()
            if len(window_lithology) == 0:
                continue
                
            dominant_lithology = window_lithology.iloc[0]
            lithology_consistency = (window_data['岩性编码'] == dominant_lithology).mean()
            
            # 只保留岩性相对一致的窗口 (>50%一致性)
            if lithology_consistency > 0.5:
                # 提取特征序列
                feature_sequence = window_data[feature_columns].values
                
                # 提取矿物成分 (用于地质一致性验证)
                mineral_seq = window_data[['黏土矿物（）', '斜长石（）', '石英（）', 
                                         '方解石（）', '铁白云石（）', '黄铁矿（）']].values
                
                sequences.append(feature_sequence)
                labels.append(dominant_lithology)
                depths.append(window_data['深度'].iloc[window_size//2])  # 窗口中心深度
                mineral_compositions.append(mineral_seq.mean(axis=0))  # 窗口平均矿物成分
        
        sequences = np.array(sequences)
        labels = np.array(labels)
        depths = np.array(depths)
        mineral_compositions = np.array(mineral_compositions)
        
        print(f"✅ 序列窗口创建完成:")
        print(f"   - 总序列数: {len(sequences):,}")
        print(f"   - 序列形状: {sequences.shape}")
        print(f"   - 特征维度: {len(feature_columns)}")
        
        # 统计各类别样本数
        unique_labels, label_counts = np.unique(labels, return_counts=True)
        print(f"📊 各岩性序列分布:")
        for label, count in zip(unique_labels, label_counts):
            lith_name = [k for k, v in self.lithology_map.items() if v == label][0]
            print(f"   - {lith_name}: {count:,} 序列")
        
        # 过滤样本数过少的类别
        valid_labels = unique_labels[label_counts >= min_samples_per_class]
        valid_mask = np.isin(labels, valid_labels)
        
        sequences = sequences[valid_mask]
        labels = labels[valid_mask]
        depths = depths[valid_mask]
        mineral_compositions = mineral_compositions[valid_mask]
        
        print(f"🔍 过滤后 (最少{min_samples_per_class}样本/类):")
        print(f"   - 有效序列数: {len(sequences):,}")
        print(f"   - 有效类别数: {len(valid_labels)}")
        
        return sequences, labels, depths, mineral_compositions, feature_columns
    
    def generate_perturbation_data(self, sequences, noise_levels=[0.01, 0.05, 0.1]):
        """
        生成扰动数据用于注意力稳定性测试
        
        参数:
        - sequences: 原始序列数据
        - noise_levels: 噪声水平列表
        """
        print(f"\n🔀 生成扰动数据 (噪声水平: {noise_levels})...")
        
        perturbation_sets = {}
        
        for noise_level in noise_levels:
            print(f"   - 生成噪声水平 {noise_level} 的扰动数据...")
            
            # 为每个序列添加高斯噪声
            perturbed_sequences = []
            
            for seq in sequences:
                # 计算序列的标准差
                seq_std = np.std(seq, axis=0)
                
                # 生成与序列形状相同的噪声
                noise = np.random.normal(0, noise_level * seq_std, seq.shape)
                
                # 添加噪声
                perturbed_seq = seq + noise
                
                # 保持物理合理性 (不能为负值)
                perturbed_seq = np.maximum(perturbed_seq, seq * 0.1)
                
                perturbed_sequences.append(perturbed_seq)
            
            perturbation_sets[f'noise_{noise_level}'] = np.array(perturbed_sequences)
        
        print(f"✅ 扰动数据生成完成，共 {len(noise_levels)} 个噪声水平")
        
        return perturbation_sets
    
    def split_dataset(self, sequences, labels, depths, mineral_compositions, 
                     test_size=0.2, val_size=0.2, random_state=42):
        """
        分层划分数据集
        
        参数:
        - sequences: 序列数据
        - labels: 标签
        - depths: 深度信息
        - mineral_compositions: 矿物成分
        - test_size: 测试集比例
        - val_size: 验证集比例 (相对于训练集)
        - random_state: 随机种子
        """
        print(f"\n📊 分层划分数据集 (测试集: {test_size*100}%, 验证集: {val_size*100}%)...")
        
        # 第一次划分：训练+验证 vs 测试
        X_temp, X_test, y_temp, y_test, d_temp, d_test, m_temp, m_test = train_test_split(
            sequences, labels, depths, mineral_compositions,
            test_size=test_size, stratify=labels, random_state=random_state
        )
        
        # 第二次划分：训练 vs 验证
        X_train, X_val, y_train, y_val, d_train, d_val, m_train, m_val = train_test_split(
            X_temp, y_temp, d_temp, m_temp,
            test_size=val_size, stratify=y_temp, random_state=random_state
        )
        
        print(f"✅ 数据集划分完成:")
        print(f"   - 训练集: {len(X_train):,} 样本")
        print(f"   - 验证集: {len(X_val):,} 样本")
        print(f"   - 测试集: {len(X_test):,} 样本")
        
        # 验证各集合的类别分布
        for split_name, split_labels in [('训练集', y_train), ('验证集', y_val), ('测试集', y_test)]:
            unique_labels, counts = np.unique(split_labels, return_counts=True)
            print(f"   {split_name}类别分布: {dict(zip(unique_labels, counts))}")
        
        return {
            'train': {'X': X_train, 'y': y_train, 'depths': d_train, 'minerals': m_train},
            'val': {'X': X_val, 'y': y_val, 'depths': d_val, 'minerals': m_val},
            'test': {'X': X_test, 'y': y_test, 'depths': d_test, 'minerals': m_test}
        }
    
    def save_dataset(self, dataset_splits, perturbation_sets, feature_columns, output_dir='./'):
        """
        保存构建好的数据集
        
        参数:
        - dataset_splits: 数据集划分结果
        - perturbation_sets: 扰动数据集
        - feature_columns: 特征列名
        - output_dir: 输出目录
        """
        print(f"\n💾 保存数据集到 {output_dir}...")
        
        # 保存主要数据集
        for split_name, split_data in dataset_splits.items():
            # 保存序列数据
            np.save(f'{output_dir}/daqing_{split_name}_sequences.npy', split_data['X'])
            np.save(f'{output_dir}/daqing_{split_name}_labels.npy', split_data['y'])
            np.save(f'{output_dir}/daqing_{split_name}_depths.npy', split_data['depths'])
            np.save(f'{output_dir}/daqing_{split_name}_minerals.npy', split_data['minerals'])
        
        # 保存扰动数据集 (基于测试集)
        for noise_name, perturbed_data in perturbation_sets.items():
            np.save(f'{output_dir}/daqing_test_{noise_name}.npy', perturbed_data)
        
        # 保存元数据
        metadata = {
            'feature_columns': feature_columns,
            'lithology_map': self.lithology_map,
            'sequence_length': dataset_splits['train']['X'].shape[1],
            'feature_dim': dataset_splits['train']['X'].shape[2],
            'num_classes': len(np.unique(dataset_splits['train']['y'])),
            'dataset_info': {
                'train_samples': len(dataset_splits['train']['X']),
                'val_samples': len(dataset_splits['val']['X']),
                'test_samples': len(dataset_splits['test']['X']),
                'perturbation_levels': list(perturbation_sets.keys())
            }
        }
        
        import json
        with open(f'{output_dir}/daqing_metadata.json', 'w', encoding='utf-8') as f:
            json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 数据集保存完成!")
        print(f"📁 输出文件:")
        print(f"   - 训练集: daqing_train_*.npy")
        print(f"   - 验证集: daqing_val_*.npy") 
        print(f"   - 测试集: daqing_test_*.npy")
        print(f"   - 扰动集: daqing_test_noise_*.npy")
        print(f"   - 元数据: daqing_metadata.json")

def main():
    """主函数"""
    
    # 初始化数据集构建器
    builder = PrivateDatasetBuilder('daqin_normalized.csv')
    
    try:
        # 1. 加载数据
        data = builder.load_data()
        
        # 2. 基于矿物成分进行岩性分类
        lithology_labels = builder.classify_lithology_by_minerals()
        
        # 3. 创建序列窗口
        sequences, labels, depths, mineral_compositions, feature_columns = builder.create_sequence_windows(
            window_size=32, stride=4, min_samples_per_class=10
        )
        
        # 4. 生成扰动数据
        perturbation_sets = builder.generate_perturbation_data(
            sequences[:100],  # 只对部分测试数据生成扰动
            noise_levels=[0.01, 0.05, 0.1]
        )
        
        # 5. 划分数据集
        dataset_splits = builder.split_dataset(
            sequences, labels, depths, mineral_compositions,
            test_size=0.2, val_size=0.2, random_state=42
        )
        
        # 6. 保存数据集
        builder.save_dataset(dataset_splits, perturbation_sets, feature_columns)
        
        print(f"\n🎉 大庆私有数据集构建完成!")
        print(f"🎯 数据集特色:")
        print(f"   ✅ 基于真实矿物成分的科学岩性分类")
        print(f"   ✅ 滑动窗口序列构建保持地质连续性")
        print(f"   ✅ 多噪声水平扰动数据支持稳定性测试")
        print(f"   ✅ 完整的元数据记录确保可复现性")
        
    except Exception as e:
        print(f"❌ 数据集构建过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 大庆私有数据集构建成功完成！")
    else:
        print("\n❌ 大庆私有数据集构建失败！")
