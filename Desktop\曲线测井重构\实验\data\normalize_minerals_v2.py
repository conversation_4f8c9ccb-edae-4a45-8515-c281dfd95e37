#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矿物成分归一化处理脚本 V2
智能检测数据单位，自动归一化所有矿物成分到100%
"""

import pandas as pd
import numpy as np

def normalize_mineral_composition_v2(input_file, output_file):
    """
    矿物成分归一化处理 V2版本
    智能处理不同单位的矿物数据
    """
    
    print("🔄 开始矿物成分归一化处理 V2...")
    
    # 读取清理后的数据
    print(f"📖 读取数据: {input_file}")
    df = pd.read_csv(input_file, encoding='utf-8')
    
    print(f"📊 原始数据统计:")
    print(f"   - 总行数: {len(df):,}")
    print(f"   - 数据形状: {df.shape}")
    
    # 定义矿物成分列
    mineral_columns = [
        '黏土矿物（）', '斜长石（）', '石英（）', 
        '方解石（）', '铁白云石（）', '黄铁矿（）'
    ]
    
    print(f"\n🔍 矿物成分列: {mineral_columns}")
    
    # 计算每行矿物成分的总和
    print(f"\n📈 计算矿物成分总和...")
    df['矿物总和'] = df[mineral_columns].sum(axis=1)
    
    # 统计矿物总和的分布
    print(f"📊 矿物总和统计:")
    print(f"   - 最小值: {df['矿物总和'].min():.3f}")
    print(f"   - 最大值: {df['矿物总和'].max():.3f}")
    print(f"   - 平均值: {df['矿物总和'].mean():.3f}")
    print(f"   - 标准差: {df['矿物总和'].std():.3f}")
    
    # 智能判断数据单位和处理策略
    mineral_sum_mean = df['矿物总和'].mean()
    mineral_sum_std = df['矿物总和'].std()
    
    print(f"\n🎯 智能数据分析:")
    print(f"   - 平均值: {mineral_sum_mean:.3f}")
    print(f"   - 标准差: {mineral_sum_std:.3f}")
    print(f"   - 变异系数: {mineral_sum_std/mineral_sum_mean*100:.1f}%")
    
    # 判断数据特征
    if 95 <= mineral_sum_mean <= 105:
        print(f"✅ 数据接近100%，可能已是百分比单位")
        strategy = "filter_and_normalize"
    elif mineral_sum_mean > 120:
        print(f"⚠️ 数据平均值({mineral_sum_mean:.1f})远大于100，可能是其他单位")
        strategy = "normalize_all"
    else:
        print(f"⚠️ 数据平均值({mineral_sum_mean:.1f})异常，需要检查")
        strategy = "normalize_all"
    
    print(f"📋 处理策略: {strategy}")
    
    if strategy == "filter_and_normalize":
        # 策略1：过滤98-102范围，然后归一化
        print(f"\n🔍 过滤98-102范围内的数据...")
        valid_range_mask = (df['矿物总和'] >= 98) & (df['矿物总和'] <= 102)
        df_filtered = df[valid_range_mask].copy()
        
        valid_rows = len(df_filtered)
        invalid_rows = len(df) - valid_rows
        
        print(f"   - 保留行数: {valid_rows:,} ({valid_rows/len(df)*100:.1f}%)")
        print(f"   - 删除行数: {invalid_rows:,} ({invalid_rows/len(df)*100:.1f}%)")
        
    else:
        # 策略2：保留所有数据，直接归一化
        print(f"\n📋 保留所有数据，进行归一化处理...")
        df_filtered = df.copy()
        valid_rows = len(df_filtered)
        invalid_rows = 0
        
        print(f"   - 处理行数: {valid_rows:,} (100.0%)")
    
    if len(df_filtered) == 0:
        print("❌ 错误：没有数据可以处理！")
        return None
    
    # 矿物成分归一化到100%
    print(f"\n⚖️ 矿物成分归一化处理...")
    
    # 显示归一化前的统计
    print(f"📊 归一化前矿物总和统计:")
    print(f"   - 范围: [{df_filtered['矿物总和'].min():.3f}, {df_filtered['矿物总和'].max():.3f}]")
    print(f"   - 平均: {df_filtered['矿物总和'].mean():.3f}")
    
    # 对每行进行归一化
    print(f"🔄 正在归一化 {len(df_filtered):,} 行数据...")
    
    for idx in df_filtered.index:
        current_sum = df_filtered.loc[idx, '矿物总和']
        if current_sum > 0:  # 避免除零错误
            # 计算归一化因子
            normalization_factor = 100.0 / current_sum
            
            # 对每个矿物成分进行归一化
            for col in mineral_columns:
                df_filtered.loc[idx, col] = df_filtered.loc[idx, col] * normalization_factor
    
    # 重新计算归一化后的总和进行验证
    df_filtered['矿物总和_归一化后'] = df_filtered[mineral_columns].sum(axis=1)
    
    print(f"✅ 归一化后验证:")
    print(f"   - 矿物总和范围: [{df_filtered['矿物总和_归一化后'].min():.6f}, {df_filtered['矿物总和_归一化后'].max():.6f}]")
    print(f"   - 矿物总和平均: {df_filtered['矿物总和_归一化后'].mean():.6f}")
    print(f"   - 矿物总和标准差: {df_filtered['矿物总和_归一化后'].std():.6f}")
    
    # 检查是否所有行都接近100
    tolerance = 1e-10
    perfect_sum = np.abs(df_filtered['矿物总和_归一化后'] - 100.0) < tolerance
    print(f"   - 完美归一化的行数: {perfect_sum.sum():,} / {len(df_filtered):,}")
    
    if perfect_sum.sum() == len(df_filtered):
        print("✅ 所有行的矿物成分都已完美归一化到100%！")
    else:
        print("⚠️ 部分行的归一化可能存在微小误差（数值精度问题）")
    
    # 删除临时列
    df_final = df_filtered.drop(['矿物总和', '矿物总和_归一化后'], axis=1)
    
    # 显示归一化后各矿物的统计
    print(f"\n📊 归一化后各矿物成分统计:")
    for col in mineral_columns:
        min_val = df_final[col].min()
        max_val = df_final[col].max()
        mean_val = df_final[col].mean()
        print(f"   {col}: [{min_val:.3f}, {max_val:.3f}], 均值: {mean_val:.3f}")
    
    # 保存处理后的数据
    print(f"\n💾 保存归一化后的数据到: {output_file}")
    df_final.to_csv(output_file, index=False, encoding='utf-8')
    
    # 最终统计
    print(f"\n📈 最终数据统计:")
    print(f"   - 最终行数: {len(df_final):,}")
    print(f"   - 最终列数: {len(df_final.columns)}")
    print(f"   - 深度范围: {df_final['深度'].min():.3f} - {df_final['深度'].max():.3f} m")
    print(f"   - 数据完整性: 100% (无缺失值，矿物成分归一化)")
    
    # 验证最终结果
    final_mineral_sum = df_final[mineral_columns].sum(axis=1)
    print(f"\n🔍 最终验证:")
    print(f"   - 矿物总和检查: 所有行总和 = 100.000 ± {final_mineral_sum.std():.6f}")
    
    return df_final

def main():
    """主函数"""
    
    # 文件路径
    input_file = "daqin_cleaned.csv"
    output_file = "daqin_normalized.csv"
    
    try:
        # 执行矿物成分归一化
        df_final = normalize_mineral_composition_v2(input_file, output_file)
        
        if df_final is not None:
            print(f"\n🎉 矿物成分归一化处理完成！")
            print(f"📁 输入文件: {input_file}")
            print(f"📁 输出文件: {output_file}")
            print(f"📊 最终数据: {df_final.shape[0]:,} 行 × {df_final.shape[1]} 列")
            
            # 显示前几行数据作为验证
            print(f"\n👀 归一化后数据预览:")
            mineral_columns = ['黏土矿物（）', '斜长石（）', '石英（）', '方解石（）', '铁白云石（）', '黄铁矿（）']
            print("前5行矿物成分:")
            for i in range(min(5, len(df_final))):
                row_minerals = df_final.iloc[i][mineral_columns]
                total = row_minerals.sum()
                print(f"  行{i+1}: {[f'{x:.3f}' for x in row_minerals.values]} → 总和: {total:.6f}")
        else:
            print(f"\n❌ 处理失败：没有有效数据")
            return False
        
    except Exception as e:
        print(f"❌ 处理过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 大庆数据集矿物成分归一化成功完成！")
        print("🎯 所有矿物成分总和已精确归一化到100%")
    else:
        print("\n❌ 大庆数据集矿物成分归一化失败！")
