#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大庆数据集预处理脚本
删除所有包含-9999缺失值的行
"""

import pandas as pd
import numpy as np

def preprocess_daqin_data(input_file, output_file):
    """
    预处理大庆数据集，删除包含-9999缺失值的行
    
    参数:
    - input_file: 输入CSV文件路径
    - output_file: 输出CSV文件路径
    """
    
    print("🔄 开始预处理大庆数据集...")
    
    # 读取原始数据
    print(f"📖 读取原始数据: {input_file}")
    df = pd.read_csv(input_file, encoding='utf-8')
    
    print(f"📊 原始数据统计:")
    print(f"   - 总行数: {len(df):,}")
    print(f"   - 总列数: {len(df.columns)}")
    print(f"   - 数据形状: {df.shape}")
    
    # 检查缺失值情况
    print(f"\n🔍 缺失值(-9999)统计:")
    missing_count = (df == -9999).sum().sum()
    print(f"   - 总缺失值数量: {missing_count:,}")
    
    # 统计每列的缺失值
    missing_by_column = (df == -9999).sum()
    print(f"   - 各列缺失值统计:")
    for col, count in missing_by_column.items():
        if count > 0:
            percentage = (count / len(df)) * 100
            print(f"     {col}: {count:,} ({percentage:.1f}%)")
    
    # 统计包含缺失值的行数
    rows_with_missing = (df == -9999).any(axis=1).sum()
    print(f"   - 包含缺失值的行数: {rows_with_missing:,} ({(rows_with_missing/len(df)*100):.1f}%)")
    
    # 删除包含-9999的行
    print(f"\n🧹 删除包含-9999缺失值的行...")
    df_clean = df[~(df == -9999).any(axis=1)]
    
    print(f"✅ 清理后数据统计:")
    print(f"   - 剩余行数: {len(df_clean):,}")
    print(f"   - 删除行数: {len(df) - len(df_clean):,}")
    print(f"   - 数据保留率: {(len(df_clean)/len(df)*100):.1f}%")
    print(f"   - 清理后形状: {df_clean.shape}")
    
    # 验证清理结果
    remaining_missing = (df_clean == -9999).sum().sum()
    print(f"   - 剩余缺失值: {remaining_missing}")
    
    if remaining_missing == 0:
        print("✅ 所有-9999缺失值已成功删除！")
    else:
        print("⚠️ 警告：仍有缺失值存在！")
    
    # 保存清理后的数据
    print(f"\n💾 保存清理后的数据到: {output_file}")
    df_clean.to_csv(output_file, index=False, encoding='utf-8')
    
    # 显示清理后数据的基本统计
    print(f"\n📈 清理后数据基本统计:")
    print(f"   - 深度范围: {df_clean['深度'].min():.3f} - {df_clean['深度'].max():.3f} m")
    print(f"   - 数据完整性: 100% (无缺失值)")
    
    # 显示各列的数据范围
    print(f"\n📊 各列数据范围:")
    for col in df_clean.columns:
        if col != '深度':  # 深度已经显示过了
            min_val = df_clean[col].min()
            max_val = df_clean[col].max()
            mean_val = df_clean[col].mean()
            print(f"   {col}: [{min_val:.3f}, {max_val:.3f}], 均值: {mean_val:.3f}")
    
    return df_clean

def main():
    """主函数"""
    
    # 文件路径
    input_file = "daqin.csv"
    output_file = "daqin_cleaned.csv"
    
    try:
        # 执行预处理
        df_clean = preprocess_daqin_data(input_file, output_file)
        
        print(f"\n🎉 预处理完成！")
        print(f"📁 原始文件: {input_file}")
        print(f"📁 清理文件: {output_file}")
        print(f"📊 最终数据: {df_clean.shape[0]:,} 行 × {df_clean.shape[1]} 列")
        
        # 显示前几行数据作为验证
        print(f"\n👀 清理后数据预览:")
        print(df_clean.head())
        
    except Exception as e:
        print(f"❌ 预处理过程中出现错误: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 大庆数据集预处理成功完成！")
    else:
        print("\n❌ 大庆数据集预处理失败！")
