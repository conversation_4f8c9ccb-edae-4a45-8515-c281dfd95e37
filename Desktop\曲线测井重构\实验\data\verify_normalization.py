#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证归一化数据的准确性
检查六个矿物成分的总和是否都等于100
"""

import pandas as pd
import numpy as np

def verify_mineral_normalization(file_path):
    """
    验证矿物成分归一化的准确性
    
    参数:
    - file_path: 归一化后的CSV文件路径
    """
    
    print("🔍 开始验证矿物成分归一化准确性...")
    
    # 读取归一化后的数据
    print(f"📖 读取数据: {file_path}")
    df = pd.read_csv(file_path, encoding='utf-8')
    
    print(f"📊 数据基本信息:")
    print(f"   - 总行数: {len(df):,}")
    print(f"   - 总列数: {len(df.columns)}")
    
    # 定义矿物成分列
    mineral_columns = [
        '黏土矿物（）', '斜长石（）', '石英（）', 
        '方解石（）', '铁白云石（）', '黄铁矿（）'
    ]
    
    print(f"\n🔍 矿物成分列:")
    for i, col in enumerate(mineral_columns, 1):
        print(f"   {i}. {col}")
    
    # 检查所有矿物列是否存在
    missing_columns = [col for col in mineral_columns if col not in df.columns]
    if missing_columns:
        print(f"❌ 错误：以下矿物列不存在: {missing_columns}")
        return False
    
    print(f"✅ 所有矿物列都存在")
    
    # 计算每行的矿物总和
    print(f"\n📈 计算每行矿物总和...")
    df['验证总和'] = df[mineral_columns].sum(axis=1)
    
    # 统计总和的分布
    print(f"📊 矿物总和统计:")
    print(f"   - 最小值: {df['验证总和'].min():.10f}")
    print(f"   - 最大值: {df['验证总和'].max():.10f}")
    print(f"   - 平均值: {df['验证总和'].mean():.10f}")
    print(f"   - 标准差: {df['验证总和'].std():.10f}")
    
    # 检查是否所有行都等于100
    tolerance_levels = [1e-10, 1e-8, 1e-6, 1e-4, 1e-2]
    
    print(f"\n🎯 归一化准确性检查:")
    for tolerance in tolerance_levels:
        perfect_count = np.abs(df['验证总和'] - 100.0) < tolerance
        perfect_rows = perfect_count.sum()
        percentage = perfect_rows / len(df) * 100
        
        print(f"   - 误差 < {tolerance}: {perfect_rows:,} 行 ({percentage:.2f}%)")
        
        if perfect_rows == len(df):
            print(f"     ✅ 所有行在此精度下都完美归一化！")
            break
    
    # 找出偏差最大的行
    df['偏差'] = np.abs(df['验证总和'] - 100.0)
    max_deviation_idx = df['偏差'].idxmax()
    max_deviation = df.loc[max_deviation_idx, '偏差']
    
    print(f"\n📊 偏差分析:")
    print(f"   - 最大偏差: {max_deviation:.10f}")
    print(f"   - 最大偏差行: 第{max_deviation_idx + 1}行")
    print(f"   - 该行总和: {df.loc[max_deviation_idx, '验证总和']:.10f}")
    
    # 显示偏差最大的几行详情
    top_deviations = df.nlargest(5, '偏差')
    
    print(f"\n🔍 偏差最大的5行详情:")
    print("行号 | 深度 | 矿物总和 | 偏差")
    print("-" * 50)
    
    for idx, row in top_deviations.iterrows():
        depth = row['深度']
        total = row['验证总和']
        deviation = row['偏差']
        print(f"{idx+1:4d} | {depth:7.3f} | {total:10.6f} | {deviation:.2e}")
    
    # 显示各矿物成分的统计
    print(f"\n📊 各矿物成分统计:")
    for col in mineral_columns:
        min_val = df[col].min()
        max_val = df[col].max()
        mean_val = df[col].mean()
        std_val = df[col].std()
        print(f"   {col}:")
        print(f"     范围: [{min_val:.3f}, {max_val:.3f}]")
        print(f"     均值: {mean_val:.3f} ± {std_val:.3f}")
    
    # 随机验证几行的计算
    print(f"\n🔍 随机验证计算准确性:")
    sample_indices = np.random.choice(len(df), min(5, len(df)), replace=False)
    
    for i, idx in enumerate(sample_indices, 1):
        row = df.iloc[idx]
        manual_sum = sum(row[col] for col in mineral_columns)
        calculated_sum = row['验证总和']
        difference = abs(manual_sum - calculated_sum)
        
        print(f"   验证{i} (行{idx+1}):")
        print(f"     深度: {row['深度']:.3f}m")
        print(f"     矿物成分: {[f'{row[col]:.3f}' for col in mineral_columns]}")
        print(f"     手动计算: {manual_sum:.10f}")
        print(f"     自动计算: {calculated_sum:.10f}")
        print(f"     差异: {difference:.2e}")
        
        if difference < 1e-10:
            print(f"     ✅ 计算完全正确")
        elif difference < 1e-6:
            print(f"     ✅ 计算基本正确（微小数值误差）")
        else:
            print(f"     ❌ 计算可能有误")
    
    # 总结评估
    perfect_normalization = (df['偏差'] < 1e-10).all()
    good_normalization = (df['偏差'] < 1e-6).all()
    
    print(f"\n🎯 归一化质量评估:")
    
    if perfect_normalization:
        print(f"   ✅ 完美归一化：所有行的矿物总和都精确等于100.000000")
        quality = "完美"
    elif good_normalization:
        print(f"   ✅ 优秀归一化：所有行的矿物总和都非常接近100（误差<1e-6）")
        quality = "优秀"
    elif (df['偏差'] < 1e-4).all():
        print(f"   ✅ 良好归一化：所有行的矿物总和都接近100（误差<1e-4）")
        quality = "良好"
    else:
        print(f"   ⚠️ 一般归一化：部分行的矿物总和偏差较大")
        quality = "一般"
    
    print(f"\n📋 结论:")
    print(f"   - 归一化质量: {quality}")
    print(f"   - 数据可信度: {'非常高' if perfect_normalization else '高' if good_normalization else '中等'}")
    print(f"   - 建议使用: {'强烈推荐' if perfect_normalization else '推荐' if good_normalization else '谨慎使用'}")
    
    return True

def main():
    """主函数"""
    
    file_path = "daqin_normalized.csv"
    
    try:
        success = verify_mineral_normalization(file_path)
        
        if success:
            print(f"\n🎉 归一化验证完成！")
        else:
            print(f"\n❌ 验证过程中出现问题")
            return False
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 矿物成分归一化验证成功完成！")
    else:
        print("\n❌ 矿物成分归一化验证失败！")
