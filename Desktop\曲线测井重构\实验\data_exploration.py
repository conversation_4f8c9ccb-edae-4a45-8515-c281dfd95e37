import pandas as pd
import os

def analyze_log_data(file_path):
    """
    Reads and analyzes the well log data from a CSV file.

    Args:
        file_path (str): The path to the CSV file.
    """
    try:
        # Load the dataset
        df = pd.read_csv(file_path)

        print("--- 1. Data Loading Successful ---")
        print(f"Successfully loaded data from: {file_path}")
        print(f"Dataset shape: {df.shape[0]} rows, {df.shape[1]} columns\n")

        print("--- 2. First 5 Rows of the Dataset (Head) ---")
        print(df.head())
        print("\n" + "="*50 + "\n")

        print("--- 3. Dataset Columns ---")
        print(df.columns.tolist())
        print("\n" + "="*50 + "\n")
        
        print("--- 4. Data Types and Non-Null Counts (Info) ---")
        print(df.info())
        print("\n" + "="*50 + "\n")

        print("--- 5. Descriptive Statistics ---")
        print(df.describe())
        print("\n" + "="*50 + "\n")

    except FileNotFoundError:
        print(f"Error: The file was not found at {file_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    # Define the path to the data file
    # Assuming the script is run from the root of the '曲线测井重构' directory
    csv_file_path = os.path.join('实验', 'data', 'log.csv')
    analyze_log_data(csv_file_path) 