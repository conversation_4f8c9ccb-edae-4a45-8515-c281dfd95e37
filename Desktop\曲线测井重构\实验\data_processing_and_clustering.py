import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler

def find_best_slice(df, window_size=200, column='GR'):
    """
    Finds the data slice with the highest variance in a specific column.
    
    Args:
        df (pd.DataFrame): The input dataframe.
        window_size (int): The size of the sliding window.
        column (str): The column to calculate variance on.

    Returns:
        int: The start index of the slice with the highest variance.
    """
    max_var = 0
    best_start_index = 0
    
    for i in range(len(df) - window_size):
        window = df[column].iloc[i:i+window_size]
        current_var = window.var()
        if current_var > max_var:
            max_var = current_var
            best_start_index = i
            
    print(f"Found best slice starting at index {best_start_index} with variance {max_var:.2f}")
    return best_start_index

def process_and_cluster(file_path):
    """
    Loads data, finds the best slice, performs clustering, and visualizes the results.
    """
    # Load data
    df = pd.read_csv(file_path)

    # Step 0: Find the most interesting data slice
    WINDOW_SIZE = 200  # Corresponds to 100 meters, as data is every 0.5m
    start_index = find_best_slice(df, window_size=WINDOW_SIZE, column='GR')
    data_slice = df.iloc[start_index:start_index + WINDOW_SIZE].copy()

    # Step 1: Perform K-Means clustering to generate "Ground Truth"
    # Select features for clustering
    features = ['GR', 'RHOB', 'DPOR', 'SP', 'RILD']
    cluster_data = data_slice[features]

    # Scale features for better clustering performance
    scaler = StandardScaler()
    cluster_data_scaled = scaler.fit_transform(cluster_data)

    # Perform K-Means
    N_CLUSTERS = 4
    kmeans = KMeans(n_clusters=N_CLUSTERS, random_state=42, n_init='auto')
    data_slice['LITH_TRUE'] = kmeans.fit_predict(cluster_data_scaled)

    print(f"\nSuccessfully clustered data into {N_CLUSTERS} lithology types.")

    # --- Visualization for Evaluation ---
    plt.style.use('seaborn-v0_8-whitegrid')
    fig = plt.figure(figsize=(15, 8))
    fig.suptitle('Evaluation of Unsupervised Clustering (K-Means)', fontsize=16, y=1.02)

    # --- Evaluation A: Cross Plot ---
    ax1 = fig.add_subplot(1, 2, 1)
    scatter = ax1.scatter(data_slice['GR'], data_slice['RHOB'], c=data_slice['LITH_TRUE'], cmap='viridis', alpha=0.7, s=50)
    ax1.set_xlabel('Gamma Ray (GR)', fontsize=12)
    ax1.set_ylabel('Bulk Density (RHOB)', fontsize=12)
    ax1.set_title('Evaluation A: Cross Plot\n(Clusters should be well-separated)', fontsize=14)
    ax1.legend(handles=scatter.legend_elements()[0], labels=[f'Type {i}' for i in range(N_CLUSTERS)])
    ax1.grid(True)

    # --- Evaluation B: Profile Consistency Plot ---
    ax2 = fig.add_subplot(1, 2, 2)
    ax2.set_title('Evaluation B: Profile Consistency\n(Colors should match curve patterns)', fontsize=14)
    ax2.set_xlabel('Value', fontsize=12)
    ax2.set_ylabel('Depth', fontsize=12)
    
    # Plot GR curve
    ax2.plot(data_slice['GR'], data_slice['Depth'], 'k-', label='GR')
    
    # Invert y-axis for depth plot
    ax2.invert_yaxis()
    
    # Create a color-filled background based on cluster labels
    for i in range(len(data_slice) - 1):
        lith_class = data_slice['LITH_TRUE'].iloc[i]
        depth_start = data_slice['Depth'].iloc[i]
        depth_end = data_slice['Depth'].iloc[i+1]
        color = plt.cm.viridis(lith_class / (N_CLUSTERS - 1))
        ax2.axhspan(depth_start, depth_end, color=color, alpha=0.3)

    ax2.legend(loc='upper right')
    ax2.grid(True)

    plt.tight_layout(rect=[0, 0, 1, 0.98])
    plt.show()


if __name__ == "__main__":
    csv_file_path = '实验/data/log.csv'
    process_and_cluster(csv_file_path) 