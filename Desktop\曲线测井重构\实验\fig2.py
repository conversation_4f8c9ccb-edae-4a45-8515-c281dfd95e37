import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from matplotlib.gridspec import GridSpec
from scipy.ndimage import gaussian_filter1d
import os

def visualize_figure2_beautified():
    """
    可视化图2的最终美化版本:
    使用顶刊风格，优化色彩、线条和布局，并移除网格线。
    """
    # --- 美学设定：设置专业、顶刊风格的参数 ---
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    plt.rcParams['xtick.labelsize'] = 10
    plt.rcParams['ytick.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 10
    plt.rcParams['axes.titleweight'] = 'bold'
    plt.rcParams['axes.labelweight'] = 'bold'

    # --- 0. 构建稳健的文件路径 ---
    script_dir = os.path.dirname(os.path.abspath(__file__))
    data_path = os.path.join(script_dir, 'data', 'log.csv')
    filters_path = os.path.join(script_dir, 'csc_filters.npz')
    output_path = os.path.join(script_dir, '..', 'figure_2_beautified.png')

    # --- 1. 数据加载与准备 ---
    try:
        df = pd.read_csv(data_path)
    except FileNotFoundError:
        print(f"错误: 无法在 '{data_path}' 找到数据文件。")
        return

    window_size = 100
    gr_curve = df['GR'].dropna()
    variances = gr_curve.rolling(window_size).var()
    best_start_index = variances.idxmax() - window_size + 1
    data_slice = df.iloc[best_start_index:best_start_index + window_size].copy()
    
    depth = data_slice['Depth'].values
    log_curve = data_slice['GR'].values
    log_curve = gaussian_filter1d(log_curve, sigma=2)
    L = len(log_curve)

    # --- 2. 加载学习到的真实地质先验 (CSC滤波器) ---
    try:
        filters_data = np.load(filters_path)
        print(f"成功加载学习到的CSC滤波器: {filters_path}")
    except FileNotFoundError:
        print(f"错误: 无法找到 '{filters_path}'。")
        return
        
    litho_keys = sorted(filters_data.keys())
    K = np.array([filters_data[key] for key in litho_keys])
    
    lithology_map = {
        '0': 'Siltstone',
        '2': 'Shale',
        '3': 'Sandstone'
    }
    litho_names = [lithology_map.get(key.split('_')[-1], f"Class {key.split('_')[-1]}") for key in litho_keys]
    C, filter_len = K.shape

    # --- 3. 计算与生成 ---
    response_maps = np.zeros((L, C))
    for i in range(C):
        conv_res = np.convolve(log_curve, K[i, ::-1], mode='valid')
        pad_len = L - len(conv_res)
        response_maps[:, i] = np.pad(conv_res, (pad_len // 2, pad_len - pad_len // 2), 'edge')

    S = response_maps @ response_maps.T
    S = (S - S.min()) / (S.max() - S.min())
    M = S
    
    # --- 4. 可视化 (Beautified) ---
    fig = plt.figure(figsize=(10, 8))
    gs = GridSpec(2, 2, figure=fig, hspace=0.4, wspace=0.3)
    fig.suptitle('Figure 2: Generation of Geological Attention Bias', fontsize=16, weight='bold')

    # 定义专业调色板 (色盲友好)
    colors = {
        'log_curve': '#332288',
        'filters': ['#0072B2', '#D55E00', '#009E73']
    }

    # Panel A: Input Well Log X
    ax_A = fig.add_subplot(gs[0, 0])
    ax_A.plot(log_curve, depth, color=colors['log_curve'], linewidth=2)
    ax_A.set_title("A. Input Well Log (X)", loc='left')
    ax_A.set_xlabel("GR Value")
    ax_A.set_ylabel("Depth (m)")
    ax_A.invert_yaxis()

    # Panel B: Learned CSC Filters K
    ax_B = fig.add_subplot(gs[0, 1])
    for i in range(C):
        ax_B.plot(range(filter_len), K[i, :], label=litho_names[i], color=colors['filters'][i], linewidth=2.5)
    ax_B.set_title("B. Learned CSC Filters (K)", loc='left')
    ax_B.set_xlabel("Filter Taps (Lag)")
    ax_B.set_ylabel("Normalized Autocorrelation")
    ax_B.legend(frameon=False)

    # Panel C: Response Maps
    ax_C = fig.add_subplot(gs[1, 0])
    im_c = ax_C.imshow(response_maps, aspect='auto', cmap='viridis', 
                       extent=[0, C, depth[-1], depth[0]])
    ax_C.set_title("C. Geological Response Maps", loc='left')
    ax_C.set_xticks(np.arange(C) + 0.5)
    ax_C.set_xticklabels(litho_names, rotation=45, ha='right')
    ax_C.set_ylabel("Depth (m)")
    fig.colorbar(im_c, ax=ax_C, orientation='vertical', label='Response Strength')

    # Panel D: Final Attention Bias Matrix M
    ax_D = fig.add_subplot(gs[1, 1])
    im_d = ax_D.imshow(M, cmap='plasma', origin='lower',
                       extent=[depth[0], depth[-1], depth[0], depth[-1]])
    ax_D.set_title("D. Attention Bias Matrix (M)", loc='left')
    ax_D.set_xlabel("Sequence Position (i)")
    ax_D.set_ylabel("Sequence Position (j)")
    cbar = fig.colorbar(im_d, ax=ax_D, orientation='vertical')
    cbar.set_label('Geological Similarity')
    
    plt.tight_layout(rect=[0, 0, 1, 0.96])

    plt.savefig(output_path, dpi=300)
    print(f"Beautified Figure 2 saved to '{output_path}'")
    plt.show()


if __name__ == '__main__':
    visualize_figure2_beautified()
