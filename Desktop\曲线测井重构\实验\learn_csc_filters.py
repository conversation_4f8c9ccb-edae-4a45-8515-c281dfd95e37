import pandas as pd
import numpy as np

def calculate_autocorrelation(segment, max_lag):
    """计算单个数据段的自相关性"""
    autocorrs = []
    for lag in range(max_lag + 1):
        # 创建两个有偏移的序列
        shifted_segment = segment.shift(-lag).dropna()
        original_segment = segment.iloc[:len(shifted_segment)]
        
        # 计算皮尔逊相关系数作为自相关值
        if len(original_segment) > 1:
            corr = original_segment.corr(shifted_segment)
            autocorrs.append(corr)
        else:
            autocorrs.append(np.nan)
    return np.array(autocorrs)

def learn_and_save_csc_filters(data_path, output_path, gr_col='GR', label_col='LITH_TRUE', segment_len=15, max_lag=7):
    """
    从带标签的测井数据中学习CSC滤波器并保存。

    参数:
    - data_path: 输入的CSV文件路径 (e.g., '实验/data/final_data_for_figure.csv')
    - output_path: 输出的.npz文件路径 (e.g., '实验/csc_filters.npz')
    - gr_col: 包含GR曲线数据的列名
    - label_col: 包含岩性标签的列名
    - segment_len: 用于计算自相关的滑动窗口长度 (奇数)
    - max_lag: 自相关的最大偏移量
    """
    print(f"--- 开始学习CSC滤波器 ---")
    
    # 1. 数据准备与分组
    try:
        df = pd.read_csv(data_path)
        print(f"成功加载数据: {data_path}")
    except FileNotFoundError:
        print(f"错误: 无法找到数据文件 at {data_path}")
        return

    litho_classes = sorted(df[label_col].unique())
    print(f"发现岩性类别: {litho_classes}")

    learned_filters = {}

    # 2. 核心计算 - 学习"地质指纹"
    for litho_class in litho_classes:
        print(f"\n正在处理类别: {litho_class}...")
        
        # 获取属于当前类别的所有数据段
        class_segments = []
        current_segment = []
        for i in range(len(df)):
            if df.loc[i, label_col] == litho_class:
                current_segment.append(df.loc[i, gr_col])
            else:
                if len(current_segment) >= segment_len:
                    class_segments.append(pd.Series(current_segment))
                current_segment = []
        # 添加最后一个段
        if len(current_segment) >= segment_len:
            class_segments.append(pd.Series(current_segment))

        if not class_segments:
            print(f"警告: 类别 {litho_class} 没有找到足够长的数据段，已跳过。")
            continue
            
        print(f"找到 {len(class_segments)} 个数据段。")

        # 对每个数据段计算自相关并求平均
        all_autocorrs = []
        for segment in class_segments:
            # 使用滑动窗口来增加样本量
            for i in range(len(segment) - segment_len + 1):
                window = segment.iloc[i:i + segment_len]
                autocorr = calculate_autocorrelation(window, max_lag)
                if not np.isnan(autocorr).any():
                    all_autocorrs.append(autocorr)
        
        if not all_autocorrs:
            print(f"警告: 类别 {litho_class} 无法计算有效的自相关性，已跳过。")
            continue

        # 计算平均自相关作为该类别的滤波器
        avg_autocorr = np.nanmean(all_autocorrs, axis=0)
        # 归一化滤波器
        final_filter = (avg_autocorr - np.mean(avg_autocorr)) / np.std(avg_autocorr)
        
        learned_filters[f'litho_{litho_class}'] = final_filter
        print(f"类别 {litho_class} 的滤波器已学习完毕。")

    # 3. 保存知识库
    if learned_filters:
        np.savez(output_path, **learned_filters)
        print(f"\n--- 所有滤波器已成功学习并保存到: {output_path} ---")
        print("保存的滤波器键名:", list(learned_filters.keys()))
    else:
        print("\n--- 未能学习到任何滤波器，未创建文件。 ---")


if __name__ == '__main__':
    # 定义输入和输出路径
    INPUT_CSV = '实验/data/final_data_for_figure.csv'
    OUTPUT_NPZ = '实验/csc_filters.npz'
    
    # 执行学习流程
    learn_and_save_csc_filters(INPUT_CSV, OUTPUT_NPZ) 