# 数据集构建与预处理：详细指南

**目标**: 构建两个用于GIAT模型训练、验证和测试的高质量数据集：
1.  **数据集A (公开基准)**: 美国堪萨斯州Hugoton-Panoma气田数据集。
2.  **数据集B (私有验证)**: 中国大庆油田某区域数据集。

本指南将详细阐述从数据获取到最终模型输入的全过程，确保实验的科学性和可复现性。

---

## 第一步：数据获取与整理 (Data Acquisition and Organization)

### 1.1 数据集A (堪萨斯公开数据)

*   **数据来源**: 该数据集通常与一些开源的地球科学项目或出版物一同发布。我们需要找到其原始出处，常见的来源包括：
    *   堪萨斯地质调查局 (Kansas Geological Survey) 官方网站。
    *   作为某些经典机器学习论文（如 `ResGAT` 或 `Intelligent Sedimentary Lithofacies Identification`）的补充材料发布在GitHub或特定数据仓库中。
    *   存在于如 `Software Underground (SWUNG)` 或 `CURVE` 等地球科学数据共享社区。
*   **行动计划**:
    1.  **定位数据**: 启动网络搜索，使用关键词 "Kansas well log dataset", "Hugoton Panoma dataset download", "Council Grove gas reservoir data" 来定位下载链接。
    2.  **文件格式**: 下载后的数据通常是 `.las` (Log ASCII Standard) 文件，或者已经预处理好的 `.csv` 文件。我们需要确保能够解析这些文件。
    3.  **数据整理**: 为该数据集创建一个专门的文件夹，例如 `data/kansas_public/`。将所有相关的井数据文件（无论是 `.las` 还是 `.csv`）都存放在此。同时，需要找到描述文件（通常是 `README.md` 或相关论文），明确每一口井的名称、位置以及可用的测井曲线和岩性标签的含义。

### 1.2 数据集B (大庆私有数据)

*   **数据来源**: 您的团队内部数据。
*   **行动计划**:
    1.  **数据收集**: 收集所有相关井的 `.las` 或 `.csv` 文件。
    2.  **数据整理**: 同样，为其创建一个独立的文件夹，如 `data/daqing_private/`。
    3.  **元数据记录**: 必须创建一个`metadata.xlsx`或`README.md`文件，清晰地记录以下信息：
        *   **井名列表**: 所有使用的井的唯一标识符。
        *   **测井曲线清单**: 记录可用的测井曲线全称、缩写（如`GR`, `DEN`）及其单位。
        *   **岩性标签**: 记录所有的岩性类别及其对应的数字编码（例如：砂岩-1, 泥岩-2）。这是确保模型输出可解释的关键。
        *   **数据质量备注**: 简要说明数据中可能存在的问题，如哪些井缺失了哪种曲线，哪些井段数据质量较差等。

---

## 第二步：数据加载与标准化 (Data Loading and Standardization)

这一步需要编写Python脚本来完成，建议使用`pandas`进行数据处理，`lasio`库来读取`.las`文件。

### 2.1 编写数据加载器 (`data_loader.py`)

*   **功能1: 加载LAS/CSV文件**:
    *   创建一个函数 `load_well_data(file_path)`，该函数能自动判断文件类型（`.las`或`.csv`）并使用相应的库（`lasio`或`pandas`）进行加载。
    *   函数返回一个`pandas.DataFrame`，其中索引是深度（DEPTH），列是各个测井曲线。
*   **功能2: 合并多井数据**:
    *   编写一个主流程函数，该函数遍历指定数据集文件夹（如`data/kansas_public/`）中的所有井文件。
    *   为每一口井的DataFrame增加一个`'WELL_ID'`列，以区分不同井的数据。
    *   将所有井的DataFrame合并成一个大的DataFrame。
    *   加载并合并岩性标签数据（可能在单独的文件中），确保深度能够对齐。

### 2.2 数据清洗与预处理 (`preprocessing.py`)

这是确保模型性能的关键一步，需要严格按照您在 `3_Data_Analysis_and_Experimental_Design.md` 中描述的流程进行。

*   **输入**: 合并后的多井DataFrame。
*   **输出**: 预处理后，准备好进行模型输入的DataFrame。
*   **核心步骤**:
    1.  **选择核心曲线**: 根据我们的研究，确定用于模型输入的测井曲线。对于两个数据集，我们都应尽量使用相同的曲线集以保证公平对比，例如：`GR`, `SP`, `DT`, `DEN`, `NPHI`, `RT`。如果私有数据中某些曲线缺失，需要记录下来并在论文中说明。
    2.  **处理异常值 (Outlier Detection)**:
        *   对每一条测井曲线，使用**四分位距 (IQR)** 方法。
        *   计算 `Q1 = df[curve].quantile(0.25)` 和 `Q3 = df[curve].quantile(0.75)`。
        *   计算 `IQR = Q3 - Q1`。
        *   定义异常值边界 `lower = Q1 - 1.5 * IQR` 和 `upper = Q3 + 1.5 * IQR`。
        *   将超出边界的值替换为`np.nan`（暂时标记为缺失）。
    3.  **处理缺失值 (Missing Value Imputation)**:
        *   **策略**: 使用**滑动窗口中位数**进行插补。这是一个比全局均值/中位数更好的策略，因为它能保留局部地质趋势。
        *   对每一条曲线，使用`df[curve].rolling(window=5, center=True, min_periods=1).median()`来计算窗口中位数，然后用这个结果来填充上一步产生的`NaN`值。
        *   在填充之后，可能还会有少量窗口无法覆盖的NaN（比如序列的开头和结尾），对这些极少数的值，可以使用整条曲线的中位数进行二次填充。
    4.  **数据归一化 (Normalization)**:
        *   对每一条处理过的测井曲线，应用**最小-最大归一化 (Min-Max Scaling)**，将数据缩放到 `[0, 1]` 区间。
        *   **重要**: 归一化参数（`min`和`max`）**必须在训练集上计算**，然后**应用到验证集和测试集上**。这可以防止测试集的信息泄露到训练过程中。在划分数据集之前，可以先计算全局的min/max，但在严格的学术流程中，应在划分数据集后再进行。我们可以在此步骤先完成对整个数据集的归一化，但在模型训练脚本中必须坚持"fit on train, transform on test"的原则。

---

## 第三步：数据集划分 (Dataset Splitting)

为保证模型评估的公正性，我们必须采用严格的**跨井验证 (Cross-Well Validation)**策略。

*   **数据集A (堪萨斯)**:
    *   总共有10口井。遵循`ResGAT`论文的划分方式（或自己预设）：
    *   **训练集**: 随机选择8口井的数据。
    *   **测试集**: 剩下的2口井作为完全独立的、模型未见过的测试集。
    *   从训练集的8口井数据中，再按80%/20%的比例划分出**训练子集**和**验证子集**，用于模型训练过程中的调参与监控。

*   **数据集B (大庆)**:
    *   假设我们有N口井。
    *   **训练集**: 选择其中 M 口井 (例如，N-2口井)。
    *   **测试集**: 剩下的 K 口井 (例如，2口井)，确保这些井与训练井在地理位置上不重叠（如果是邻井，可以在论文中说明，作为模型在邻近区域泛化能力的考验）。
    *   同样，训练集再划分为训练子集和验证子集。

*   **实现**: 编写一个`split_data.py`脚本，输入预处理好的DataFrame，输出`train.csv`, `validation.csv`, `test.csv`三个文件。文件中应包含所有处理好的测井曲线以及对应的岩性标签。

---

至此，我们就完成了数据集的构建工作。最终产出物应该是结构清晰的文件夹和几个可以直接被模型训练脚本调用的 `.csv` 文件。这个流程确保了我们的实验既能和SOTA方法在公开数据上公平比较，又能在私有数据上验证模型的独特性和泛化能力。
