# DRSN-GAF: Deep Residual Shrinkage Network (DRSN) for Lithology Classification Through Well Logging Data Transformed by Gram Angle Field  

Youzhuang $\operatorname { S u n } ^ { \mathbb { \oplus } }$ , <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>  

Abstract— Lithology holds significant importance in reservoir evaluation and geological modeling. However, the complex relationship between logging and lithology leads to strong multisolutions in logging responses, resulting in inaccurate identification of traditional logging lithology methods. Given the impressive performance of deep learning in data classification, we delved further into the technology and presented a deep residual network for lithological classification. The deep residual shrinkage network (DRSN) model incorporates an attention mechanism and a soft thresholding strategy based on the residual network. The residual shrinkage mechanism is the core characteristic of DRSN. It enhances model sparsity by shrinking the weights in the residual block (soft threshold strategy), resulting in a simpler model, and mitigating the overfitting problem. To test the model, we selected data from two wells in the Tarim Oilfield, China. In this article, we innovatively use the Gram angle field (GAF) to convert 1-D logging parameters into 2-D images. These images are then input into the DRSN model, using the idea of image processing to tackle the lithology classification problem. GAF effectively captures time-series information and converts 1-D time-series data into a 2-D matrix representation. Furthermore, GAF enhances the ability to capture nonlinear structures and patterns in time-series data using trigonometric transformations. Experimental results demonstrate that the DRSN-GAF network outperforms both DRSN and convolutional neural network (CNN) networks in terms of accuracy. The lithology prediction tasks for the two wells achieve accuracies of $9 6 . 0 0 \%$ and $9 6 . 5 0 \%$ , respectively.  

Index Terms— Attention mechanism, depth residual shrinkage network, gram angle field (GAF), lithological classification, logging data, soft thresholding.  

# I. INTRODUCTION  

premise of fine characterization and comprehensive evaluation of reservoirs. The approaches to obtaining lithological information are primarily drilling coring, rock chip logging, and well coring, among others [1]. The high cost of drilling coring and the inaccuracy of rock chip logging have  

Manuscript received 6 September 2023; revised 6 November 2023;   
accepted 20 December 2023. Date of publication 25 December 2023; date   
of current version 8 January 2024. (Corresponding author: Shanchen Pang.) Youzhuang Sun is with the School of Earth Science and Technology,   
China University of Petroleum, Qingdao, Shandong 266000, China (e-mail:   
<EMAIL>). Shanchen Pang is with the School of Software, China University   
of Petroleum, Qingdao, Shandong 266000, China (e-mail: $\mathord { \ z } 2 1 0 1 0 0 8 8 @ \varnothing$   
s.upc.edu.cn). Junhua Zhang is with the College of Earth Science and Technology, China   
University of Petroleum, Qingdao, Shandong 266000, China. Yongan Zhang is with the School of Computer Science, China University   
of Petroleum (East China), Qingdao, Shandong 266000, China. Digital Object Identifier 10.1109/LGRS.2023.3346382  

spotlighted the research on logging lithology identification methods. Logging lithology identification primarily establishes a mapping relationship between logging parameters and rock types, using this mapping to identify the rock types of unsampled well sections [2]. As methods such as pattern recognition, statistical learning, and machine learning evolve, an increasing number of mathematical theories and computer algorithms have been incorporated into the establishment process of lithology recognition models [3]. Prior to the advent of deep learning, conventional neural networks with substantial depth suffered from technical issues such as local optimal solutions and vanishing gradients, resulting in subpar performance. Deep learning, a multilayer neural network architecture, enables the discovery of distributed feature representations by combining low-level attributes to form more abstract, high-level representation categories or features. As such, deep learning has gradually emerged as a focal point in the research of numerous scholars. Zhang et al. [4] used continuous wavelet transforms to obtain the time spectrum of seismic data, which was then input into convolutional neural networks (CNNs) for lithology prediction. Liu et al. [5] proposed the quantum-enhanced deep learning (QEDL) model for lithology prediction, demonstrating significantly improved generalization capabilities compared with classical CNN models and long short-term memory (LSTM) models. Jiang et al. [6] evaluated the performance of the bidirectional gated recurrent unit network by incorporating geological constraints to address logging lithology classification issues. The experimental results indicated that geological constraints can enhance classification accuracy, and the deep learning model of the bidirectional gated recurrent unit network effectively solved lithology classification challenges. Liu et al. [7] proposed a regularized residual network for lithology prediction, capable of effectively indicating the distribution of underground lithology in seismic data predictions. Zhang et al. [8] used a CNN to extract single-point, structural, and multidimensional features, incorporating a feature fusion module in U-Net and feature pyramid. The experimental results showed that the recognition accuracy of the hybrid method on the test set reached $8 0 . 0 2 \%$ , verifying the effectiveness of the feature fusion model in lithology identification. Song et al. [9] proposed a reservoir lithology identification method based on improved adversarial learning (IAL) to mitigate the overfitting problem and multiple solution issues caused by insufficient labeling data and numerous learnable parameters during training. This method effectively reduces classification errors for sandstone and sandy mudstone.  

In previous research on lithological classification, the gradient disappearance problem in CNNs was pronounced. Each layer of the CNN conducts convolution and pooling operations, which compress and filter the gradient to some extent. As the number of layers increases, the gradient disappears rapidly, making it difficult to perform effective backpropagation. To address this issue, this article presents a residual network that introduces a residual connection to directly pass the input signal to subsequent layers, enabling gradient flow throughout the network more seamlessly. This approach mitigates the vanishing gradient problem, making the network easier to train.  

Several techniques have been proposed in CNN training to tackle the vanishing gradient problem.  

1) Using diverse activation functions [10] such as ReLU and ELU increases the gradient propagation ability of neural networks, thereby mitigating the vanishing gradient phenomenon.   
2) Dropout [11] was used to randomly discard neurons during training, enhancing model generalization while preventing overfitting, and thereby reducing the gradient vanishing issue.   
3) Batch processing normalization [12] normalizes each layer’s inputs, stabilizing gradients during network propagation and mitigating the gradient vanishing problem.   
4) Hopping connections (residual network structures) [13] facilitate faster gradient transmission during backpropagation, lowering the risk of gradient vanishing.  

Residual networks offer a simple yet powerful structure that has achieved remarkable results in numerous computer classification tasks, such as image classification and object detection. In comparison to LSTMs, residual networks enable the construction of deeper network structures. LSTMs, however, are better suited for handling sequential data and modeling longterm dependencies. In lithological classification tasks, deep networks’ capabilities improve accuracy by capturing complex features and patterns in input data.  

![](images/97dd688903c0a8b38a3495e32ab2a62a4c41bb28cffe8fe9a9c6f0c7a51b2b52.jpg)  
Fig. 1. Overall structure of a deep residual network.  

This study introduces a deep residual shrinkage network (DRSN) that combines residual networks, attention mechanisms, and soft thresholding strategies. The attention mechanism enables the network to focus on vital data features, while the soft thresholding technique eliminates redundant information. To assess the model’s effectiveness, logging data from the Tarim Oilfield in northwest China are selected, and the deep residual contraction network, CNN, and artificial neural network are trained. Finally, the test set is used for model evaluation through accuracy, precision, and recall.  

# II. METHODOLOGY  

A DRSN is a variant of a deep residual network, incorporating residual networks, attention mechanisms, and soft thresholding. This network eliminates redundant information through the soft thresholding mechanism and enhances the importance of crucial data features using the attention mechanism. Building upon the residual network structure, DRSN retains cross-layer links while integrating a small subnetwork into the main structure. This subnetwork learns a set of thresholds, which are used to soft threshold the input data features. The attention mechanism calculates layers and assigns higher weights to important features, resulting in lower attention weights for redundant data. The soft thresholded output sets redundant features to zero, preserving only nonzero important features. Fig. 1 illustrates the network structure of DRSN. The residual shrinkage mechanism is a core characteristic of DRSN. It enhances the sparsity of the residual block’s output by shrinking the weights within the residual block (using a soft threshold strategy). This operation reduces the model’s complexity and mitigates overfitting issues. By constraining the sparsity of network parameters, the shrink operation can effectively alleviate overfitting problems.  

# A. Residual Networks  

In experimental research, it is observed that as the depth of the network increases, the depth level of its features also rises. Consequently, a deeper network yields better performance. However, continuously increasing the number of layers in a CNN can lead to larger training errors. When the network layers become deeper, the gradient gradually attenuates during the propagation process, eventually disappearing or becoming too small to be useful. This prevents the network from adjusting the weights of previous layers during training, causing the network to struggle to converge.  

The residual network [15] is a CNN variant that borrows the structure of cross-layer links from the Highway Network, an improved structure that simplifies the Highway Network’s form. Residual blocks with this residual structure can define a very deep network, reversing the update process to address the issue of vanishing gradients and optimize classification results. This innovative approach allows for greater network depth without the attendant training errors, resulting in improved performance.  

# B. Attention Mechanism  

Attention mechanisms can reinforce the representation of key features, making them more prominent. By assigning higher weights to significant attributes, the attention mechanism can emphasize vital aspects and suppress irrelevant ones, thus enhancing the model’s perception of crucial information and improving accuracy. Squeeze-and-excitation network (SENet) [16] is a notable attention mechanism designed to boost the performance of deep learning models. It incorporates attention mechanisms into its framework through two primary steps. Fig. 2 displays the network architecture of SENet.  

Squeeze step: In this step, SENet uses global information to learn the importance weights of each channel. Suppose the d on May 27,2025 at 10:29:56 UTC from IEEE Xplore.  Restrictions apply.  

![](images/eaabb072d66722f68170051270b514a1ae9da2e9282448b5d37e2769cf924bc9.jpg)  
Fig. 2. Schematic of the SENet-block.  

size of the input feature map is $H \ \times \ W \ \times \ C$ , where $H$ and $W$ represent the height and width, respectively, and $C$ represents the number of channels. SENet first uses a global average pooling layer to reduce the dimensionality of the input feature map, transforming it into a feature vector of size $1 \times 1 \times C$ . In this way, SENet compresses the global information of the entire feature map. Excitation step: In this step, SENet learns the importance weights of each channel through two fully connected layers. Specifically, SENet first uses a fully connected layer to map the input eigenvectors to a smaller intermediate dimension. Then, apply an activation function, such as ReLU, to introduce nonlinearity. Next, SENet uses another fully connected layer to adjust the eigenvectors of the intermediate dimension to the original number of channels and outputs it as a feature descriptor of size $1 \ \times \ 1 \ \times \ C$ . This feature descriptor contains the importance weight of each channel. Finally, SENet generates an enhanced feature map by multiplying the original feature map with the attention feature descriptors derived from the squeeze-and-excitation steps. This enables the network to adaptively reassign channel importance and enhance the individual channels’ contributions to specific tasks, thereby leading to improved classification performance.  

on the input. Next, compute the shrinkage coefficient for each convolutional layer’s weights. This coefficient is typically calculated based on the absolute value of the weights and compared with a predefined threshold. A common way to calculate the shrinkage factor is to use a soft threshold function. For each weight $w$ , the shrinkage factor $s$ is calculated as follows  

# C. Soft Thresholding (Shrinkage Mechanism)  

The soft threshold mechanism [17] involves thresholding input values and either shrinking or leaving them unchanged based on the threshold. In a deep residual shrinkage network, the shrinking process is achieved by reducing the weights within the residual block. The objective of the shrink operation is to decrease model complexity and mitigate overfitting to some extent. Deep neural networks possess numerous parameters, and their number escalates with network depth. This high-dimensional parameter space can lead to excessive complexity and is inclined to trigger overfitting issues. By implementing shrink operations, the model’s weights can be made sparser. The shrinkage process achieves weight shrinkage by thresholding and multiplying the weights, bringing smaller weight values closer to zero. This manipulation results in some model weights reaching zero, thereby reducing the number of parameters and lessening model complexity. The shrinkage process can be further broken down into the following steps: First, assuming the residual block contains multiple convolutional layers and activation functions, the block’s output is derived by performing convolution operations  

$$
s = \operatorname* { m a x } ( | w | - \lambda , 0 ) / ( | w | + \varepsilon )
$$  

where $\lambda$ is the threshold, and $\varepsilon$ is a small positive number used to avoid dividing by zero.  

Next, for each weight, multiply it by points with the contraction factor. This operation causes the smaller values in the weights to approach zero, resulting in sparsity. The weight after calculation of the shrink is  

$$
w ^ { \prime } = w * s .
$$  

Finally, the convolution operation is performed using the shrinked weights to obtain the output of the shrinked residual block. This processing causes the smaller values in the output to approach zero, resulting in weight shrinkage.  

# D. Gram Angle Field  

Gram angle field (GAF) [18] is a feature extraction method that converts 1-D time-series data into 2-D matrix representations. It captures the structure and dynamic patterns of time-series data, providing richer feature representations for machine learning tasks. GAF retains the correlation characteristics and time-dependent dependencies of the signal. It generates a density distribution map of GAF values using trigonometric operations after polar coordinate encoding in a Cartesian coordinate system. GAF has the following advantages in logging lithology prediction.  

1) GAF can effectively capture the time-series information of time-series data and convert 1-D time-series data into 2-D matrix representation. This better preserves the temporal evolution characteristics of the original data, considering not only the values of each time point but also their relative position and order.   
2) GAF can better capture the nonlinear structure and patterns in time-series data using trigonometric transformation. This allows GAF to provide more accurate feature representation when dealing with complex lithology prediction problems.   
3) Well logged data are often noisy and outliers, which can adversely affect lithology predictions. By normalizing and transforming time-series data, GAF can reduce the interference of noise to a certain extent and maintain the local and global characteristics of the data. This makes GAF have strong anti-interference ability in the face of noise and outliers and improves the accuracy and stability of lithology prediction.   
4) The resulting GAF matrix can be viewed as a 2-D image that can be visualized and analyzed by means of image processing and computer vision. This visualization can help geologists and engineers more intuitively understand the characteristics and patterns of the data and provide more insight and judgment.  

![](images/d57dadae4175f0f14205627da1b380dedae6998f942b460837461b38e1746765.jpg)  
Fig. 3. Two-dimensional image of Well 1 test set after GAF transformation  

For a given set of time-series $\begin{array} { r c l } { A } & { = } & { \{ a _ { 1 } , a _ { 2 } , \ldots , a _ { n } \} . } \end{array}$ To ensure that the inner product is not biased toward the maximum value in the sequence, first normalize the data and scale it between $( - 1 , 1 )$ , using the following formula:  

$$
a _ { i } = { \frac { ( a _ { i } - \operatorname* { m i n } A ) + ( a _ { i } - \operatorname* { m a x } A ) } { \operatorname* { m a x } A - \operatorname* { m i n } A } } .
$$  

Complete the mapping of angle and radius according to the following formula:  

$$
\left\{ \begin{array} { l l } { \theta _ { i } = \operatorname { a r c c o s } a _ { i } , - 1 \leq a _ { i } \leq 1 , } & { a _ { i } \in A } \\ { r = { \displaystyle { \frac { i } { N } } } , } & { i \in N . } \end{array} \right.
$$  

Preserving the dependence of the signal on time, the time movement increases from the upper left to the lower right, and the trigonometric function is used to generate the Gram matrix, and the angle summation of the inner product of the GAF is selected  

$$
\mathrm { G A F } = \left( \begin{array} { c c c } { \cos ( \theta 1 + \theta 1 ) } & { \ldots } & { \cos ( \theta 1 + \theta { \mathrm { n } } ) } \\ { \vdots } & { \ddots } & { \vdots } \\ { \cos ( \theta { \mathrm { n } } + \theta 1 ) } & { \ldots } & { \cos ( \theta { \mathrm { n } } + \theta { \mathrm { n } } ) } \end{array} \right) .
$$  

# III. DATA PROCESSING AND SELECTION  

The data presented in this article are extracted from China’s Tarim Oilfield, situated in the Taklamakan Desert, western China, which is abundant in oil and natural gas reserves. The lithology of the area primarily comprises mudstone, sandstone, and sand–mudstone. In this study, five logging parameters, namely, photoelectric (PE), gamma (GR), acoustic (ac), neutron (CNL), and density (DEN), are selected to predict lithology as input for deep learning models.  

This research uses logging data from two wells for model evaluation. The sampling interval of the logging data is $0 . 1 \mathrm { ~ m ~ }$ . The first and second wells selected 2000 sets of data, dividing them into a training set and a test set at a ratio of 8:2. A total of 1600 sets of logging data were used for model training, while 400 sets were reserved for model testing. Figs. 3 and 4 display 2-D images of the test set after GAF transformation (serving as examples). The training set consisted of 1600 groups prior to the transformation, and the data volume expanded to $1 6 0 0 \times$ 1600 groups after the GAF transformation (this method exponentially enlarges the dataset). The lithology value is output diagonally.  

![](images/4b45a09f00bc595b1b26d9ff8b9ba9d8df506315eaaeadeb19a572941b6ff73c.jpg)  
Fig. 4. Two-dimensional image of Well 2 test set after GAF transformation.  

![](images/a33f623e57adf54b854d5690a4f193ae051873498147fe25a9828dd3b08f6d1b.jpg)  
Fig. 5. Prediction results and confusion matrices of various models of Well 1.  

# IV. ANALYSIS OF RESULTS  

We used accuracy, precision, and recall to assess the performance of the model [19]. In this article, the network configuration of DRSN is as follows: a DRSN with 100 layers (excluding the input and output layers) is constructed. The number of residual blocks is set to 10, and each residual block’s depth is arranged to be 10. The threshold in the soft thresholding is fixed at 0.1. The learning rate is set to 0.01, and the number of iterations is configured to be 1000. The network configuration of the CNN is as follows: the convolutional layer is set to 10. The number of iterations is 1000, and the learning rate is set to 0.01. In this study, the input to the DRSN-GAF model is an image, wherein a $3 \times 3$ convolutional kernel is used to process the image, effectively preserving the detailed information. The 1-D logging data input from the DRSN model is used to extract data features using a $3 \times 1$ convolution kernel. The experimental results are as follows.  

In the test results of machine learning models for Well 1, as depicted in Fig. 5, the accuracy of DSCN-GAF stands at $9 6 . 0 0 \%$ , surpassing the DRSN model $( 8 9 . 7 5 \% )$ and the CNN model $( 8 1 . 2 5 \% )$ . It is evident from the figure that the DSCN-GAF model has fewer incorrect layers. The DRSN identified $8 1 8 ~ \mathrm { ~ m ~ }$ of lithology with numerous false horizons, which were continuous. The superior performance of DSCN-GAF over DRSN can be attributed to the fact that GAF effectively captures the time-series information in time-series data and transforms 1-D time-series data into a 2-D matrix representation. using trigonometric transformation, GAF can better comprehend the nonlinear structures and patterns within time-series data. The DRSN model demonstrates better performance than the CNN model in experiments, which is attributed to the introduction of residual networks, attention mechanisms, and soft threshold strategies. In the test results of machine learning models for Well 2, as illustrated in Fig. 6, the accuracy of DSCN-GAF stands at $9 6 . 5 0 \%$ , outperforming the DRSN model $( 8 9 . 2 5 \% )$ and the CNN model $( 8 0 . 5 0 \% )$ . The figure clearly demonstrates the superiority of the DSCN-GAF model over the DRSN model. The DRSN encountered numerous consecutive misidentified horizons at $1 0 7 3 \mathrm { m }$ . The GAF algorithm can be used to convert the 1-D logging data and input it into the DRSN model, significantly enhancing recognition accuracy. This enables the identification of incorrect horizons to be corrected. As evident in the confusion matrix, the DRSN model outperforms the CNN model, indicating that the proposed model in this article exhibits a superior application effect.  

![](images/499e2cdfae09154bdf5a070a412655b5487ebbb3bf248cde71298f421bad6cf5.jpg)  
Fig. 6. Prediction results and confusion matrices of various models of Well 2.  

# V. CONCLUSION  

A comprehensive series of experiments corroborate that the innovative aspects of this article effectively enhance the accuracy of the traditional CNNs. The residual network mitigates gradient vanishing, the attention mechanism allocates crucial information to the machine learning model, and soft thresholding reduces logging data noise. DRSN demonstrates several advantages over common CNNs: it can construct networks with depths exceeding $^ { 1 0 0 + }$ layers, addressing the challenges of training deep networks. Residual connections enhance the network structure and improve its capability to extract features. DRSN is modular and easily expandable. ResNet resolves deep network issues through residual thinking, resulting in a rational network structure that outperforms ordinary CNNs and ANNs in terms of performance and stability. GAF effectively captures time-series information and transforms 1-D data into a 2-D matrix, preserving temporal evolution characteristics. It uses trigonometric transformations to capture nonlinear structures and patterns, providing more accurate feature representation for complex lithology prediction problems. GAF normalizes and transforms time-series data, reducing noise interference and maintaining data’s local and global characteristics. This enhances GAF’s robustness to noise and outliers, improving lithology prediction accuracy and stability. However, DRSN does have some limitations: its performance is sensitive to specific hyperparameters, necessitating extensive experimentation and tuning.  

# REFERENCES  

[1] X. Zhao et al., “A new method for lithology identification of fine grained deposits and reservoir sweet spot analysis: A case study of Kong 2 member in Cangdong sag, Bohai Bay Basin, China,” Petroleum Exploration Develop., vol. 44, no. 4, pp. 524–534, Aug. 2017. [2] S. Kuhn, M. J. Cracknell, and A. M. Reading, “Lithological mapping via random forests: Information entropy as a proxy for inaccuracy,” ASEG Extended Abstr., vol. 2016, no. 1, pp. 1–4, Dec. 2016. [3] V. Purohit, A. Bisui, and R. Sharma, “Effectiveness of machine learning algorithms to use unconstrained data for lithofacies prediction in carbonate reservoir,” in Proc. AGU Fall Meeting Abstr., 2019, pp. 23–42. [4] G. Zhang, Z. Wang, and Y. Chen, “Deep learning for seismic lithology prediction,” Geophys. J. Int., vol. 215, no. 2, pp. 1368–1387, Aug. 2018. [5] N. Liu, T. Huang, J. Gao, Z. Xu, D. Wang, and F. Li, “Quantumenhanced deep learning-based lithology interpretation from well logs,” IEEE Trans. Geosci. Remote Sens., vol. 60, 2022, Art. no. 3085340. [6] C. Jiang, D. Zhang, and S. Chen, “Lithology identification from welllog curves via neural networks with additional geologic constraint,” Geophysics, vol. 86, no. 5, pp. IM85–IM100, Sep. 2021. [7] Z. Liu, J. Zhang, Y. Li, G. Zhang, Y. Gu, and Z. Chu, “Lithology prediction of one-dimensional residual network based on regularization constraints,” J. Petroleum Sci. Eng., vol. 215, Aug. 2022, Art. no. 110620. [8] X. Zhang, J. Wen, Q. Sun, Z. Wang, L. Zhang, and P. Liang, “Lithology identification technology of logging data based on deep learning model,” Earth Sci. Informat., vol. 16, no. 3, pp. 2545–2557, Sep. 2023. [9] L. Song, X. Yin, and L. Yin, “Reservoir lithology identification based on improved adversarial learning,” IEEE Geosci. Remote Sens. Lett., vol. 20, pp. 1–5, 2023.   
[10] A. Apicella, F. Donnarumma, F. Isgrò, and R. Prevete, “A survey on modern trainable activation functions,” Neural Netw., vol. 138, pp. 14–32, Jun. 2021.   
[11] N. Srivastava, G. Hinton, A. Krizhevsky, I. Sutskever, and R. Salakhutdinov, “Dropout: A simple way to prevent neural networks from overfitting,” J. Mach. Learn. Res., vol. 15, no. 1, pp. 1929–1958, 2014.   
[12] Y. Wang, F. Sun, and B. Li, “Multiscale neighborhood normalizationbased multiple dynamic PCA monitoring method for batch processes with frequent operations,” IEEE Trans. Autom. Sci. Eng., vol. 15, no. 3, pp. 1053–1064, Jul. 2018.   
[13] K. Huang, S. Li, W. Deng, Z. Yu, and L. Ma, “Structure inference of networked system with the synergy of deep residual network and fully connected layer network,” Neural Netw., vol. 145, pp. 288–299, Jan. 2022.   
[14] M. Zhao, S. Zhong, X. Fu, B. Tang, and M. Pecht, “Deep residual shrinkage networks for fault diagnosis,” IEEE Trans. Ind. Informat., vol. 16, no. 7, pp. 4681–4690, Jul. 2020.   
[15] K. He, X. Zhang, S. Ren, and J. Sun, “Deep residual learning for image recognition,” in Proc. IEEE Conf. Comput. Vis. Pattern Recognit. (CVPR), Jun. 2016, pp. 770–778.   
[16] J. Hu, L. Shen, and G. Sun, “Squeeze-and-excitation networks,” in Proc. IEEE/CVF Conf. Comput. Vis. Pattern Recognit., Jun. 2018, pp. 7132–7141.   
[17] L. Zhang, B. Wang, X. Yuan, and P. Liang, “Remaining useful life prediction via improved CNN, GRU and residual attention mechanism with soft thresholding,” IEEE Sensors J., vol. 22, no. 15, pp. 15178–15190, Aug. 2022.   
[18] Q. Li, Y. Zou, G. H. Long, and W. Wu, “Ferromagnetic resonance overvoltage identification method based on Gram angle field,” Energy Rep., vol. 8, pp. 546–558, Sep. 2022.   
[19] B. Juba and H. S. Le, “Precision-recall versus accuracy and the role of large data sets,” in Proc. AAAI Conf. Artif. Intell., vol. 33, no. 1, 2019, pp. 4039–4048.  