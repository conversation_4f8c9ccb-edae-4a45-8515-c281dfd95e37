# Category-Wise Sequence Correlation Filter for Lithology Identification With Well Logs  

<PERSON> , Member, IEEE, <PERSON><PERSON> , <PERSON><PERSON><PERSON> and <PERSON><PERSON> , Graduate Student Member, IEEE  

Abstract— Well log data play a crucial role in lithology identification, given their nature as petrophysical measurements of stratigraphy that reflect lithological successions and depositional processes. Extracting recognizable features from well logs has become a focal point in lithology identification tasks. Recent advancements in machine learning (ML) have improved feature extraction by incorporating neighborhood information, particularly through filtering techniques that integrate sequence dependencies from well logs. However, traditional filters are often constrained by predefined theoretical models, posing adaptability challenges across varied reservoir conditions. To address these limitations, we propose a novel category-wise sequence correlation (CSC) filter for lithology identification, which is data-driven and model-free. Notably, the CSC filter captures the sequence structural variations arising from category heterogeneity by utilizing labeling information derived from well logs. Building on this, the CSC filter banks (CSCFBs) model is developed. It uses a series of CSCFBs as 1-D convolution templates acting on each logging curve to extract discriminative features associated with lithological categories. Application studies on several oil and gas fields demonstrate that the CSCFB model outperforms competitors in classification performance, proving its effectiveness and utility across diverse reservoir types.  

Index Terms— Correlation filter, lithology identification, machine learning (ML), sequence structural feature, well log data.  

# I. INTRODUCTION  

ITHOLOGY identification is an important foundational Various lithologies have different contributions to oil and gas accumulation, so accurate lithology identification is critical for reservoir evaluation and deployment of exploitation plans [1], [2].  

The traditional methods for lithology identification determine the lithology type by detailed laboratory analysis of  

Received 29 July 2024; revised 21 November 2024; accepted 2 March 2025.   
Date of publication 4 March 2025; date of current version 21 March 2025. This   
work was supported in part by the Science Foundation of China University   
of Petroleum, Beijing, under Grant 2462025XKBH015 and in part by the   
Theory of Hydrocarbon Enrichment under Multi-Spheric Interactions of the   
Earth under Grant THEMSIE04010101. (Corresponding author: Xin Zhang.) Zitong Zhang is with the State Key Laboratory of Petroleum Resources and   
Engineering and the College of Geoscience, China University of Petroleum   
(Beijing), Beijing 102249, China. Xin Zhang is with the Department of Statistics and Epidemiology, Grad  
uate School of the PLA General Hospital, Beijing 100853, China (e-mail:   
<EMAIL>). Chunlei Zhang is with Beijing Zhongdi Runde Petroleum Technology   
Company Ltd., Beijing 100083, China. Hanlin Feng is with the School of Computer and Information Technology,   
Northeast Petroleum University, Daqing 163318, China. Digital Object Identifier 10.1109/TGRS.2025.3547940  

drilling cuts or core samples. However, this method is labor-intensive, bias-prone, and limited by the high cost of coring [3]. Well logging is a technique widely used in hydrocarbon exploration, giving useful information related to lithology, porosity, permeability, fluid composition, and hydrocarbon saturation by conducting physical and chemical measurements on rocks [4], [5]. As the cornerstone of reservoir research, well log data encompass continuous petrophysical information of the whole well with several advantages including all-around coverage, high vertical resolution, and convenient data acquisition [6]. Hence, lithology identification methods from well logs have appealed to increasing interest among researchers [7].  

Lithology identification based on well logs aims to establish mapping relationships between logging parameters and lithological categories by analyzing parameters that are sensitive to lithology. Different lithologies exhibit distinct log response characteristics. Early studies in lithology identification based on well logs utilized empirical or theoretical models such as cross plots and statistical analysis [8], [9], [10], [11]. Despite their utility, cross plots depend heavily on expertise and human experience, while statistical analysis methods often struggle to adequately model the complex relationship between logging parameters and lithology, which leads to notable deviations. Moreover, as geological conditions become more intricate and the volume of well log data increases, these early logging interpretation methods show great limitations.  

The machine learning (ML) methods facilitate the automatic learning of petrophysical characteristics inherent in well log data. Such methods alleviate excessive reliance on domain expertise and enhance the efficiency of lithology identification [12], [13]. Currently, there are two types of data-driven mainstream methods with logging data: one only considers the relationship between lithology-related features and categories at individual points, and the other constructs a classification model based on spatial sequences [14].  

In the lithology identification with logging data points, a wide variety of conventional ML methods have been applied, including $k$ -nearest neighbors (kNNs), support vector machine (SVM), and tree-based ensemble methods [15], [16], [17]. Xie et al. [18] evaluated five typical ML methods for lithology recognition and concluded that gradient tree boosting could provide higher classification accuracy for distinguishing sandstone classes. Nevertheless, accurately identifying lithology remains challenging due to the similarity in properties among adjacent rocks in the strata. Conventional ML methods typically predict lithology using measured properties at each point in depth, whereas few take the influence of the neighboring formation into account. This limitation seriously hampers the performance improvement of lithology identification [19].  

In the field of spatial modeling and classification based on well logs, feature extraction methods, such as deep learning, show great advantages in capturing spatial structures. These methods excel in learning from neighborhood information, thereby effectively extracting features that align closely with geological patterns. Several feature extraction methods have been successfully employed for lithology identification tasks, including convolutional neural network (CNN) [20], recurrent neural network (RNN) [21], and long short-term memory (LSTM) [22]. The studies above show that integrating neighborhood information into feature engineering (i.e., neighborhood feature engineering) to characterize the spatial structure can overcome the issue of insufficient learning caused by a point-to-point learning strategy. Despite their high performance, deep learning-based feature extraction methods necessitate a large number of labeled samples, which are often scarce in reservoir studies. Moreover, the black-box nature of deep learning poses challenges in terms of interpretability [23].  

Currently, there has been a surge in research on neighborhood feature engineering, particularly in the form of filtering techniques. These methods have proven effective in extracting local patterns, which are essential for understanding subsurface geological formations. For example, Yang et al. [24] employed the Haar wavelet transform to extract lithologic interfaces from well log data, improving the accuracy of metamorphic rock classification. Similarly, Sun et al. [25] adopted wavelet transform to process logging parameters, thus enhancing model interpretability and addressing stratigraphic feature limitations. These efforts demonstrate how filtering methods can significantly enhance the performance of ML models. However, despite these advancements, conventional filters are based on theoretical assumptions and design principles that may not be suitable for complex well log data. In other words, these theoretical models are not universally applicable in complex geological conditions. In contrast, data-driven filters excel in this regard by avoiding rigid model assumptions, ensuring the extraction of features that are more closely aligned with the unique characteristics of logging data across various reservoirs. Therefore, it is necessary to develop a data-driven filter for lithology identification.  

In statistics literature, “correlation” is a statistical term used to describe the strength and direction of the linear relationship between two random variables. In a broader sense, measuring the degree of correlation is apt to measure a specific distance between variable pairs, revealing the nature of data [26]. Many scholars have used correlation metrics as filters to portray dependencies between features and class variables, successfully applying them to feature engineering [27], [28]. In addition, in geostatistics, the variogram serves as a correlation metric to express spatial variability, reflecting the degree of similarity between data values across different distance ranges [29]. Garrigues et al. [30] employed variogram modeling to characterize and quantify spatial heterogeneity at the landscape level. Kongwung and Ronghe [31] utilized geostatistical modeling based on the variogram to identify potential areas of hydrocarbon accumulations.  

Based on insights gained from prior research, a pertinent question emerges: can enhanced benefits be derived from designing feature extraction approaches directly based on correlation metrics? Fu et al. [26] gave a positive answer to this question in their work. Given the multifaceted nature of geological formations and the intricate data recorded by well logs, leveraging correlation as a filter to capture spatial variability holds significant promise.  

In this study, we propose a novel filter named category-wise sequence correlation (CSC) filter based on well logs. The CSC filter is designed to represent the internal differences among logging parameters by leveraging labeling information. Furthermore, we develop a CSC filter banks (CSCFBs) model for lithology identification tasks. It employs the filter banks as the 1-D convolution operator to extract recognizable features, which improves the feature discriminability and achieves remarkable results. The main contributions of this article can be summarized as follows.  

1) A data-driven and model-free CSC filter is proposed, which enhances the discrimination of different lithology types in logging features by category-wise filtering, improving the performance of lithology identification.   
2) The CSCFB model adopts a 1-D convolution strategy to extract category-aware features, which is effective and less time-consuming compared to iterative deep learning methods.   
3) Experimental results on three well log datasets demonstrate that the CSCFB model outperforms competitors in terms of adaptability and utility across different reservoir types.  

The rest of the article is organized as follows. In Section II, the CSC filter and its derived lithology identification model are proposed. In Section III, the geological background and logging data from three gas fields are introduced in detail. The experiment setup and comparative results are illustrated in Section IV. Finally, Section $\mathrm { v }$ concludes the article.  

# II. METHODOLOGY  

# A. GSC Filter  

Given a sequence data $\begin{array} { l } { \mathcal { L } ~ = ~ \left[ X _ { 1 } , X _ { 2 } , \ldots , X _ { n } \right] } \end{array}$ with $n$ elements. Since $X _ { i } , i = 1 , 2 , \dotsc , n$ are sequence-dependent, $X _ { i }$ can be regarded as a function of its position variable $u$ . In other words, one has a model  

$$
\mathcal { L } = f ( u , \epsilon )
$$  

such that ${ \mathcal { L } } | _ { u = i } \ { \triangleq } \ X _ { i }$ , where $\epsilon$ represents random error. For convenience, we denote $\mathcal { L }$ as a sequence $[ \mathcal { L } ( u ) ] _ { u }$ with the random error term $\epsilon$ being omitted in the following.  

Considering sequence correlations among elements $X _ { i } , i =$ $1 , 2 , \ldots , n$ at multiple scales, we define a novel filter named global sequence correlation (GSC) filter to capture the structural features of $\mathcal { L }$ . Specifically, for any given step size $t$ , the GSC at the scale $t$ can be given by the Pearson Correlation  

![](images/7765ec9c30d4ce8390fcab167497c519a85fc687141a822e2ad9c1cbe3134d14.jpg)  
Fig. 1. Schematic of GSC filter.  

Coefficient between sequences $\left[ \mathcal { L } ( u ) \right]$ and $[ \mathcal { L } ( u + t ) ]$ . That is,  

$$
\rho ( t ) = \frac { \mathbb { E } \left[ \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ( u + t ) \widetilde { \mathcal { L } } ( u ) \right] } { \sqrt { \mathbb { E } \left[ \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ^ { 2 } ( u + t ) \right] } \sqrt { \mathbb { E } \left[ \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ^ { 2 } ( u ) \right] } }
$$  

where $\widetilde { \mathcal { L } } ( u + t )$ is an element in sequence $[ \widetilde { \mathcal { L } } ( u + t ) ] , \widetilde { \mathcal { L } } ( u )$ is an  ement in sequence $[ \widetilde { \mathcal { L } } ( u ) ]$ , and $u$ belo negs to reg en of interest (ROI) as follows  

$$
\mathcal { A } = \{ u = i | \operatorname* { m a x } ( 1 , - t ) \leq i \leq \operatorname* { m i n } ( n , n - t ) \} .
$$  

$\widetilde { \mathcal { L } } ( u )$ and $\widetilde { \mathcal { L } } ( u + t )$ correspond to the centralized versions of $\mathcal { L } ( u )$ and $\mathcal { L } ( u + t )$ , respectively. That is,  

$$
\begin{array} { c } { \widetilde { \mathcal { L } } ( u ) = \mathcal { L } ( u ) - \mathbb { E } ( \mathcal { L } ( u ) ) } \\ { \widetilde { \mathcal { L } } ( u + t ) = \mathcal { L } ( u + t ) - \mathbb { E } ( \mathcal { L } ( u + t ) ) . } \end{array}
$$  

Note: If the random error term $\epsilon$ is reintroduced, the numerator $\begin{array} { r } { \mathbb { E } [ \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ( u + t ) \widetilde { \mathcal { L } } ( u ) ] } \end{array}$ in (2) can be written as $\begin{array} { r } { \mathbb { E } _ { \epsilon _ { 1 } , \epsilon _ { 2 } } [ \sum _ { u \in \mathcal { A } } \widetilde { f } ( u + t , \epsilon _ { 1 } ) \widetilde { f } ( u , \epsilon _ { 2 } ) ] , } \end{array}$ , and the denominators $\mathbb { E } [ \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ^ { 2 } ( u + t ) ]$ and $\mathbb { E } [ \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ^ { 2 } ( u ) ]$ can be rewritten as $\begin{array} { r } { \mathbb { E } _ { \epsilon _ { 1 } } [ \sum _ { u \in \mathcal { A } } \widetilde { f } ^ { 2 } ( u + t , \epsilon _ { 1 } ) ] } \end{array}$ and $\begin{array} { r } { \mathbb { E } _ { \epsilon _ { 2 } } [ \sum _ { u \in \mathcal { A } } \widetilde { f } ^ { 2 } ( u , \epsilon _ { 2 } ) ] } \end{array}$ , respectively, wh e $\widetilde { f } ( u + t , \epsilon _ { 1 } ) = f ( u + t , \epsilon _ { 1 } ) - \mathbb { E } _ { i \in \mathcal { A } , \epsilon } [ f ( i , \epsilon ) ] ,$ · and $\widetilde { f } ( u , \epsilon _ { 2 } ) = f ( u , \epsilon _ { 2 } ) - \mathbb { E } _ { i \in \mathcal { A } , \epsilon } [ f ( i , \epsilon ) ]$ . If the $\epsilon$ is just an add ieve noise like $f ( u , \epsilon ) = f ( u ) + \epsilon$ , considering that the noise in $\widetilde { \mathcal { L } } ( u + t )$ is irrelevant to the noise in $\widetilde { \mathcal { L } } ( u ) _ { \perp }$ , we can say that $\begin{array} { r } { \mathbb { E } [ \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ( u + t ) \widetilde { \mathcal { L } } ( u ) ] = \sum _ { u \in \mathcal { A } } \widetilde { \mathcal { L } } ( u + t ) \widetilde { \mathcal { L } } ( u ) } \end{array}$ , i.e., the autocorrelationeis a speceial case of the eproposedeGSC.  

The definition of GSC characterizes the structure and strength of variation of a sequence at each fixed scale $t$ . One can see that $\rho ( t ) ~ = ~ \rho ( - t )$ . To further obtain information about the multiscale structure of the sequence, we compute a series of correlations at bi-direction and specified multiple scales $t$ , where the two directions correspond to the positive and negative $t$ -values, respectively. Specifically, the offset $t$ takes values in the following set:  

$$
{ \mathcal { T } } = \{ t \in \mathbb { Z } | - t _ { 0 } \leq t \leq t _ { 0 } , t _ { 0 } \in \mathbb { Z } _ { + } \}
$$  

and the GSC filter is formally defined as follows  

$$
\mathcal { F } = [ \rho ( t ) ] _ { t \in T } .
$$  

Fig. 1 displays the construction of the proposed filter with the parameter setting $t _ { 0 } ~ = ~ 2$ . The resulting GSC filter is of size $1 \times 5$ with the center point as the coordinate origin and upward as the positive direction. All point pairs in the sequence $\mathcal { L }$ with offset $t ~ = ~ + 2$ are collected, see yellow background region in Fig. 1.  

The values at the first position in these pairs are concatenated to form the head sequence $\mathcal { L } _ { 1 }$ , and similarly, the values at the last position are concatenated to form the tail sequence $\mathcal { L } _ { 2 }$ . The filter value $\rho ( t )$ at $t = 2$ is then calculated by the Pearson Correlation Coefficient between $\boldsymbol { \mathrm { [ } \mathcal { L } ( u ) \vert }$ and $[ \mathcal { L } ( u + 2 ) ]$ . It is worth noting that the GSC filter is data-driven and thus significantly different from the Median filter, Gabor filter, and other filters constrained by specific theoretical models.  

# B. CSC Filter  

Denote $y _ { i } \in \{ 1 , 2 , \ldots , C \}$ by the category label of element $X _ { i }$ in the sequence data $\mathcal { L }$ , where $i = 1 , 2 , \dotsc , n$ . Assume there is heterogeneity in $\mathcal { L }$ . The GSC filter uses the complete sequence as the ROI to characterize the sequence structure, ignoring the differential structural information due to category heterogeneity. Therefore, in the classification task, it is necessary to utilize the labeling information of the samples $X _ { i } , i ~ = ~ 1 , 2 , \ldots , n$ for a more detailed portrayal of the different categories.  

Before formally giving CSC filter $\textstyle \mathcal { K } _ { c }$ , we first rewrite the global ROI in (3) as the ROI $\boldsymbol { \mathcal { A } } _ { c }$ under the category strategy. Denoting $\mathcal { T } _ { c }$ the index set for training samples in class $c$ , and the patch size is set to $1 \times \omega$ , then we have  

$$
\mathcal { A } _ { c } = \left\{ u = i | i , i ^ { \prime } \in \bigcup _ { j \in \mathcal { T } _ { c } } \Omega _ { j } \right\} , \quad i ^ { \prime } = i + t
$$  

where $\Omega _ { i }$ corresponds to a $1 \times \omega$ patch neighborhood of the $i$ th training sample, with $i$ being the center index of the patch. Note that, $\boldsymbol { \mathcal { A } } _ { c }$ is the union of the $1 \times \omega$ patches from all training samples that belong to class $c$ .  

Similar to (2) and (6), the CSC filter $\boldsymbol { \mathcal { K } } _ { c } = [ \rho _ { c } ( t ) ] _ { t \in \mathcal { T } }$ can be given with  

$$
\rho _ { c } ( t ) = \frac { \mathbb { E } \Big [ \sum _ { u \in \mathcal { A } _ { c } } \widetilde { \mathcal { L } } ( u + t ) \widetilde { \mathcal { L } } ( u ) \Big ] } { \sqrt { \mathbb { E } \Big [ \sum _ { u \in \mathcal { A } _ { c } } \widetilde { \mathcal { L } } ^ { 2 } ( u + t ) \Big ] } \sqrt { \mathbb { E } \Big [ \sum _ { u \in \mathcal { A } _ { c } } \widetilde { \mathcal { L } } ^ { 2 } ( u ) \Big ] } } .
$$  

Authorized licensed use limited to: China University of Petroleum. Downloaded on May 28,2025 at 14:27:32 UTC from IEEE Xplore.  Restrictions apply.  

![](images/ef018e964832dfd8aa9a9dca66e333d5fe5863c88272bf46953ca5a4ede40e73.jpg)  
Fig. 2. Schematic of CSC filter. The green box on the right shows the construction process of ROI for class 2 $( \mathbf { \mathcal { A } } _ { 2 } )$ . Note that, $\mathbf { \mathcal { A } } _ { 2 }$ is the union of the $1 \times \omega$ patches of all training samples belonging to class 2.  

![](images/5031e926faa766ca053ae93fc8fffca954e56a02e3d7393632b615999053aa57.jpg)  
Fig. 3. Workflow of lithology identification based on CSCFB model.  

After that, a total of $C$ CSC filters $\mathcal { K } _ { 1 }$ , $\kappa _ { 2 } , \ldots , \kappa _ { C }$ are generated, as shown in Fig. 2.  

# C. Workflow of Lithology Identification  

In the lithology identification task from well logs, each logging parameter provides unique geological information. Considering the structural differences of different logging curves in various lithology categories, we develop a lithology identification model based on CSC filter banks, namely, CSCFBs model. The workflow of the CSCFB model for lithology identification is shown in Fig. 3.  

1) Mathematical Representation of Well Logs: Denote the well log data by  

$$
\mathbf { X } = \left[ \begin{array} { c c c c } { x _ { 1 } ^ { ( 1 ) } } & { x _ { 1 } ^ { ( 2 ) } } & { \cdots } & { x _ { 1 } ^ { ( p ) } } \\ { x _ { 2 } ^ { ( 1 ) } } & { x _ { 2 } ^ { ( 2 ) } } & { \cdots } & { x _ { 2 } ^ { ( p ) } } \\ { \vdots } & { \vdots } & { \ddots } & { \vdots } \\ { x _ { n } ^ { ( 1 ) } } & { x _ { n } ^ { ( 2 ) } } & { \cdots } & { x _ { n } ^ { ( p ) } } \end{array} \right] \triangleq \left[ \mathbf { X } ^ { ( 1 ) } , \mathbf { X } ^ { ( 2 ) } , \ldots , \mathbf { X } ^ { ( p ) } \right] \in \mathbb { R } ^ { p }
$$  

where $\mathbf { X } ^ { ( j ) } ~ = ~ [ x _ { 1 } ^ { ( j ) } , x _ { 2 } ^ { ( j ) } , \dots , x _ { n } ^ { ( j ) } ]$ , xn( j)] with n elements represents the sequence data along the depth direction of the $j$ th logging parameter. Then, the GSC information of $p$ logging curves $[ \mathbf { X } ^ { ( 1 ) } , \mathbf { X } ^ { ( 2 ) } , \ldots , \mathbf { X } ^ { ( p ) } ]$ can be mined by constructing corresponding $p$ GSC filters. As for the information extraction of Local Sequence Correlation based on category, the labeling information $y _ { i } ~ \in ~ \{ 1 , 2 , \dotsc ~ . \dotsc , C \}$ of each sample $\pmb { x } _ { i } ~ = ~ [ x _ { i } ^ { ( 1 ) } , x _ { i } ^ { ( 2 ) } , \dots , x _ { i } ^ { ( p ) } ]$ should be provided additionally. In practice, only the labels of the training data $\mathbf { X } ^ { \mathrm { ( t r ) } }$ are available. Let $\mathbf { X } ^ { \mathrm { ( t r ) } }$ and $\mathbf { X } ^ { \left( \mathrm { t e } \right) }$ denote the training and testing samples, respectively. Therefore, the CSC filters are computed based on $\mathbf { X } ^ { \mathrm { ( t r ) } }$ and applied to both $\mathbf { X } ^ { \mathrm { ( t r ) } }$ and $\mathbf { X } ^ { \left( \mathrm { t e } \right) }$ .  

2) Construct CSCFBs: Local logging curves corresponding to different lithologies exhibit various intrinsic structures, and such structural information is closely related to the depth of the formation in which they are located.  

Section II-B, we can generate the CSCFB $\begin{array} { r l } { \kappa ^ { ( j ) } } & { { } = } \end{array}$ $\{ \mathcal { K } _ { 1 } ^ { ( j ) } , \mathcal { K } _ { 2 } ^ { ( j ) } , \ldots , \mathcal { K } _ { C } ^ { ( j ) } \}$ for each logging curve $\mathbf { X } ^ { ( j ) }$ , where each CSC filter $\mathcal { K } _ { c } ^ { ( j ) } = [ \rho _ { c } ( t ) ] _ { t \in \mathcal { T } }$ with respect to category $c$ can be easily obtained by (8) if we treat $\mathbf { X } ^ { ( j ) }$ as $\mathcal { L }$ . Finally, a total of $p \times C$ data-driven CSC filters are collected as $\cup _ { j = 1 } ^ { p } \kappa ^ { ( j ) }$ .  

The diversity of filters is beneficial for a richer feature representation. Recall (5) and (7), the tuning parameters $t _ { 0 }$ for filter size and $\omega$ for neighborhood length (patch size) should be carefully controlled. The sizes of the filter and patch constrain each other; a too-large patch size may result in category features containing an excessive amount of interfering information that does not belong in the category, while a patch size that is too small may not satisfy the basic requirements for constructing a filter, which can lead to excessive sparsity in the filter, rendering it incapable of representing the entire structure. Unless otherwise stated, the optimal parameters in this article are chosen by cross-validation.  

3) Feature Extraction: Filters can be used for feature extraction through a sliding convolution operation on the data. Convolution was initially applied to signals and linear systems with the purpose of characterizing the alterations that occur in a linear time-varying body when stimulated by a signal. 1-D convolution operation, also known as 1-D cross correlation operation, extracts abstract features for each local neighborhood by sliding a 1-D convolution template over the sequence data.  

Definition 1 (1-D Convolution Operator): Given a 1-D sequence data $\textbf { Z } \in \mathbb { R } ^ { m }$ , it can be viewed as a 1-D signal defined over the discrete domain $[ 1 , \ldots , m ]$ , and extended to $\mathbb { Z }$ by padding 0. Given a 1-D kernel $\pmb { S } \in \mathbb { R } ^ { 1 \times s }$ , it can be viewed as a 1-D signal defined over the discrete domain $[ - s _ { 0 } , \ldots , s _ { 0 } ]$ , where $s = 2 s _ { 0 } + 1$ represents the convolution kernel size. Let $\otimes$ denote the convolution operator between two 1-D signals, the specific calculations are as follows:  

$$
\begin{array} { c } { \mathbf { F } = \mathbf { Z } \otimes \mathbf { \mathcal { S } } } \\ { \displaystyle \mathbf { F } ( i ) : = \sum _ { r = - s _ { 0 } } ^ { s _ { 0 } } \mathbf { Z } ( i + r ) \cdot \mathbf { \mathcal { S } } ( r ) } \end{array}
$$  

where $\mathbf { F }$ is the resulting feature vector after convolution, $i$ is the position coordinate of $\mathbf { F }$ , and $r$ is the position coordinate of the 1-D kernel $s$ .  

Denote $\mathbf { I } _ { i } ^ { ( j ) } = [ x _ { i + r } ^ { ( j ) } ] _ { r = - q _ { 0 } } ^ { q _ { 0 } }$ by a $1 \times q$ neighborhood of $x _ { i } ^ { ( j ) }$ where $q = 2 q _ { 0 } + 1$ .+Then, $\bar { \mathbf { I } } _ { i } ^ { ( j ) }$ is used as the region to be convolved for the ith sample in the $j$ th logging curve. A total of $p$ CSCFBs $\kappa = \{ \kappa ^ { ( j ) } \} _ { j = 1 } ^ { p }$ with $\pmb { \mathcal { K } } ^ { ( j ) } = \{ \mathcal { K } _ { 1 } ^ { ( j ) } , \mathcal { K } _ { 2 } ^ { ( j ) } , \ldots , \mathcal { K } _ { C } ^ { ( j ) } \}$ are employed for the feature extraction on each $\begin{array} { r } { \mathbf { I } _ { i } ^ { ( j ) } , i ~ = } \end{array}$ $1 , 2 , \ldots , n$ . The following is the detailed organization process:  

$$
\begin{array} { l } { { \displaystyle { \cal F } _ { i } ^ { ( 1 ) } = \left\{ { \bf I } _ { i } ^ { ( 1 ) } \otimes { \cal K } _ { 1 } ^ { ( j ) } , { \bf I } _ { i } ^ { ( 1 ) } \otimes { \cal K } _ { 2 } ^ { ( j ) } , \ldots , { \bf I } _ { i } ^ { ( 1 ) } \otimes { \cal K } _ { C } ^ { ( j ) } \right\} _ { j = 1 } ^ { p } } } \\ { { \displaystyle { \cal F } _ { i } ^ { ( 2 ) } = \left\{ { \bf I } _ { i } ^ { ( 2 ) } \otimes { \cal K } _ { 1 } ^ { ( j ) } , { \bf I } _ { i } ^ { ( 2 ) } \otimes { \cal K } _ { 2 } ^ { ( j ) } , \ldots , { \bf I } _ { i } ^ { ( 2 ) } \otimes { \cal K } _ { C } ^ { ( j ) } \right\} _ { j = 1 } ^ { p } } } \\ { { \vdots } } \\ { { \displaystyle { \cal F } _ { i } ^ { ( p ) } = \left\{ { \bf I } _ { i } ^ { ( p ) } \otimes { \cal K } _ { 1 } ^ { ( j ) } , { \bf I } _ { i } ^ { ( p ) } \otimes { \cal K } _ { 2 } ^ { ( j ) } , \ldots , { \bf I } _ { i } ^ { ( p ) } \otimes { \cal K } _ { C } ^ { ( j ) } \right\} _ { j = 1 } ^ { p } } } \end{array}
$$  

where feature vector $\mathbf { I } _ { i } ^ { ( j ) } \otimes \mathcal { K } _ { c } ^ { ( j ) } \ \in \ \mathbb { R } ^ { a }$ , with $a$ being the dimension of the feature vector that depends on the step size of the 1-D convolution. The feature vectors Fi(1), Fi(2), $F _ { i } ^ { ( 1 ) } , F _ { i } ^ { ( 2 ) } , \ldots , F _ { i } ^ { ( p ) }$ of sample $\pmb { x } _ { i } = [ x _ { i } ^ { ( 1 ) } , x _ { i } ^ { ( 2 ) } , \dots , x _ { i } ^ { ( p ) } ]$ are concatenated to form the final feature set $\begin{array} { r } { \dot { F } = \bigcup _ { j = 1 } ^ { p } F _ { i } ^ { ( j ) } } \end{array}$ containing $\boldsymbol { p } ^ { 2 } \times \boldsymbol { C }$ extracted feature vectors, that s, the cardinality of the set $F$ is $a p ^ { 2 } C$ . Obviously, each feature vector $F _ { i } ^ { ( j ) }$ is a combined response to the lithologic category and logging parameter and is, therefore, more recognizable, practical, and interpretable.  

4) ML Classifiers: The recognizable features obtained in Section II-C3 are fed to the classifier for lithology recognition of well logs. The candidate classifiers include eight conventional ML methods, such as logistic regression (LR), kNNs, SVMs, back propagation (BP) neural network, random forest (RF), extreme gradient boosting (XGB), light gradient boosting machine (LGBM), and CatBoost. It should be clarified that the BP classifier refers to a multilayer perceptron (MLP) trained by the backpropagation algorithm. Unless otherwise specified, all hyperparameters used in ML classifiers were set to the default values in scikit-learn 0.24.1.  

# III. EXPERIMENTAL DATA  

Clastic, carbonate, and volcanic reservoirs provide the vast majority of oil and gas accumulation space. Accurate lithology identification is a critical task for low-risk exploration and efficient development of oil and gas resources. This study focuses on these three reservoir types from three gas fields. The complete logging data are obtained and organized into three experimental datasets for in-depth case studies: 1) the Daniudi gas field (DGF) dataset was gathered from the DGF, located in the clastic reservoir of the Ordos Basin; 2) the Sulige gas field (SGF) dataset was collected from the SGF, situated in the carbonate reservoir of the Ordos Basin; and 3) the Xushen gas field (XGF) dataset was obtained from the XGF, located in the volcanic reservoir of the Songliao Basin.  

Each dataset represents the typical lithology and its corresponding logging responses. However, accurate lithology identification faces significant constraints due to the complex geological background and diverse sedimentary and diagenetic processes. Thus, it is essential to understand the geological setting in logging data processing and model evaluation. A detailed geological background and sampling information are introduced as follows.  

# A. Geological Background  

1) DGF: The Clastic Reservoir in Ordos Basin: The clastic reservoir of the DGF is located in the eastern part of the Yishan Slope of the Ordos Basin. According to [42], the Upper Paleozoic reservoirs were formed in a fluvial-deltaic depositional environment and had relatively low porosity and permeability. The Carboniferous Taiyuan, Permian Shanxi, and Lower Xiashihezi Formations are the main gas-bearing formations.  

The logging data and core analysis indicate that the reservoir in the DGF is mainly composed of clastic lithologies, including sandstone, mudstone, coal, and carbonate, containing a small amount of microcrystalline and micrite [18]. Research has demonstrated the potential for gas accumulation in the clastic reservoir of the upper Paleozoic strata in the Ordos Basin [43]. Accurate lithology identification and detailed analysis are crucial for understanding the sedimentary environment in the geological period, the petrophysical properties of the current reservoir, and the reservoir evaluation for gas reservoir development [16].  

2) SGF: The Carbonate Reservoir in Ordos Basin: The carbonate reservoir of the SGF is positioned in Wushenqi,  

TABLE I STATISTICAL INDEXES ABOUT LOGGING PARAMETERS COLLECTED FROM THE EXPERIMENTAL DATASETS, INCLUDING MEAN VALUE (MEAN), STANDARD DEVIATION, MINIMUM VALUE (MIN), QUANTILE AT $2 5 \%$ $( 2 5 \% )$ , QUANTILE AT $50 \%$ $( 5 0 \% )$ , QUANTILE AT $7 5 \%$ $( 7 5 \% )$ , AND MAXIMUM VALUE (MAX)   


<html><body><table><tr><td rowspan="2">Dataset</td><td rowspan="2">Statistical index</td><td colspan="7">Logging parameters</td></tr><tr><td>GR (API)</td><td>CNL (%)</td><td>AC (μs/m)</td><td>DEN (g/cm³)</td><td>LLD (S2·m)</td><td>LLS (S·m)</td><td>CAL (cm)</td></tr><tr><td rowspan="7">DGF</td><td>Mean</td><td>109.58</td><td>20.63</td><td>229.90</td><td>2.48</td><td>2127.68</td><td>611.46</td><td>24.23</td></tr><tr><td>Std</td><td>55.05</td><td>14.70</td><td>54.74</td><td>0.32</td><td>11982.66</td><td>2719.43</td><td>2.49</td></tr><tr><td>Min</td><td>24.27</td><td>0.40</td><td>159.00</td><td>1.21</td><td>11.37</td><td>10.83</td><td>21.39</td></tr><tr><td>25%</td><td>77.96</td><td>10.85</td><td>204.36</td><td>2.51</td><td>49.06</td><td>45.51</td><td>22.71</td></tr><tr><td>50%</td><td>95.06</td><td>15.82</td><td>213.79</td><td>2.58</td><td>71.39</td><td>69.60</td><td>23.45</td></tr><tr><td>75%</td><td>133.06</td><td>24.36</td><td>228.12</td><td>2.63</td><td>106.96</td><td>101.68</td><td>25.02</td></tr><tr><td>Max</td><td>771.34</td><td>92.77</td><td>608.60</td><td>2.97</td><td>99990.00</td><td>26057.18</td><td>44.78</td></tr><tr><td rowspan="7">SGF</td><td>Mean</td><td>31.50</td><td>6.69</td><td>162.73</td><td>2.82</td><td>11554.89</td><td>2114.52</td><td>24.51</td></tr><tr><td>Std</td><td>21.85</td><td>2.88</td><td>8.64</td><td>0.06</td><td>13323.02</td><td>2147.13</td><td>0.33</td></tr><tr><td>Min</td><td>9.30</td><td>0.03</td><td>146.43</td><td>2.39</td><td>16.03</td><td>18.20</td><td>23.89</td></tr><tr><td>25%</td><td>17.48</td><td>5.07</td><td>156.21</td><td>2.79</td><td>601.42</td><td>375.48</td><td>24.32</td></tr><tr><td>50%</td><td>22.42</td><td>6.70</td><td>160.49</td><td>2.83</td><td>5142.99</td><td>1338.50</td><td>24.43</td></tr><tr><td>75%</td><td>37.46</td><td>8.32</td><td>166.60</td><td>2.86</td><td>19919.28</td><td>3302.52</td><td>24.58</td></tr><tr><td>Max</td><td>179.71</td><td>19.69</td><td>215.53</td><td>2.98</td><td>40000.00</td><td>14816.15</td><td>27.48</td></tr><tr><td rowspan="8">XGF</td><td>Mean</td><td>155.73</td><td>3.97</td><td>188.24</td><td>2.47</td><td>16393.40</td><td>2267.61</td><td>22.56</td></tr><tr><td>Std</td><td>24.27</td><td>4.50</td><td>20.82</td><td>0.18</td><td>32213.24</td><td>5501.05</td><td>2.48</td></tr><tr><td>Min</td><td>77.15</td><td>0.01</td><td>148.85</td><td>1.13</td><td>17.86</td><td>12.82</td><td>21.11</td></tr><tr><td>25%</td><td>142.03</td><td>0.36</td><td>171.66</td><td>2.46</td><td>131.36</td><td>113.79</td><td>21.91</td></tr><tr><td>50%</td><td>150.46</td><td>2.72</td><td>185.38</td><td>2.53</td><td>428.76</td><td>316.67</td><td>22.13</td></tr><tr><td>75%</td><td>161.33</td><td>5.69</td><td>193.83</td><td>2.56</td><td>12274.67</td><td>2606.67</td><td>22.39</td></tr><tr><td>Max</td><td>261.24</td><td>18.41</td><td>262.97</td><td>2.69</td><td>104989.49</td><td>92130.85</td><td>45.32</td></tr></table></body></html>  

Inner Mongolia, close to Hengshan County to the east and Jingbian County to the south [38]. The geological structure unit is located in the northern part of the Yishan Slope of the Ordos Basin. Notably, the Ma 5 Member in the Majiagou Formation constitutes an important gas-bearing stratigraphic unit [39], [40].  

The lithologies of Ma 5 Member are primarily limestone, dolomite, dolomitic limestone, calcite dolomite, argillaceous dolomite, argillaceous limestone, gypsum, and mudstone, according to a thorough analysis of cores samples and well logs [39], [41]. The complexity of the lithologic composition in this study area brings difficulty to lithology identification.  

3) XGF: The Volcanic Reservoir in Songliao Basin: The Songliao Basin, situated in northeastern China, is a middle Cenozoic sedimentary basin. The Xujiaweizi Depression is located in the central part of the Songliao Basin [32]. Controlled by the Xuxi, Xuzhong, and Xudong Faults, the depression mainly consists of four sags and one middle uplift, among which intense volcanic activity occurred along the Xuzhong and Xudong Faults and developed thick volcanic rocks in the Yingcheng Formation [33].  

The XGF is located in the Xingcheng and Fengle areas of the Xujiaweizi Depression. The main target volcanic reservoirs are distributed in the Lower Cretaceous Yingcheng Formation [34]. The Yingcheng Formation is divided into four members by scholars [35], [36], which showcases the interbedding of two members of volcanic rocks and two members of clastic rocks. The volcanic rocks are mainly concentrated in Member 1 and Member 3 of the Yingcheng Formation.  

Member 1 is dominated by acidic volcanic rocks, which are widely distributed and primarily contain rhyolite and dacite. Member 3 is composed of neutral volcanic andesite, basal volcanic basalt, and volcanic clastic rocks.  

The formation and evolution of volcanic reservoirs in Songliao Basin are highly complicated [37]. The primary target reservoir, Member 1 of the Yingcheng Formation, exhibits variability in lithology, characterized by rapid changes and an uncertain distribution. In addition, volcanic strata often develop sedimentary rock interlayers, leading to significant challenges and uncertainties in distinguishing volcanic lithologies.  

# B. Data Preparation  

The three well log datasets from DGF, SGF, and XGF [18] contain 915 readings from six wells, 2881 readings from four wells, and 3534 readings from five wells, respectively. A total of seven logging parameters are obtained, including gamma ray log (GR), compensated neutron log (CNL), acoustic log (AC), density log (DEN), deep latero log (LLD), shallow latero log (LLS), and caliper log (CAL). The statistical summary of datasets is shown in Table I. Since the CAL refers to the borehole diameter during drilling, it is often used for engineering design and wellbore stability considerations. Therefore, we choose six more sensitive logging parameters, such as GR, CNL, AC, DEN, LLD, and LLS, as input to participate in lithology identification.  

Table II shows the distribution of lithology categories in the three experimental datasets. The lithologic category is determined by the drilling cuttings returned from the annulus. It can be seen that the well log datasets are typical imbalanced datasets. We select one well from each of the three datasets to showcase the logging curves and lithologic association characteristics, as depicted in Fig. 4.  

TABLE II LITHOLOGY DISTRIBUTION STATISTICS FOR EXPERIMENTAL WELL LOG DATASETS   


<html><body><table><tr><td rowspan="2"></td><td colspan="3">DGF</td><td colspan="3">SGF</td><td colspan="3">XGF</td></tr><tr><td>Category Lithology</td><td>Samples</td><td>Proportion</td><td>Lithology</td><td>Samples</td><td>Proportion</td><td>Lithology</td><td>Samples</td><td>Proportion</td></tr><tr><td>C1</td><td>Coal</td><td>104</td><td>11.37%</td><td>Limestone</td><td>358</td><td>12.43%</td><td>Siltstone</td><td>1926</td><td>54.50%</td></tr><tr><td>C2</td><td>Carbonate rock</td><td>48</td><td>5.25%</td><td>Dolomitic limestone</td><td>122</td><td>4.23%</td><td>Conglomerate</td><td>572</td><td>16.19%</td></tr><tr><td>C3</td><td>Coarse sandstone</td><td>114</td><td>12.46%</td><td>Argillaceous limestone</td><td>386</td><td>13.40%</td><td>Rhyolite</td><td>835</td><td>23.63%</td></tr><tr><td>C4</td><td>Fine sandstone</td><td>132</td><td>14.43%</td><td>Dolomite</td><td>383</td><td>13.29%</td><td>Basalt</td><td>103</td><td>2.91%</td></tr><tr><td>C5</td><td>Mudstone</td><td>133</td><td>14.54%</td><td>Calcite dolomite</td><td>814</td><td>28.25%</td><td>Dacite</td><td>98</td><td>2.77%</td></tr><tr><td>C6</td><td>Medium sandstone</td><td>211</td><td>23.06%</td><td>Argillaceous dolomite</td><td>818</td><td>28.39%</td><td></td><td></td><td></td></tr><tr><td>C7</td><td>Pebbly sandstone</td><td>120</td><td>13.11%</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>C8</td><td>Siltstone</td><td>53</td><td>5.79%</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td></td><td>Total</td><td>915</td><td></td><td>Total</td><td>2881</td><td></td><td>Total</td><td>3534</td><td></td></tr></table></body></html>  

![](images/68196e56748b9d89963d19c307eed7018fc014a8651a043c0343e4dcaa669630.jpg)  
Fig. 4. Lithologic associations and logging curves of three wells in experimental datasets. Caliper (CAL) refers to the diameter of the borehole after drilling which has a certain correlation with other logging curves, it is only used as auxiliary information and does not participate in this lithology identification task (a) Well D17 in the DGF dataset. (b) Well J1 in the SGF dataset. (c) Well X8 in the XGF dataset.  

![](images/1fea4ae3917feb3454c57f2324cea2351154263ed70c72a97d1a5173b1fd4d26.jpg)  
Fig. 5. Pairwise scatterplots and grouped histograms of experimental datasets. (a) DGF dataset. (b) SGF dataset. (c) XGF dataset.  

As widely recognized, the lithology identification task is a multiclassification issue. In Fig. 5, pairwise scatterplots of six logging parameters by lithologies reveal significant overlaps among different lithologies (shown in various colors) in each subplot. This observation suggests that traditional statistical methods may not effectively classify them owing to intricate nonlinear relationships between logging parameters and categories. Hence, a data-driven and model-free method is anticipated to automatically extract features from multivariable datasets.  

TABLE III TUNING PARAMETER FOR EACH FILTER AND OPTIMAL PARAMETER SETTING   


<html><body><table><tr><td rowspan="2">Filter</td><td rowspan="2">Tuning parameter</td><td rowspan="2">Search range</td><td colspan="3">Optimal parameter value</td></tr><tr><td>DGF</td><td>SGF</td><td>XGF</td></tr><tr><td>Gaussian</td><td>Standard deviation of Gaussian kernel (std)</td><td>0.1 - 0.9</td><td>0.5</td><td>0.2</td><td>0.1</td></tr><tr><td>Gabor</td><td>The center frequency (frequency)</td><td>1.5 - 5.0</td><td>2.5</td><td>5.0</td><td>3.0</td></tr><tr><td>Wavelet</td><td>The order of dbN wavelet (order)</td><td>1-10</td><td>1</td><td>1</td><td>1</td></tr><tr><td>CSC</td><td>The filter size (size)</td><td>23 - 39</td><td>35</td><td>31</td><td>33</td></tr></table></body></html>  

# C. Data Preprocessing  

Differences in the acquisition principles for measuring various logging parameters result in inconsistencies in their magnitudes. Standardizing the data the following equation is necessary to mitigate prediction errors caused by magnitude variations and provide standardized input samples for subsequent experimental analysis  

$$
x ^ { \prime } = \frac { x - x _ { \mathrm { m e a n } } } { x _ { \mathrm { s t d } } }
$$  

where $x$ and $x ^ { \prime }$ are the actual and standardized values, and $x _ { \mathrm { m e a n } }$ and $x _ { \mathrm { s t d } }$ are the mean and standard deviation (Std) values. In the case study, each category of the experimental dataset is randomly divided into the training set and the test set according to the $30 \% / 7 0 \%$ criterion.  

# IV. EXPERIMENTAL RESULTS AND DISCUSSION  

# A. Experimental Setup  

The experiments are implemented by Python 3.10 on a workstation with Inter Core i7-11700K CPU, 128G RAM, and an NVIDIA GeForce RTX3090 24 GB GPU. For the CSC filter, the patch size $\omega$ is set to 20, and the region $q$ to employ a 1-D convolution operator is set to 40.  

To assess the effectiveness of different methods, evaluation indicators such as overall accuracy (OA), average accuracy (AA), and Kappa coefficient $( \kappa )$ are employed. OA reflects the proportion of correctly identified samples. AA represents the average recall for each category, indicating the proportion of samples correctly classified in a specific category [44]. $\kappa$ is a statistic that measures the agreement between two raters or classifiers, accounting for the possibility of agreement occurring by chance [45].  

# B. Determination of Optimal Parameters  

To further elaborate on the benefits of the CSC filter, we compare it with three classic filters: the Gaussian filter [46], the Gabor filter [47], and the Daubechies wavelet (dbN) [48]. Before filtering, parameter tuning is crucial to establish the ideal parameter configurations for these filters, which utilizes a performance indicator to rank the feature extraction ability of filters with varied parameters and then searches for the optimal parameter value. In the tuning process, OA is employed as an indicator to measure classification performance, and grid search is utilized to find the optimal parameters through the specified search range.  

The key parameters that need tuning are listed in Table III for comparative filters. Fig. 6 illustrates the validation curves of ML classifiers based on features extracted by different filters. As evident, appropriate parameter setting is essential to improve classification performance. We recognize that different ML classifiers have their applicable parameter setting, which can extract effective features. For convenience, the parameter value when the highest OA appears in each subfigure is used as the optimal setting for each filter, as shown in Table III.  

# C. Comparison and Evaluation  

1) Compared With Traditional Filters: The core of the CSCFB model depends on the construction of CSCFBs and the layer-wise filtering manner to extract features. To ensure a fair comparison of performance, we exclusively substitute the filters in the CSCFB model while keeping other parameters consistently configured. For instance, the patch size of the comparative filter is consistent with that of the CSC filter, and the filtering process is employed independently for each logging parameter.  

The comparisons on well log datasets using different filters are reported in Table IV. Here, the statistical classifier KNN, network learning classifier BP, and ensemble learning classifiers RF and CatBoost, are chosen for display. In most cases, both CSC and Gaussian filters demonstrate superior accuracy. However, it is noteworthy that the traditional filters are primarily designed rooted in model assumptions and lack adjustment capabilities with changes in data. Moreover, parameter settings often rely on practical needs and experience. Actually, selecting the most suitable combination of parameters poses a challenge, considering our knowledge of diverse data sources, and the complex task of parameter optimization undoubtedly increases the workload.  

In contrast, the CSC filter is data-driven rather than model-driven. It enables the incorporation of labeling information through category-wise filter construction to extract the category-based discriminant features. Taking the XGF dataset as an example, Fig. 7 displays the visualization of CSCFBs constructed of different logging parameters in well  

![](images/cf3a5785137a4153fbb3da22082f5c95e119f21bdde888adcdde6a9a876676a1.jpg)  
Fig. 6. Validation curves for key parameters of different filters on well log datasets using ML classifiers. (a) DGF dataset. (b) SGF dataset. (c) XGF dataset.  

TABLE IVCOMPARISON OF OA VALUES $( \% )$ USING DIFFERENT FILTERS ON WELL LOG DATASETS, THE OPTIMAL RESULTS ARE IN BOLD  


<html><body><table><tr><td rowspan="2">Filter</td><td colspan="4">DGF</td><td colspan="4">SGF</td><td colspan="4">XGF</td></tr><tr><td>kNN</td><td>BP</td><td>RF</td><td>CatBoost</td><td>kNN</td><td>BP</td><td>RF</td><td>CatBoost</td><td>kNN</td><td>BP</td><td>RF</td><td>CatBoost</td></tr><tr><td>Gaussian</td><td>63.86</td><td>82.83</td><td>83.28</td><td>86.20</td><td>73.38</td><td>88.02</td><td>91.01</td><td>95.14</td><td>94.68</td><td>98.19</td><td>98.11</td><td>98.53</td></tr><tr><td>Gabor</td><td>60.49</td><td>77.78</td><td>82.49</td><td>81.82</td><td>68.21</td><td>84.28</td><td>88.13</td><td>91.32</td><td>93.07</td><td>98.28</td><td>96.83</td><td>97.23</td></tr><tr><td>Wavelet</td><td>62.81</td><td>76.07</td><td>81.46</td><td>83.60</td><td>62.81</td><td>78.02</td><td>78.65</td><td>82.99</td><td>90.95</td><td>95.50</td><td>97.99</td><td>98.70</td></tr><tr><td>CSC</td><td>75.60</td><td>83.65</td><td>86.83</td><td>86.27</td><td>71.83</td><td>80.45</td><td>95.86</td><td>95.44</td><td>98.04</td><td>98.27</td><td>98.50</td><td>99.15</td></tr></table></body></html>  

X8. Each lithology exhibits unique geological characteristics, and the distribution disparities among different categories are notably pronounced. Specifically, the filters constructed by the LLD and LLS logs in $C l { - } C 4$ are similar. However, there are significant differences between filters in C5 (Dacite). The reason can be found in Fig. 4(a). In the $3 9 6 0 { - } 3 9 7 0 \mathrm { ~ m ~ }$ depth interval with lithology $C 5$ , the distribution of the LLD and LLS logs varies, while the trend of them is roughly the same for intervals with other lithology types. This difference is reflected in the constructed filters, which further demonstrates that the data-driven CSC filter can effectively express the structural variations of logging parameters with different categories.  

The visualization results show that the CSC filter excels in capturing and emphasizing the distinctive sequence structural features related to categories. Therefore, the CSCFB model enhances feature diversity with different categories by employing a series of CSCFBs as 1-D convolution operators. This augmentation increases the sensitivity of the model in extracting subtle differences among various lithologies, ultimately capturing more discriminative features and improving the classification performance.  

As shown in Tables V–VII, we report the accuracy of each category in detail when employing the CSCFB model with various classifiers. Quantitative indicators, including OA, AA, and $\kappa$ , are utilized to analyze experimental results, where higher values for these indicators signify better classification performance. It is noteworthy that, for the DGF, SGF, and XGF datasets, when employing the CatBoost, XGB, and RF classifiers, respectively, the three indicators simultaneously achieve their highest values. In addition, even for the lithologies with fewer labeled samples, satisfactory results are obtained with the CSCFB model. Examples include C8 (Siltstone) in the DGF dataset, C2 (Dolomitic limestone) in the SGF dataset, C2 (Carbonate rock), and C5 (Dacite) in the XGF dataset. Remarkably, their accuracies are comparable to those of other categories. These results demonstrate that the novel CSC filter is well-suited for extracting meaningful sequence features from well log data.  

![](images/787c5f9784b2695bc07caf5d3208b362a14b1bb0c2e96db01bc843442c224f21.jpg)  
Fig. 7. Visualization of CSCFBs with respect to different logging parameters in Well X8, the XGF dataset. (a) GR filter bank. (b) CNL filter bank. (c) AC filter bank. (d) DEN filter bank. (e) LLD filter bank. (f) LLS filter bank. (g) Color bar.  

TABLE V CLASSIFICATION RESULTS OF CSCFB MODEL WITH DIFFERENT CLASSIFIERS ON THE DGF DATASET, THE OPTIMAL RESULTS ARE IN BOLD   


<html><body><table><tr><td>Category</td><td>LR</td><td>kNN</td><td>SVM</td><td>BP</td><td>RF</td><td>XGB</td><td>LGBM</td><td>CatBoost</td></tr><tr><td>C1</td><td>93.27</td><td>87.50</td><td>89.42</td><td>96.15</td><td>93.27</td><td>90.38</td><td>94.23</td><td>97.12</td></tr><tr><td>C2</td><td>85.42</td><td>81.25</td><td>31.25</td><td>77.08</td><td>91.67</td><td>81.25</td><td>81.25</td><td>79.17</td></tr><tr><td>C3</td><td>48.60</td><td>56.07</td><td>41.12</td><td>57.94</td><td>66.36</td><td>67.29</td><td>64.49</td><td>63.55</td></tr><tr><td>C4</td><td>77.60</td><td>80.80</td><td>70.40</td><td>84.80</td><td>83.20</td><td>84.00</td><td>85.60</td><td>85.60</td></tr><tr><td>C5</td><td>75.19</td><td>86.47</td><td>86.47</td><td>89.47</td><td>94.74</td><td>93.23</td><td>93.98</td><td>93.98</td></tr><tr><td>C6</td><td>69.61</td><td>76.47</td><td>78.92</td><td>80.88</td><td>88.24</td><td>85.29</td><td>85.78</td><td>85.29</td></tr><tr><td>C7</td><td>54.55</td><td>63.64</td><td>7.27</td><td>93.64</td><td>90.91</td><td>89.09</td><td>88.18</td><td>93.64</td></tr><tr><td>C8</td><td>90.00</td><td>68.00</td><td>26.00</td><td>90.00</td><td>86.00</td><td>84.00</td><td>90.00</td><td>88.00</td></tr><tr><td>OA (%)</td><td>71.96</td><td>75.60</td><td>60.95</td><td>83.65</td><td>86.83</td><td>84.90</td><td>85.70</td><td>86.27</td></tr><tr><td>AA (%)</td><td>74.28</td><td>75.02</td><td>53.86</td><td>83.75</td><td>86.80</td><td>84.32</td><td>85.44</td><td>85.79</td></tr><tr><td>K ×100</td><td>67.27</td><td>71.32</td><td>52.96</td><td>80.83</td><td>84.53</td><td>82.28</td><td>83.21</td><td>83.88</td></tr></table></body></html>  

TABLE VI CLASSIFICATION RESULTS OF CSCFB MODEL WITH DIFFERENT CLASSIFIERS ON THE SGF DATASET, THE OPTIMAL RESULTS ARE IN BOLD   


<html><body><table><tr><td>Category</td><td>LR</td><td>kNN</td><td>SVM</td><td>BP</td><td>RF</td><td>XGB</td><td>LGBM</td><td>CatBoost</td></tr><tr><td>C1</td><td>89.36</td><td>81.79</td><td>87.96</td><td>96.07</td><td>96.08</td><td>96.64</td><td>96.08</td><td>95.52</td></tr><tr><td>C2</td><td>57.02</td><td>47.93</td><td>33.88</td><td>80.16</td><td>85.12</td><td>85.95</td><td>82.64</td><td>85.12</td></tr><tr><td>C3</td><td>83.90</td><td>63.64</td><td>56.36</td><td>93.76</td><td>95.58</td><td>95.32</td><td>94.81</td><td>94.29</td></tr><tr><td>C4</td><td>79.84</td><td>76.44</td><td>73.56</td><td>92.67</td><td>96.86</td><td>95.55</td><td>95.81</td><td>94.76</td></tr><tr><td>C5</td><td>80.69</td><td>71.09</td><td>69.62</td><td>93.17</td><td>95.69</td><td>95.57</td><td>95.33</td><td>94.71</td></tr><tr><td>C6</td><td>80.05</td><td>73.44</td><td>77.97</td><td>93.64</td><td>97.18</td><td>98.29</td><td>98.65</td><td>98.53</td></tr><tr><td>OA (%)</td><td>80.90</td><td>71.83</td><td>71.51</td><td>93.13</td><td>95.86</td><td>96.03</td><td>95.83</td><td>95.44</td></tr><tr><td>AA (%)</td><td>78.48</td><td>69.06</td><td>66.56</td><td>91.58</td><td>94.42</td><td>94.55</td><td>93.89</td><td>93.82</td></tr><tr><td>K ×100</td><td>75.87</td><td>64.22</td><td>63.71</td><td>91.26</td><td>94.74</td><td>94.96</td><td>94.69</td><td>94.21</td></tr></table></body></html>  

TABLE VII CLASSIFICATION RESULTS OF CSCFB MODEL WITH DIFFERENT CLASSIFIERS ON THE XGF DATASET, THE OPTIMAL RESULTS ARE IN BOLD   


<html><body><table><tr><td>Category</td><td>LR</td><td>kNN</td><td>SVM</td><td>BP</td><td>RF</td><td>XGB</td><td>LGBM</td><td>CatBoost</td></tr><tr><td>C1</td><td>94.03</td><td>98.60</td><td>97.92</td><td>99.38</td><td>98.70</td><td>98.29</td><td>99.17</td><td>99.37</td></tr><tr><td>C2</td><td>98.95</td><td>98.95</td><td>91.77</td><td>99.47</td><td>99.47</td><td>98.77</td><td>99.82</td><td>99.82</td></tr><tr><td>C3</td><td>86.21</td><td>96.04</td><td>81.41</td><td>98.20</td><td>97.72</td><td>97.48</td><td>98.08</td><td>100.0</td></tr><tr><td>C4</td><td>92.16</td><td>97.06</td><td>65.69</td><td>100.0</td><td>94.12</td><td>100.0</td><td>100.0</td><td>99.01</td></tr><tr><td>C5</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td><td>100.0</td></tr><tr><td>OA (%)</td><td>93.09</td><td>98.04</td><td>92.15</td><td>99.15</td><td>98.50</td><td>98.27</td><td>98.95</td><td>99.60</td></tr><tr><td>AA (%)</td><td>94.27</td><td>98.13</td><td>87.36</td><td>99.41</td><td>98.00</td><td>98.91</td><td>98.95</td><td>99.64</td></tr><tr><td>K ×100</td><td>88.85</td><td>96.85</td><td>87.05</td><td>98.63</td><td>97.58</td><td>97.22</td><td>98.31</td><td>99.36</td></tr></table></body></html>  

![](images/866c895ae04b01b8a8d1baecb98c7bb3a2adb912b033da4cb9886372fbfe2f5e.jpg)  
Fig. 8. Visualization of identification results on three wells from experimental datasets. (a) Well D17 in the DGF dataset. (b) Well J1 in the SGF dataset. (c) Well X8 in the XGF dataset.  

Fig. 8 visually displays the log interpretation results from three wells. In each subfigure, panels 2–4 represent the conventional well logs, panel 5 shows lithological labels obtained from core data, panels 6–8 exhibit classified results obtained from features extracted by traditional filters with the CatBoost classifier, and the last panel showcases the identification results derived from the CSC filter with CatBoost classifier. An evident observation is the more misclassified layers of the comparison filters compared to the CSC filter. Notably, Fig. 8 shows that the CSCFB model successfully identifies lithology distributions in the following depth intervals: the entire interval of well D17 [see Fig. 8(a)], which achieves almost complete accuracy, $3 4 8 0 { - } 3 5 2 0 \ \mathrm { m }$ in well J1 [see Fig. 8(b)], and $3 9 5 5 { - } 3 9 9 5 \mathrm { ~ m ~ }$ in well X8 [see Fig. 8(c)]. Furthermore, compared to other methods, the CSCFB model reduces identification errors of rhyolite and siltstone in the $3 6 5 5 { - } 3 6 9 5 \mathrm { ~ m ~ }$ interval. Theoretically, incorporating labeling information enables the creation of diverse CSCFBs, which rely on intrinsic attributes of the training data, characterizing the sequence dependencies without involving any learning algorithm. Thus, the CSC filter demonstrates enhanced applicability and practicality for various reservoir types compared to traditional filters with fixed patterns.  

TABLE VIII IDENTIFICATION PERFORMANCE OF VARIOUS FEATURE EXTRACTION STRATEGIES, THE OPTIMAL RESULTS ON WELL LOG DATASETS ARE IN BOL   


<html><body><table><tr><td rowspan="2">Category</td><td colspan="7">DGF</td><td colspan="7">SGF</td><td colspan="7">XGF</td></tr><tr><td></td><td>ANN CSCFB CNN</td><td></td><td></td><td>ViT MLP-Mixer RNN LSTM ANN CSCFB CNN</td><td></td><td></td><td></td><td></td><td></td><td>ViT</td><td>MLP-Mixer</td><td>RNN LSTM ANN CSCFB CNN</td><td></td><td></td><td></td><td></td><td>ViT</td><td>MLP-Mixer RNN LSTM</td><td></td><td></td></tr><tr><td>C1</td><td>93.26</td><td>93.27</td><td>94.23 90.38</td><td></td><td>92.31</td><td>88.4690.38 95.23</td><td></td><td></td><td>96.64</td><td>96.36 95.80</td><td></td><td>93.00</td><td>92.1694.12 98.70</td><td></td><td></td><td>99.37</td><td>99.27 99.32</td><td></td><td>99.58</td><td>99.8499.79</td><td></td></tr><tr><td>C2</td><td>58.33</td><td>91.67</td><td>75.00 87.50</td><td></td><td>79.17</td><td>81.2583.3371.07</td><td></td><td></td><td>85.95</td><td>68.60 74.38</td><td></td><td>66.94</td><td>58.6860.33</td><td></td><td>97.19</td><td>99.82</td><td></td><td>99.47 99.12</td><td>99.47</td><td></td><td>97.9099.12</td></tr><tr><td>C3</td><td>46.36</td><td>66.36</td><td>64.5553.64</td><td></td><td>65.45</td><td>61.82 55.45 86.75</td><td></td><td></td><td>95.32</td><td>89.09 74.55</td><td></td><td>87.27</td><td>82.8680.78 96.30</td><td></td><td></td><td>100.0</td><td></td><td>99.76 97.00</td><td>97.12</td><td>91.3797.60</td><td></td></tr><tr><td>C4</td><td>83.20</td><td>83.20</td><td>88.5587.02</td><td></td><td>90.84</td><td>87.7990.0891.88</td><td></td><td></td><td>95.55</td><td>95.03 92.93</td><td></td><td>93.19</td><td>89.7987.96</td><td></td><td>97.05</td><td>99.01</td><td>99.02 100.0</td><td></td><td>100.0</td><td>85.29</td><td>100.0</td></tr><tr><td>C5</td><td>89.47</td><td>94.74</td><td>80.45 82.71</td><td></td><td>75.94</td><td>68.4283.46 88.90</td><td></td><td></td><td>95.57</td><td>90.38 85.14</td><td></td><td>87.09</td><td>88.19</td><td>85.02</td><td>98.96</td><td>100.0</td><td></td><td>100.0 100.0</td><td>98.97</td><td>100.0</td><td>100.0</td></tr><tr><td>C6</td><td>88.88</td><td>88.24</td><td>75.36 83.09</td><td></td><td>71.98</td><td>79.2381.1686.79</td><td></td><td></td><td>98.29</td><td>94.02 85.59</td><td></td><td>89.50</td><td>89.26 84.62</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>C7</td><td>74.99</td><td>90.91</td><td>58.93 58.04</td><td></td><td>53.57</td><td>58.0465.18</td><td></td><td></td><td>1 1</td><td></td><td></td><td></td><td></td><td></td><td>-</td><td></td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>C8</td><td>51.99</td><td>86.00</td><td>68.00 50.00</td><td></td><td>60.00</td><td>48.0070.00</td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td></td><td>1</td><td>-</td><td>1</td><td>1</td><td></td><td></td></tr><tr><td>OA (%)</td><td>77.98</td><td>86.83</td><td>76.42 76.09</td><td></td><td>74.30</td><td>73.52</td><td>78.21</td><td>88.44</td><td>96.03</td><td>91.68 85.75</td><td></td><td>88.49</td><td>87.24 84.82 97.85</td><td></td><td></td><td>99.60</td><td>99.38 98.78</td><td></td><td>98.98</td><td>97.1199.18</td><td></td></tr><tr><td>AA (%)</td><td>73.31</td><td>86.80</td><td>75.63 74.05</td><td></td><td>73.66</td><td>71.6377.3886.77</td><td></td><td></td><td>94.55</td><td>88.91 84.73</td><td></td><td>86.17</td><td>83.49</td><td>82.14</td><td>97.64</td><td>99.64</td><td>99.11 99.09</td><td></td><td>99.03</td><td></td><td>94.88 99.30</td></tr><tr><td>K ×100</td><td>73.95</td><td>84.53</td><td>72.33 71.86</td><td></td><td>69.96</td><td>68.90</td><td>74.3585.34</td><td></td><td>94.96</td><td>90.72 81.94</td><td></td><td>86.64</td><td>84.85</td><td>82.95 96.53</td><td></td><td>99.36</td><td>99.28 98.85</td><td></td><td>99.07</td><td>98.02</td><td>99.17</td></tr><tr><td>Times (s) 111.0</td><td></td><td>92.00</td><td>444.0 542.0</td><td></td><td>574.0</td><td>398.0 470.0</td><td></td><td>97.00</td><td>75.00</td><td>387.0 403.0</td><td></td><td>676.0</td><td>380.0 392.0</td><td></td><td>75.00</td><td>60.00</td><td>297.0 570.0</td><td></td><td>728.0</td><td></td><td>420.0 433.0</td></tr></table></body></html>

\*Times denotes the training time of the model.  

2) Compared With Deep Learning-Based Feature Extraction Methods: To validate the effectiveness of the CSCFB model, six classic and popular feature extraction strategies are compared to explore the superiority of the CSC filter. The six advanced feature extraction methods include artificial neural network (ANN) [49], CNN, vision transformer (ViT) [50], MLP-Mixer [51], RNN, and LSTM. For convenience, we treat different feature extraction strategies as distinct models and do not define separate names.  

In this comparative section, CNN is the only feature extraction strategy directly comparable to our proposed CSCFB model. Convolution kernels, acting as filters, are usually randomly initialized and then iteratively updated in the network to extract abstract features. For the sake of fairness, we maintain consistency between the configuration of the CNN and CSCFB models. The customized CNN accepts all logging curves as different channels of the input. The key parameters of the CNN include the following: one grouped convolution layer consisting of $p \times C$ convolutional kernels, where $p$ represents the number of logging curves (i.e., input channel) and $C$ represents the number of classes. The convolution kernel size is consistent with the CSC filter size, i.e., $3 5 \times 1$ on the DGF dataset, $3 1 \times 1$ on the SGF dataset, and $3 3 \times 1$ on the XGF dataset, respectively. Alternative strategies, such as traditional ANN, emerging methods like ViT and MLP-Mixer, as well as sequence-based methods like RNN and LSTM, serve as baselines for comparison. All comparison methods share the following parameter settings: 1000 iterations and a dropout parameter set to 0.25. The optimal results from Tables V to VII are regarded as benchmarks for evaluating the proposed CSCFB in subsequent comparative experiments. Thus, the CSCFB model is implemented with CatBoost, XGB, and RF classifiers on the XGF, SGF, and DGF datasets, respectively.  

As shown in Table VIII, when compared to other methods, the CSCFB model improves the OA values by $8 . 6 2 \% - 1 3 . 3 1 \%$ , $4 . 3 5 \% - 1 1 . 2 1 \%$ , and $0 . 2 2 \% - 2 . 4 9 \%$ in the DGF, SGF, and XGF datasets, respectively. When contrasted with the convolution kernel in the CNN that utilizes learnable parameters, the CSC filter in the CSCFB model not only guarantees but also improves the feature extraction capability, which enhances the classification performance and improves the OA values by $10 . 4 1 \%$ , $4 . 3 5 \%$ , and $0 . 2 2 \%$ in three datasets, respectively. In addition, the CSC filter eliminates the need for iterative updating parameters, exhibiting superior feature extraction performance and computational cost.  

ViT and MLP-Mixer are popular mainstream architectures in deep learning, and RNN and LSTM represent classical sequence learning paradigms. Despite their established status, these methods exhibit somewhat underwhelming performance when applied to logging datasets from three types of reservoirs. In contrast, our model significantly outperforms them. This superiority is largely attributed to the nature of CSC filters, which enables them to comprehensively capture the inherent diversity and complexity of the data. As a result, the CSCFB model demonstrates enhanced adaptability to the sequential dependencies of logging curves and achieves more effective handling of complex logging datasets across diverse reservoirs.  

# V. CONCLUSION  

A novel CSC filter, which is data-driven and model-free, is proposed for lithology identification tasks using well logs. The CSC filter captures sequence structural variations in logging parameters resulting from category heterogeneity by integrating labeling information. On this basis, the CSCFBs model is developed. It combines neighborhood feature engineering with ML classifiers and utilizes a series of CSCFBs as 1-D convolution operators to extract discriminative features, which is conducive to improving model performance. The performance of the CSCFB model is evaluated on three logging datasets obtained from diverse reservoirs (i.e., clastic, carbonate, and volcanic reservoirs). Experimental results validate the feasibility and effectiveness of the proposed CSCFB model in terms of classification accuracy, demonstrating its applicability across different reservoir types. Furthermore, the features extracted by the CSC filter are more recognizable and representative of the categories. To broaden the application of the CSCFB model in practical geological fields, the optimization of the CSC filter, such as handling out-of-distribution datasets, will be explored in future work.  

# REFERENCES  

[1] N. Liu, T. Huang, J. Gao, Z. Xu, D. Wang, and F. Li, “Quantumenhanced deep learning-based lithology interpretation from well logs,” IEEE Trans. Geosci. Remote Sens., vol. 60, 2021, Art. no. 4503213.   
[2] Z. Liu, J. Zhang, Y. Li, G. Zhang, Y. Gu, and Z. Chu, “Lithology prediction of one-dimensional residual network based on regularization constraints,” J. Petroleum Sci. Eng., vol. 215, Aug. 2022, Art. no. 110620.   
[3] X. Liu, X. Chen, J. Li, X. Zhou, and Y. Chen, “Facies identification based on multikernel relevance vector machine,” IEEE Trans. Geosci. Remote Sens., vol. 58, no. 10, pp. 7269–7282, Oct. 2020.   
[4] J. Lin, H. Li, N. Liu, J. Gao, and Z. Li, “Automatic lithology identification by applying LSTM to logging data: A case study in x tight rock reservoirs,” IEEE Geosci. Remote Sens. Lett., vol. 18, no. 8, pp. 1361–1365, Aug. 2021.   
[5] S. Soltani, M. Kordestani, P. K. Aghaee, and M. Saif, “Improved estimation for well-logging problems based on fusion of four types of Kalman filters,” IEEE Trans. Geosci. Remote Sens., vol. 56, no. 2, pp. 647–654, Feb. 2018.   
[6] G. Lu et al., “Lithology identification using graph neural network in continental shale oil reservoirs: A case study in mahu sag, Junggar basin, Western China,” Mar. Petroleum Geol., vol. 150, Apr. 2023, Art. no. 106168.   
[7] Z. Song, D. Xiao, Y. Wei, R. Zhao, X. Wang, and J. Tang, “The research on complex lithology identification based on well logs: A case study of lower 1st member of the shahejie formation in raoyang sag,” Energies, vol. 16, no. 4, p. 1748, Feb. 2023.   
[8] S. Li, K. Zhou, L. Zhao, Q. Xu, and J. Liu, “An improved lithology identification approach based on representation enhancement by logging feature decomposition, selection and transformation,” J. Petroleum Sci. Eng., vol. 209, Feb. 2022, Art. no. 109842.   
[9] O. A. Anyiam, A. W. Mode, and E. S. Okara, “The use of cross-plots in lithology delineation and petrophysical evaluation of some wells in the western coastal swamp, Niger delta,” J. Petroleum Explor. Prod. Technol., vol. 8, no. 1, pp. 61–71, Mar. 2018.   
[10] B. Huang and B. Pan, “Characteristics of log responses and lithology determination of igneous rock reservoirs,” J. Geophys. Eng., vol. 1, no. 1, pp. 51–55, Mar. 2004.   
[11] P. Wang, X. Chen, B. Wang, J. Li, and H. Dai, “An improved method for lithology identification based on a hidden Markov model and random forests,” Geophysics, vol. 85, no. 6, pp. IM27–IM36, Nov. 2020.   
[12] T. Kumar, N. K. Seelam, and G. S. Rao, “Lithology prediction from well log data using machine learning techniques: A case study from talcher coalfield, eastern India,” J. Appl. Geophys., vol. 199, Apr. 2022, Art. no. 104605.   
[13] L. Qingfeng et al., “A comprehensive machine learning model for lithology identification while drilling,” Geoenergy Sci. Eng., vol. 231, Dec. 2023, Art. no. 212333.   
[14] Z. Liu, J. Cao, J. You, S. Chen, Y. Lu, and P. Zhou, “A lithological sequence classification method with well log via SVM-assisted bidirectional GRU-CRF neural network,” J. Petroleum Sci. Eng., vol. 205, Oct. 2021, Art. no. 108913.   
[15] A. Al-Anazi and I. D. Gates, “On the capability of support vector machines to classify lithology from well logs,” Natural Resour. Res., vol. 19, no. 2, pp. 125–139, Jun. 2010.   
[16] C. M. Saporetti, L. G. Da Fonseca, and E. Pereira, “A lithology identification approach based on machine learning with evolutionary parameter tuning,” IEEE Geosci. Remote Sens. Lett., vol. 16, no. 12, pp. 1819–1823, Dec. 2019.   
[17] J. R. Harris and E. C. Grunsky, “Predictive lithological mapping of Canada’s north using random forest classification applied to geophysical and geochemical data,” Comput. Geosci., vol. 80, pp. 9–25, Jul. 2015.   
[18] Y. Xie, C. Zhu, W. Zhou, Z. Li, X. Liu, and M. Tu, “Evaluation of machine learning methods for formation lithology identification: A comparison of tuning processes and model performances,” J. Petroleum Sci. Eng., vol. 160, pp. 182–193, Jan. 2018.   
[19] C. Jiang, D. Zhang, and S. Chen, “Lithology identification from welllog curves via neural networks with additional geologic constraint,” Geophysics, vol. 86, no. 5, pp. IM85–IM100, Sep. 2021.   
[20] J. Zhang, J. Li, X. Chen, Y. Li, and W. Tang, “A spatially coupled datadriven approach for Lithology/Fluid prediction,” IEEE Trans. Geosci. Remote Sens., vol. 59, no. 7, pp. 5526–5534, Jul. 2021.   
[21] D. T. dos Santos, M. Roisenberg, and M. dos S. Nascimento, “Deep recurrent neural networks approach to sedimentary facies classification using well logs,” IEEE Geosci. Remote Sens. Lett., vol. 19, pp. 1–5, 2022.   
[22] J. Wang and J. Cao, “A lithology identification approach using well logs data and convolutional long short-term memory networks,” IEEE Geosci. Remote Sens. Lett., vol. 20, pp. 1–5, 2023.   
[23] X. Zhang, J. Zhao, and C. Zhang, “Spatial correlation filter and its application in hyperspectral ground objects recognition,” Int. J. Remote Sens., vol. 42, no. 18, pp. 7053–7074, Sep. 2021.   
[24] H. Yang, H. Pan, H. Ma, A. A. Konaté, J. Yao, and B. Guo, “Performance of the synergetic wavelet transform and modified K-means clustering in lithology classification using nuclear log,” J. Petroleum Sci. Eng., vol. 144, pp. 1–9, Aug. 2016.   
[25] L. Sun, Z. Li, K. Li, H. Liu, G. Liu, and W. Lv, “Cross-well lithology identification based on wavelet transform and adversarial learning,” Energies, vol. 16, no. 3, p. 1475, Feb. 2023.   
[26] Y. Fu, S. Yan, and T. S. Huang, “Correlation metric for generalized feature extraction,” IEEE Trans. Pattern Anal. Mach. Intell., vol. 30, no. 12, pp. 2229–2235, Dec. 2008.   
[27] H. Gong, Y. Li, J. Zhang, B. Zhang, and X. Wang, “A new filter feature selection algorithm for classification task by ensembling Pearson correlation coefficient and mutual information,” Eng. Appl. Artif. Intell., vol. 131, May 2024, Art. no. 107865.   
[28] G. Li, A. Zhang, Q. Zhang, D. Wu, and C. Zhan, “Pearson correlation coefficient-based performance enhancement of broad learning system for stock price prediction,” IEEE Trans. Circuits Syst. II, Exp. Briefs, vol. 69, no. 5, pp. 2413–2417, May 2022.   
[29] M. A. Oliver and R. Webster, “A tutorial guide to geostatistics: Computing and modelling variograms and Kriging,” Catena, vol. 113, pp. 56–69, Feb. 2014.   
[30] S. Garrigues, D. Allard, F. Baret, and M. Weiss, “Quantifying spatial heterogeneity at the landscape scale using variogram models,” Remote Sens. Environ., vol. 103, no. 1, pp. 81–96, Jul. 2006.   
[31] B. Kongwung and S. Ronghe, “Reservoir identification and characterization through sequential horizon mapping and geostatistical analysis: A case study from the Gulf of Thailand,” Petroleum Geosci., vol. 6, no. 1, pp. 47–57, Mar. 2000.   
[32] J. Fu, X. Wei, J. Nan, and X. Shi, “Characteristics and origin of reservoirs of gas fields in the upper Paleozoic tight sandstone, Ordos basin,” J. Palaeogeography, vol. 15, no. 4, pp. 529–539, 2013.   
[33] H. Yang, J. Fu, X. Liu, and P. Meng, “Accumulation conditions and exploration and development of tight gas in the upper Paleozoic of the Ordos basin,” Petroleum Explor. Develop., vol. 39, no. 3, pp. 315–324, Jun. 2012.   
[34] Y. Zhang, H.-R. Zhong, Z.-Y. Wu, H. Zhou, and Q.-Y. Ma, “Improvement of petrophysical workflow for shear wave velocity prediction based on machine learning methods for complex carbonate reservoirs,” J. Petroleum Sci. Eng., vol. 192, Sep. 2020, Art. no. 107234.   
[35] X.-L. Bai, S.-N. Zhang, Q.-Y. Huang, X.-Q. Ding, and S.-Y. Zhang, “Origin of dolomite in the middle Ordovician peritidal platform carbonates in the northern Ordos basin, western China,” Petroleum Sci., vol. 13, no. 3, pp. 434–449, Aug. 2016.   
[36] Y. Zhang, C. Zhang, Q. Ma, X. Zhang, and H. Zhou, “Automatic prediction of shear wave velocity using convolutional neural networks for different reservoirs in Ordos basin,” J. Petroleum Sci. Eng., vol. 208, Jan. 2022, Art. no. 109252.   
[37] Y. Gu, Z. Bao, Y. Lin, Z. Qin, J. Lu, and H. Wang, “The porosity and permeability prediction methods for carbonate reservoirs with extremely limited logging data: Stepwise regression vs. n-way analysis of variance,” J. Natural Gas Sci. Eng., vol. 42, pp. 99–119, Jun. 2017.   
[38] C. Chen, “Volcanostratigraphic sequence and tectonic-volcanic-basin filling evolution in the faulted period of songliao basin-based on the comparative study of basin margin sections and xujiaweizi graben,” Ph.D. thesis, College Earth Sci., Jilin Univ., Changchun, China, 2016.   
[39] T. Nian, G. Wang, D. Cang, C. Tan, Y. Tan, and F. Zhang, “The diagnostic criteria of borehole electrical imaging log for volcanic reservoir interpretation: An example from the yingcheng formation in the xujiaweizi depression, songliao basin, China,” J. Petroleum Sci. Eng., vol. 208, Jan. 2022, Art. no. 109713.   
[40] H. Tang, X. Zhao, M. Shao, X. Sun, Y. Zhang, and P. Cryton, “Reservoir origin and characterization of gas pools in intrusive rocks of the yingcheng formation, songliao basin, NE China,” Mar. Petroleum Geol., vol. 84, pp. 148–159, Jun. 2017.   
[41] R.-H. Cheng, T.-F. Wang, Y.-J. Shen, and Y.-G. Ren, “Architecture of volcanic sequence and its structural control of yingcheng formation in songliao basin,” J. Central South Univ., vol. 21, no. 5, pp. 2026–2040, May 2014.   
[42] Y. Wang, R. Cheng, Y. Shen, Y. Fu, Z. Xu, and Y. Gao, “Volcanosedimentary fill of the early cretaceous yingcheng formation and response to the end of continental rifting in the songliao basin: Constraints from well SK-2, northern xujiaweizi fault depression,” Mar. Petroleum Geol., vol. 157, Nov. 2023, Art. no. 106493.   
[43] B. Pan et al., “Study on reservoir characteristics and evaluation methods of altered igneous reservoirs in songliao basin, China,” J. Petroleum Sci. Eng., vol. 212, May 2022, Art. no. 110266.   
[44] J. Shi, X. Zhao, L. Zeng, Y. Zhang, and S. Dong, “Identification of coal structures by semi-supervised learning based on limited labeled logging data,” Fuel, vol. 337, Apr. 2023, Art. no. 127191.   
[45] S. M. Vieira, U. Kaymak, and J. M. C. Sousa, “Cohen’s Kappa coefficient as a performance measure for feature selection,” in Proc. Int. Conf. Fuzzy Syst., Jul. 2010, pp. 1–8.   
[46] H. H. Afshari, S. A. Gadsden, and S. Habibi, “Gaussian filters for parameter and state estimation: A general review of theory and recent trends,” Signal Process., vol. 135, pp. 218–238, Jun. 2017.   
[47] J.-K. Kamarainen, V. Kyrki, and H. Kalviainen, “Invariance properties of Gabor filter-based features-overview and applications,” IEEE Trans. Image Process., vol. 15, no. 5, pp. 1088–1099, May 2006.   
[48] Q. Zhang et al., “A method for identifying the thin layer using the wavelet transform of density logging data,” J. Petroleum Sci. Eng., vol. 160, pp. 433–441, Jan. 2018.   
[49] J. Sietsma and R. J. F. Dow, “Creating artificial neural networks that generalize,” Neural Netw., vol. 4, no. 1, pp. 67–79, Jan. 1991.   
[50] A. Dosovitskiy et al., “An image is worth $1 6 \times 1 6$ words: Transformers for image recognition at scale,” 2020, arXiv:2010.11929.   
[51] I. O. Tolstikhin et al., “MLP-mixer: An all-MLP architecture for vision,” in Proc. Adv. Neural Inf. Process. Syst., vol. 34, M. Ranzato, A. Beygelzimer, Y. Dauphin, P. Liang, and J. W. Vaughan, Eds., Red Hook, NY, USA: Curran Associates, 2021, pp. 24261–24272. [Online]. Available: https://proceedings.neurips.cc/paper_files/paper/2021/file/cba0a4ee5ccd 02fda0fe3f9a3e7b89fe-Paper.pdf  

![](images/fc94a6430355d363fe7693ddb7f251aa9093a33719cf63520a42e3da16f87c93.jpg)  

Zitong Zhang (Member, IEEE) received the B.S. and M.S. degrees in mathematics and the Ph.D. degree in geophysical prospecting and information technology from China University of Geosciences at Beijing, Beijing, China, in 2017, 2020, and 2024, respectively.  

She is currently a Post-Doctoral Researcher with China University of Petroleum (Beijing), Beijing. Her research interests include deep learning and intelligent log interpretation.  

Xin Zhang received the B.S. and M.S. degrees from China University of Geosciences (Beijing), Beijing, China in 2018 and 2020, respectively, and the Ph.D. degree from the Department of Statistics, Beijing Normal University, Beijing, in 2023.  

![](images/69a210766201da893fcc908a402df88c41b29869471184eb9470b36350977309.jpg)  

She is currently a Lecturer with the Department of Statistics and Epidemiology, Graduate School of the PLA General Hospital, Beijing. Her research interests include data mining, deep learning, time series analysis, and statistics analysis.  

![](images/49c40f934f740cf131f957750527fe808f93203a11fab6dba4cac455b0888261.jpg)  

Chunlei Zhang received the M.Eng. degree from Taiyuan University of Technology, Taiyuan, China, in 1997, and the Ph.D. degree from China University of Petroleum (Beijing), Beijing, China, in 2000, all in geostatistics, reservoir characterization, and reservoir engineering.  

From 2000 to 2002, he was Post-Doctoral Researcher at China University of Petroleum at Beijing. Since 2002, he has been working in the energy industry. His research interests include geostatistics, pattern recognition, deep learning, and intelligent log interpretation.  

![](images/207c9a0881bc2bf3ed4da54e19751a05b2c6168e4b58d7c0f6ebf1af426fa7ac.jpg)  

Hanlin Feng (Graduate Student Member, IEEE) received the B.Eng. degree in Internet of Things engineering from North China Electric Power University, Beijing, China, in 2021. He is currently pursuing the M.Eng. degree in computer and information technology with Northeast Petroleum University, Daqing, China.  

His research interests include deep learning, time series analysis, and pattern recognition.  