<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .db-icon {
        stroke: #4A5568;
        /* A nice, modern gray for the lines */
        stroke-width: 4;
        fill: #E2E8F0;
        /* A light gray fill */
        stroke-linejoin: round;
        stroke-linecap: round;
      }

      .db-icon-top {
        fill: #F7FAFC;
        /* A slightly lighter fill for the top surface */
      }
    </style>
  </defs>

  <title>Database Icon</title>

  <!-- Base cylinder shape -->
  <path class="db-icon" d="M20,30 C20,15 80,15 80,30 V70 C80,85 20,85 20,70 Z" />

  <!-- Top ellipse to give it a 3D look -->
  <ellipse class="db-icon db-icon-top" cx="50" cy="30" rx="30" ry="12" />

  <!-- Middle decorative line -->
  <path class="db-icon" d="M20,50 C20,35 80,35 80,50" />
</svg>